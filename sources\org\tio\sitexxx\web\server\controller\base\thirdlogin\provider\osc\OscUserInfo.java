package org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.osc;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/provider/osc/OscUserInfo.class */
public class OscUserInfo {

    /* renamed from: id */
    private Long f9id;
    private String email;
    private String name;
    private String gender;
    private String avatar;
    private String location;
    private String url;

    public static void main(String[] args) {
    }

    public Long getId() {
        return this.f9id;
    }

    public void setId(Long id) {
        this.f9id = id;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return this.gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getAvatar() {
        return this.avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getLocation() {
        return this.location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
