package org.tio.sitexxx.web.server.recharge.provider.apple;

import cn.hutool.core.util.StrUtil;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import okhttp3.Headers;
import okhttp3.Response;
import org.slf4j.Logger;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.util.Resps;
import org.tio.sitexxx.service.model.main.RechargeItem;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.service.service.recharge.RechargeItemService;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.servicecommon.utils.LogUtils;
import org.tio.sitexxx.web.server.http.WebApiHttpServerInterceptor;
import org.tio.sitexxx.web.server.recharge.IRechargeProvider;
import org.tio.sitexxx.web.server.recharge.provider.alipay.AlipayConfig;
import org.tio.sitexxx.web.server.recharge.provider.apple.p006vo.ReceiptResult;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.crypto.Md5;
import org.tio.utils.http.HttpUtils;
import org.tio.utils.json.Json;
import org.tio.utils.resp.Resp;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/provider/apple/AppleRechargeProvider.class */
public class AppleRechargeProvider implements IRechargeProvider {
    private static Logger log = LogUtils.getCoinLog();

    /* renamed from: me */
    public static final AppleRechargeProvider f27me = new AppleRechargeProvider();

    protected AppleRechargeProvider() {
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public HttpResponse toThirdRechargePage(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        String tradeno = rechargeItem.getTradeno();
        return Resps.json(request, Resp.ok(tradeno));
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public boolean isValidCallback(HttpRequest request, int callbackType) throws Exception {
        RequestExt requestExt = WebUtils.getRequestExt(request);
        String appVersion = requestExt.getAppVersion();
        String receipt = request.getParam("receipt");
        if (receipt == null) {
            receipt = "";
        }
        String tradeno = getTradeno(request, callbackType);
        String sign = request.getParam("sign");
        String key = ConfService.getString("ios.pay.key", "iolkhtgf");
        String str = receipt + tradeno + key + appVersion;
        String mysign = Md5.getMD5(str);
        if (Objects.equals(mysign, sign)) {
            return true;
        }
        log.error("苹果充值验证失败, appversion:{}, tradeno:{}", appVersion, tradeno);
        return false;
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public String getTradeno(HttpRequest request, int callbackType) {
        String tradeno = request.getParam("tradeno");
        return tradeno;
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public void fillOnNotify(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        String receipt = request.getParam("receipt");
        if (StrUtil.isBlank(receipt)) {
            rechargeItem.setRemark("收到回调,但没有receipt值,回调原状态：" + request.getParam("status"));
            return;
        }
        try {
            ReceiptResult result = IosVerify.verifyReceipt(request, receipt, rechargeItem);
            rechargeItem.setReceipt(receipt);
            Integer status = Integer.valueOf(result.getStatus());
            rechargeItem.setThirdstatus(status + "");
            if (Objects.equals(status, 0)) {
                RechargeItem rechargeItem_1 = RechargeItemService.me.selectByReceipt(receipt, rechargeItem.getId());
                if (rechargeItem_1 != null) {
                    log.error("receipt重复,\r\n原RechargeItem\r\n{}\r\n现RechargeItem\r\n{}", Json.toFormatedJson(rechargeItem_1), Json.toFormatedJson(rechargeItem));
                    rechargeItem.setStatus(9);
                } else {
                    rechargeItem.setStatus(2);
                }
            } else {
                rechargeItem.setRemark("收到回调,但status并没有成功：" + status);
            }
        } catch (Exception e1) {
            throw e1;
        }
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public HttpResponse notifySuccess(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        Resp resp = Resp.ok();
        return Resps.json(request, resp);
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public HttpResponse notifyFail(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        Resp resp = Resp.fail();
        return Resps.json(request, resp);
    }

    public static void main(String[] args) throws Exception {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("host", "www.nb350.com");
        headerMap.put(WebApiHttpServerInterceptor.HEADER_NAME_TIO_APPVERSION, "1.2.9");
        String receipt = URLDecoder.decode("MIIT4gYJKoZIhvcNAQcCoIIT0zCCE88CAQExCzAJBgUrDgMCGgUAMIIDgwYJKoZIhvcNAQcBoIIDdASCA3AxggNsMAoCARQCAQEEAgwAMAsCAQ4CAQEEAwIBeDALAgEZAgEBBAMCAQMwDQIBCgIBAQQFFgMxNyswDQIBDQIBAQQFAgMBhqAwDgIBAQIBAQQGAgRQsF3ZMA4CAQkCAQEEBgIEUDI1MTAOAgELAgEBBAYCBAcXtpEwDgIBEAIBAQQGAgQxe1B1MBACAQMCAQEECAwGMS4yLjE3MBACAQ8CAQEECAIGVJzNex4bMBACARMCAQEECAwGMS4xLjIwMBQCAQACAQEEDAwKUHJvZHVjdGlvbjAYAgECAgEBBBAMDmNvbS5uYjM1MC5uYnliMBgCAQQCAQIEEOyLnSrJvyHJHsFVirjV7BowHAIBBQIBAQQUIUkX0Cmzp744oATWR68tUktxoSIwHgIBCAIBAQQWFhQyMDE5LTAyLTIxVDExOjU0OjQ2WjAeAgEMAgEBBBYWFDIwMTktMDItMjFUMTE6NTQ6NDZaMB4CARICAQEEFhYUMjAxOC0wOS0xMFQxMjowNjoxNVowPgIBBwIBAQQ2Q7FuIxDp4xjNBAMGvQxTkwfFmq7zXwciYYUxpNrhE%2B2gQXVmjRIawfekTlNr0a6Y/h0z25NGMEsCAQYCAQEEQ11HjqSpjQW2iVdAsWoEkuS9l3kR2KIwJ7PmG/VxFg7vx0CmPUVjogoAPMwc92Vctlsl/99oweNynb8WDDJ96ZgGXKwwggFZAgERAgEBBIIBTzGCAUswCwICBqwCAQEEAhYAMAsCAgatAgEBBAIMADALAgIGsAIBAQQCFgAwCwICBrICAQEEAgwAMAsCAgazAgEBBAIMADALAgIGtAIBAQQCDAAwCwICBrUCAQEEAgwAMAsCAga2AgEBBAIMADAMAgIGpQIBAQQDAgEBMAwCAgarAgEBBAMCAQEwDAICBq8CAQEEAwIBADAMAgIGsQIBAQQDAgEAMA8CAgauAgEBBAYCBFYKuAIwGgICBqcCAQEEEQwPNTMwMDAwNDAxODA4NTEyMBoCAgapAgEBBBEMDzUzMDAwMDQwMTgwODUxMjAeAgIGpgIBAQQVDBNjb20ubmIzNTAubmJ5Yi43NTYwMB8CAgaoAgEBBBYWFDIwMTktMDItMjFUMTE6NTM6MjNaMB8CAgaqAgEBBBYWFDIwMTktMDItMjFUMTE6NTM6MjNaoIIOZTCCBXwwggRkoAMCAQICCA7rV4fnngmNMA0GCSqGSIb3DQEBBQUAMIGWMQswCQYDVQQGEwJVUzETMBEGA1UECgwKQXBwbGUgSW5jLjEsMCoGA1UECwwjQXBwbGUgV29ybGR3aWRlIERldmVsb3BlciBSZWxhdGlvbnMxRDBCBgNVBAMMO0FwcGxlIFdvcmxkd2lkZSBEZXZlbG9wZXIgUmVsYXRpb25zIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MB4XDTE1MTExMzAyMTUwOVoXDTIzMDIwNzIxNDg0N1owgYkxNzA1BgNVBAMMLk1hYyBBcHAgU3RvcmUgYW5kIGlUdW5lcyBTdG9yZSBSZWNlaXB0IFNpZ25pbmcxLDAqBgNVBAsMI0FwcGxlIFdvcmxkd2lkZSBEZXZlbG9wZXIgUmVsYXRpb25zMRMwEQYDVQQKDApBcHBsZSBJbmMuMQswCQYDVQQGEwJVUzCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKXPgf0looFb1oftI9ozHI7iI8ClxCbLPcaf7EoNVYb/pALXl8o5VG19f7JUGJ3ELFJxjmR7gs6JuknWCOW0iHHPP1tGLsbEHbgDqViiBD4heNXbt9COEo2DTFsqaDeTwvK9HsTSoQxKWFKrEuPt3R%2BYFZA1LcLMEsqNSIH3WHhUa%2BiMMTYfSgYMR1TzN5C4spKJfV%2BkhUrhwJzguqS7gpdj9CuTwf0%2Bb8rB9Typj1IawCUKdg7e/pn%2B/8Jr9VterHNRSQhWicxDkMyOgQLQoJe2XLGhaWmHkBBoJiY5uB0Qc7AKXcVz0N92O9gt2Yge4%2BwHz%2BKO0NP6JlWB7%2BIDSSMCAwEAAaOCAdcwggHTMD8GCCsGAQUFBwEBBDMwMTAvBggrBgEFBQcwAYYjaHR0cDovL29jc3AuYXBwbGUuY29tL29jc3AwMy13d2RyMDQwHQYDVR0OBBYEFJGknPzEdrefoIr0TfWPNl3tKwSFMAwGA1UdEwEB/wQCMAAwHwYDVR0jBBgwFoAUiCcXCam2GGCL7Ou69kdZxVJUo7cwggEeBgNVHSAEggEVMIIBETCCAQ0GCiqGSIb3Y2QFBgEwgf4wgcMGCCsGAQUFBwICMIG2DIGzUmVsaWFuY2Ugb24gdGhpcyBjZXJ0aWZpY2F0ZSBieSBhbnkgcGFydHkgYXNzdW1lcyBhY2NlcHRhbmNlIG9mIHRoZSB0aGVuIGFwcGxpY2FibGUgc3RhbmRhcmQgdGVybXMgYW5kIGNvbmRpdGlvbnMgb2YgdXNlLCBjZXJ0aWZpY2F0ZSBwb2xpY3kgYW5kIGNlcnRpZmljYXRpb24gcHJhY3RpY2Ugc3RhdGVtZW50cy4wNgYIKwYBBQUHAgEWKmh0dHA6Ly93d3cuYXBwbGUuY29tL2NlcnRpZmljYXRlYXV0aG9yaXR5LzAOBgNVHQ8BAf8EBAMCB4AwEAYKKoZIhvdjZAYLAQQCBQAwDQYJKoZIhvcNAQEFBQADggEBAA2mG9MuPeNbKwduQpZs0%2BiMQzCCX%2BBc0Y2%2BvQ%2B9GvwlktuMhcOAWd/j4tcuBRSsDdu2uP78NS58y60Xa45/H%2BR3ubFnlbQTXqYZhnb4WiCV52OMD3P86O3GH66Z%2BGVIXKDgKDrAEDctuaAEOR9zucgF/fLefxoqKm4rAfygIFzZ630npjP49ZjgvkTbsUxn/G4KT8niBqjSl/OnjmtRolqEdWXRFgRi48Ff9Qipz2jZkgDJwYyz%2BI0AZLpYYMB8r491ymm5WyrWHWhumEL1TKc3GZvMOxx6GUPzo22/SGAGDDaSK%2BzeGLUR2i0j0I78oGmcFxuegHs5R0UwYS/HE6gwggQiMIIDCqADAgECAggB3rzEOW2gEDANBgkqhkiG9w0BAQUFADBiMQswCQYDVQQGEwJVUzETMBEGA1UEChMKQXBwbGUgSW5jLjEmMCQGA1UECxMdQXBwbGUgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkxFjAUBgNVBAMTDUFwcGxlIFJvb3QgQ0EwHhcNMTMwMjA3MjE0ODQ3WhcNMjMwMjA3MjE0ODQ3WjCBljELMAkGA1UEBhMCVVMxEzARBgNVBAoMCkFwcGxlIEluYy4xLDAqBgNVBAsMI0FwcGxlIFdvcmxkd2lkZSBEZXZlbG9wZXIgUmVsYXRpb25zMUQwQgYDVQQDDDtBcHBsZSBXb3JsZHdpZGUgRGV2ZWxvcGVyIFJlbGF0aW9ucyBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAMo4VKbLVqrIJDlI6Yzu7F%2B4fyaRvDRTes58Y4Bhd2RepQcjtjn%2BUC0VVlhwLX7EbsFKhT4v8N6EGqFXya97GP9q%2BhUSSRUIGayq2yoy7ZZjaFIVPYyK7L9rGJXgA6wBfZcFZ84OhZU3au0Jtq5nzVFkn8Zc0bxXbmc1gHY2pIeBbjiP2CsVTnsl2Fq/ToPBjdKT1RpxtWCcnTNOVfkSWAyGuBYNweV3RY1QSLorLeSUheHoxJ3GaKWwo/xnfnC6AllLd0KRObn1zeFM78A7SIym5SFd/Wpqu6cWNWDS5q3zRinJ6MOL6XnAamFnFbLw/eVovGJfbs%2BZ3e8bY/6SZasCAwEAAaOBpjCBozAdBgNVHQ4EFgQUiCcXCam2GGCL7Ou69kdZxVJUo7cwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBQr0GlHlHYJ/vRrjS5ApvdHTX8IXjAuBgNVHR8EJzAlMCOgIaAfhh1odHRwOi8vY3JsLmFwcGxlLmNvbS9yb290LmNybDAOBgNVHQ8BAf8EBAMCAYYwEAYKKoZIhvdjZAYCAQQCBQAwDQYJKoZIhvcNAQEFBQADggEBAE/P71m%2BLPWybC%2BP7hOHMugFNahui33JaQy52Re8dyzUZ%2BL9mm06WVzfgwG9sq4qYXKxr83DRTCPo4MNzh1HtPGTiqN0m6TDmHKHOz6vRQuSVLkyu5AYU2sKThC22R1QbCGAColOV4xrWzw9pv3e9w0jHQtKJoc/upGSTKQZEhltV/V6WId7aIrkhoxK6%2BJJFKql3VUAqa67SzCu4aCxvCmA5gl35b40ogHKf9ziCuY7uLvsumKV8wVjQYLNDzsdTJWk26v5yZXpT%2BRN5yaZgem8%2BbQp0gF6ZuEujPYhisX4eOGBrr/TkJ2prfOv/TgalmcwHFGlXOxxioK0bA8MFR8wggS7MIIDo6ADAgECAgECMA0GCSqGSIb3DQEBBQUAMGIxCzAJBgNVBAYTAlVTMRMwEQYDVQQKEwpBcHBsZSBJbmMuMSYwJAYDVQQLEx1BcHBsZSBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTEWMBQGA1UEAxMNQXBwbGUgUm9vdCBDQTAeFw0wNjA0MjUyMTQwMzZaFw0zNTAyMDkyMTQwMzZaMGIxCzAJBgNVBAYTAlVTMRMwEQYDVQQKEwpBcHBsZSBJbmMuMSYwJAYDVQQLEx1BcHBsZSBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTEWMBQGA1UEAxMNQXBwbGUgUm9vdCBDQTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAOSRqQkfkdseR1DrBe1eeYQt6zaiV0xV7IsZid75S2z1B6siMALoGD74UAnTf0GomPnRymacJGsR0KO75Bsqwx%2BVnnoMpEeLW9QWNzPLxA9NzhRp0ckZcvVdDtV/X5vyJQO6VY9NXQ3xZDUjFUsVWR2zlPf2nJ7PULrBWFBnjwi0IPfLrCwgb3C2PwEwjLdDzw%2BdPfMrSSgayP7OtbkO2V4c1ss9tTqt9A8OAJILsSEWLnTVPA3bYharo3GSR1NVwa8vQbP4%2B%2BNwzeajTEV%2BH0xrUJZBicR0YgsQg0GHM4qBsTBY7FoEMoxos48d3mVz/2deZbxJ2HafMxRloXeUyS0CAwEAAaOCAXowggF2MA4GA1UdDwEB/wQEAwIBBjAPBgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBQr0GlHlHYJ/vRrjS5ApvdHTX8IXjAfBgNVHSMEGDAWgBQr0GlHlHYJ/vRrjS5ApvdHTX8IXjCCAREGA1UdIASCAQgwggEEMIIBAAYJKoZIhvdjZAUBMIHyMCoGCCsGAQUFBwIBFh5odHRwczovL3d3dy5hcHBsZS5jb20vYXBwbGVjYS8wgcMGCCsGAQUFBwICMIG2GoGzUmVsaWFuY2Ugb24gdGhpcyBjZXJ0aWZpY2F0ZSBieSBhbnkgcGFydHkgYXNzdW1lcyBhY2NlcHRhbmNlIG9mIHRoZSB0aGVuIGFwcGxpY2FibGUgc3RhbmRhcmQgdGVybXMgYW5kIGNvbmRpdGlvbnMgb2YgdXNlLCBjZXJ0aWZpY2F0ZSBwb2xpY3kgYW5kIGNlcnRpZmljYXRpb24gcHJhY3RpY2Ugc3RhdGVtZW50cy4wDQYJKoZIhvcNAQEFBQADggEBAFw2mUwteLftjJvc83eb8nbSdzBPwR%2BFg4UbmT1HN/Kpm0COLNSxkBLYvvRzm%2B7SZA/LeU802KI%2B%2BXj/a8gH7H05g4tTINM4xLG/mk8Ka/8r/FmnBQl8F0BWER5007eLIztHo9VvJOLr0bdw3w9F4SfK8W147ee1Fxeo3H4iNcol1dkP1mvUoiQjEfehrI9zgWDGG1sJL5Ky%2BERI8GA4nhX1PSZnIIozavcNgs/e66Mv%2BVNqW2TAYzN39zoHLFbr2g8hDtq6cxlPtdk2f8GHVdmnmbkyQvvY1XGefqFStxu9k0IkEirHDx22TZxeY8hLgBdQqorV2uT80AkHN7B1dSExggHLMIIBxwIBATCBozCBljELMAkGA1UEBhMCVVMxEzARBgNVBAoMCkFwcGxlIEluYy4xLDAqBgNVBAsMI0FwcGxlIFdvcmxkd2lkZSBEZXZlbG9wZXIgUmVsYXRpb25zMUQwQgYDVQQDDDtBcHBsZSBXb3JsZHdpZGUgRGV2ZWxvcGVyIFJlbGF0aW9ucyBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eQIIDutXh%2BeeCY0wCQYFKw4DAhoFADANBgkqhkiG9w0BAQEFAASCAQBZVNuDS9tjBGr0v1iy37XQPLsNtQpdskkbaJOVm%2BRbS/wrwoowuOseOAChQMi0nO%2BPMz63As3vsqrxJom66970j94QscZZ%2BmtDDxcvKI2jY/VnXasHN3X10vp7NuLw5hH%2B7h8EnpCaasK3yNvOWd7AdwET3MHtJMnrLzRp5SpsYcH41/q%2BaJuWdodzRua0%2BDYXomlOXmfXRz62VspFnSWamb81U%2BG00KPdRTRXK4Paehj9pjiSkvXuZHPZf2zNvMWCvVSl9luyMBhLDoC4mpThiUse4TV7B1hUEcYG604XS90QlLYpCKjktoPerIr0oOHZbuu/YgUa7ZeJhdze1rCP", AlipayConfig.charset);
        String str = receipt + "35925QhTQk190726163702082iolkhtgf1.2.9";
        String mysign = Md5.getMD5(str);
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("tradeno", "35925QhTQk190726163702082");
        paramMap.put("status", "0");
        paramMap.put("sign", mysign);
        paramMap.put("receipt", receipt);
        Response response = HttpUtils.post("https://www.nb350.com/i.html?tio_path=/recharge/nf/33", headerMap, paramMap);
        String body = response.body().string();
        Headers headers = response.headers();
        String cookie = headers.get("Set-Cookie");
        System.out.println(body);
        System.out.println(cookie);
    }
}
