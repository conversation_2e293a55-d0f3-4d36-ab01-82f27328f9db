package org.tio.sitexxx.web.server.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.StreamProgress;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import java.awt.Color;
import java.awt.Font;
import java.awt.FontFormatException;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.awt.image.ImageObserver;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import javax.imageio.ImageIO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.UploadFile;
import org.tio.jfinal.plugin.activerecord.Db;
import org.tio.sitexxx.service.cache.CacheConfig;
import org.tio.sitexxx.service.cache.Caches;
import org.tio.sitexxx.service.model.conf.AutoAvatar;
import org.tio.sitexxx.service.model.conf.AutoAvatarPlate;
import org.tio.sitexxx.service.model.main.Img;
import org.tio.sitexxx.service.service.conf.AvatarService;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.sitexxx.web.server.controller.base.AccessTokenController;
import org.tio.sitexxx.web.server.recharge.IRechargeProvider;
import org.tio.utils.cache.CacheUtils;
import org.tio.utils.cache.FirsthandCreater;
import org.tio.utils.cache.ICache;
import org.tio.utils.hutool.ResourceUtil;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/utils/WxGroupAvatarUtil.class */
public class WxGroupAvatarUtil {
    private static final Logger log = LoggerFactory.getLogger(WxGroupAvatarUtil.class);
    private static final int AVATAR_BASE_WIDTH = 168;
    private static final int AVATAR_BASE_HEIGHT = 168;
    private static final int ONE_IMG_WIDTH = 40;
    private static final int BORDER_WIDTH = 3;
    private static final int CANVANS_W = 132;
    private static final int CANVANS_H = 132;
    private static final int ONE_IMAGE_WIDTH = 126;
    private static final int TWO_IMAGE_WIDTH = 61;
    private static final int FIVE_IMAGE_WIDTH = 40;
    private static List<AutoAvatarPlate> basePlateConf;
    private static int plateSize;

    static {
        basePlateConf = null;
        plateSize = -1;
        if (basePlateConf == null) {
            basePlateConf = AutoAvatarPlate.dao.find("select id,r,g,b from auto_avatar_plate");
            if (basePlateConf != null) {
                plateSize = basePlateConf.size();
            }
        }
    }

    private WxGroupAvatarUtil() {
    }

    public static Img generateGroupAvatar(List<String> paths, int uid) throws Exception {
        int imageSize;
        List<BufferedImage> biList = new ArrayList<>();
        int imgCount = paths.size();
        if (imgCount <= 1) {
            imageSize = ONE_IMAGE_WIDTH;
        } else if (imgCount > 1 && imgCount < 5) {
            imageSize = TWO_IMAGE_WIDTH;
        } else {
            imageSize = 40;
        }
        for (int i = 0; i < imgCount; i++) {
            BufferedImage resize2 = resize(paths.get(i), imageSize, imageSize, true);
            biList.add(resize2);
        }
        BufferedImage outImage = new BufferedImage(132, 132, 1);
        Graphics2D g2d = outImage.getGraphics();
        g2d.setBackground(new Color(226, 226, 226));
        g2d.clearRect(0, 0, 132, 132);
        for (int i2 = 1; i2 <= imgCount; i2++) {
            int index = i2 - 1;
            BufferedImage bi = biList.get(index);
            switch (imgCount) {
                case IRechargeProvider.CallType.RETURN /* 1 */:
                    g2d.drawImage(bi, BORDER_WIDTH, BORDER_WIDTH, (ImageObserver) null);
                    break;
                case IRechargeProvider.CallType.NOTIFY /* 2 */:
                    if (i2 == 1) {
                        g2d.drawImage(bi, BORDER_WIDTH, (132 - imageSize) / 2, (ImageObserver) null);
                        break;
                    } else {
                        g2d.drawImage(bi, 6 + imageSize, (132 - imageSize) / 2, (ImageObserver) null);
                        break;
                    }
                case BORDER_WIDTH /* 3 */:
                    if (i2 == 1) {
                        g2d.drawImage(bi, (132 - imageSize) / 2, BORDER_WIDTH, (ImageObserver) null);
                        break;
                    } else {
                        g2d.drawImage(bi, ((i2 - 1) * BORDER_WIDTH) + ((i2 - 2) * imageSize), imageSize + 6, (ImageObserver) null);
                        break;
                    }
                case 4:
                    if (i2 <= 2) {
                        g2d.drawImage(bi, (i2 * BORDER_WIDTH) + ((i2 - 1) * imageSize), BORDER_WIDTH, (ImageObserver) null);
                        break;
                    } else {
                        g2d.drawImage(bi, ((i2 - 2) * BORDER_WIDTH) + ((i2 - BORDER_WIDTH) * imageSize), imageSize + 6, (ImageObserver) null);
                        break;
                    }
                case 5:
                    if (i2 <= 2) {
                        g2d.drawImage(bi, (((132 - (2 * imageSize)) - BORDER_WIDTH) / 2) + ((i2 - 1) * imageSize) + ((i2 - 1) * BORDER_WIDTH), ((132 - (2 * imageSize)) - BORDER_WIDTH) / 2, (ImageObserver) null);
                        break;
                    } else {
                        g2d.drawImage(bi, ((i2 - 2) * BORDER_WIDTH) + ((i2 - BORDER_WIDTH) * imageSize), (((132 - (2 * imageSize)) - BORDER_WIDTH) / 2) + imageSize + BORDER_WIDTH, (ImageObserver) null);
                        break;
                    }
                case 6:
                    if (i2 <= BORDER_WIDTH) {
                        g2d.drawImage(bi, (BORDER_WIDTH * i2) + (imageSize * (i2 - 1)), ((132 - (2 * imageSize)) - BORDER_WIDTH) / 2, (ImageObserver) null);
                        break;
                    } else {
                        g2d.drawImage(bi, ((i2 - BORDER_WIDTH) * BORDER_WIDTH) + ((i2 - 4) * imageSize), (((132 - (2 * imageSize)) - BORDER_WIDTH) / 2) + imageSize + BORDER_WIDTH, (ImageObserver) null);
                        break;
                    }
                case 7:
                    if (i2 <= 1) {
                        g2d.drawImage(bi, 6 + imageSize, BORDER_WIDTH, (ImageObserver) null);
                    }
                    if (i2 <= 4 && i2 > 1) {
                        g2d.drawImage(bi, ((i2 - 1) * BORDER_WIDTH) + ((i2 - 2) * imageSize), 6 + imageSize, (ImageObserver) null);
                    }
                    if (i2 > 7 || i2 <= 4) {
                        break;
                    } else {
                        g2d.drawImage(bi, ((i2 - 4) * BORDER_WIDTH) + ((i2 - 5) * imageSize), 9 + (2 * imageSize), (ImageObserver) null);
                        break;
                    }
                case 8:
                    if (i2 <= 2) {
                        g2d.drawImage(bi, (((132 - (2 * imageSize)) - BORDER_WIDTH) / 2) + ((i2 - 1) * imageSize) + ((i2 - 1) * BORDER_WIDTH), BORDER_WIDTH, (ImageObserver) null);
                    }
                    if (i2 <= 5 && i2 > 2) {
                        g2d.drawImage(bi, ((i2 - 2) * BORDER_WIDTH) + ((i2 - BORDER_WIDTH) * imageSize), 6 + imageSize, (ImageObserver) null);
                    }
                    if (i2 > 8 || i2 <= 5) {
                        break;
                    } else {
                        g2d.drawImage(bi, ((i2 - 5) * BORDER_WIDTH) + ((i2 - 6) * imageSize), 9 + (2 * imageSize), (ImageObserver) null);
                        break;
                    }
                case AccessTokenController.AccessTokenResp1.RANDOM_LEN /* 9 */:
                    if (i2 <= BORDER_WIDTH) {
                        g2d.drawImage(bi, (i2 * BORDER_WIDTH) + ((i2 - 1) * imageSize), BORDER_WIDTH, (ImageObserver) null);
                    }
                    if (i2 <= 6 && i2 > BORDER_WIDTH) {
                        g2d.drawImage(bi, ((i2 - BORDER_WIDTH) * BORDER_WIDTH) + ((i2 - 4) * imageSize), 6 + imageSize, (ImageObserver) null);
                    }
                    if (i2 > 9 || i2 <= 6) {
                        break;
                    } else {
                        g2d.drawImage(bi, ((i2 - 6) * BORDER_WIDTH) + ((i2 - 7) * imageSize), 9 + (2 * imageSize), (ImageObserver) null);
                        break;
                    }
                    break;
            }
        }
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        ImageIO.write(outImage, "jpg", output);
        UploadFile uploadFile = new UploadFile();
        uploadFile.setData(output.toByteArray());
        uploadFile.setName("wxgroup_avatar.jpg");
        uploadFile.setSize(uploadFile.getData().length);
        Img img = ImgUtils.processImg("wx/group/avatar", Integer.valueOf(uid), uploadFile, 1.0f);
        return img;
    }

    public static String pressUserAvatar(String nickFristChar) throws Exception {
        if (!Const.USE_AUTO_AVATAR) {
            String avatar = AvatarService.nextAvatar("1");
            return avatar;
        }
        if (nickFristChar.length() > 1) {
            nickFristChar = nickFristChar.substring(0, 1).toUpperCase();
        }
        ICache cache = Caches.getCache(CacheConfig.AUTO_AVATAR);
        final String key = nickFristChar;
        String path = (String) CacheUtils.get(cache, key, true, new FirsthandCreater<String>() { // from class: org.tio.sitexxx.web.server.utils.WxGroupAvatarUtil.1
            /* renamed from: create, reason: merged with bridge method [inline-methods] */
            public String m92create() {
                String path2 = Db.use("tio_site_conf").queryStr("select path from auto_avatar where chatindex = ? limit 0,1", new Object[]{key});
                if (StrUtil.isNotBlank(path2)) {
                    return path2;
                }
                try {
                    Img img = WxGroupAvatarUtil.imgPressText(key + "_avatar.png", "user/base/avatar", key, Const.USER_AVATAR_BASE_BACKGROUND_IMG, Const.USER_AVATAR_MEDIUM_FONT_SIZE, 1);
                    if (img == null || StrUtil.isBlank(img.getCoverurl())) {
                        WxGroupAvatarUtil.log.error("头像生成错误");
                        return null;
                    }
                    AutoAvatar autoAvatar = new AutoAvatar();
                    autoAvatar.setPath(img.getCoverurl());
                    autoAvatar.setChatindex(key);
                    autoAvatar.setRemark(key);
                    autoAvatar.save();
                    return img.getCoverurl();
                } catch (Exception e) {
                    WxGroupAvatarUtil.log.error("", e);
                    return "";
                }
            }
        });
        if (StrUtil.isBlank(path)) {
            path = (String) CacheUtils.get(cache, "#", true, new FirsthandCreater<String>() { // from class: org.tio.sitexxx.web.server.utils.WxGroupAvatarUtil.2
                /* renamed from: create, reason: merged with bridge method [inline-methods] */
                public String m93create() {
                    String path2 = Db.use("tio_site_conf").queryStr("select path from auto_avatar where chatindex = ? limit 0,1", new Object[]{"#"});
                    if (StrUtil.isNotBlank(path2)) {
                        return path2;
                    }
                    try {
                        Img img = WxGroupAvatarUtil.imgPressText("u#_avatar.png", "user/base/avatar", "#", Const.USER_AVATAR_BASE_BACKGROUND_IMG, Const.USER_AVATAR_MEDIUM_FONT_SIZE, 1);
                        if (img == null || StrUtil.isBlank(img.getCoverurl())) {
                            return null;
                        }
                        AutoAvatar autoAvatar = new AutoAvatar();
                        autoAvatar.setPath(img.getCoverurl());
                        autoAvatar.setChatindex("#");
                        autoAvatar.setRemark("#");
                        autoAvatar.save();
                        return img.getCoverurl();
                    } catch (Exception e) {
                        WxGroupAvatarUtil.log.error("", e);
                        return "";
                    }
                }
            });
        }
        return path;
    }

    public static Img imgPressText(String name, String dir, String str, String basePlatePath, int fontsize, int alpha) throws Exception {
        Image srcImg;
        if (CollectionUtil.isEmpty(basePlateConf)) {
            File srcImgFile = new File(ResourceUtil.getAbsolutePath(basePlatePath));
            srcImg = ImageIO.read(srcImgFile);
        } else {
            srcImg = new BufferedImage(168, 168, 1);
            Graphics g = srcImg.getGraphics();
            AutoAvatarPlate plate = getAvatarPlate();
            g.setColor(new Color(plate.getR().intValue(), plate.getB().intValue(), plate.getG().intValue()));
            g.fillRect(0, 0, 168, 168);
        }
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        ImgUtil.pressText(srcImg, output, str, Color.WHITE, getMediumFont(0, fontsize), 0, 64, alpha);
        UploadFile uploadFile = new UploadFile();
        uploadFile.setData(output.toByteArray());
        uploadFile.setName(name);
        uploadFile.setSize(uploadFile.getData().length);
        try {
            Img img = ImgUtils.processImg(dir, uploadFile);
            return img;
        } catch (Exception e) {
            log.error("", e);
            return null;
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: cn.hutool.http.HttpException */
    public static BufferedImage resize(String path, int height, int width, boolean bb) throws HttpException {
        double ratio;
        try {
            String urlPath = WebUtils.resUrl(path);
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            HttpResponse response = HttpRequest.get(urlPath).executeAsync();
            if (!response.isOk()) {
                throw new HttpException("[url:{}]Server response error with status code: [{}]", new Object[]{urlPath, Integer.valueOf(response.getStatus())});
            }
            response.writeBody(out, true, (StreamProgress) null);
            byte[] bytes = out.toByteArray();
            ByteArrayInputStream input = new ByteArrayInputStream(bytes);
            BufferedImage bi = ImageIO.read(input);
            Image itemp = bi.getScaledInstance(width, height, 4);
            if (bi.getHeight() > height || bi.getWidth() > width) {
                if (bi.getHeight() > bi.getWidth()) {
                    ratio = new Integer(height).doubleValue() / bi.getHeight();
                } else {
                    ratio = new Integer(width).doubleValue() / bi.getWidth();
                }
                AffineTransformOp op = new AffineTransformOp(AffineTransform.getScaleInstance(ratio, ratio), (RenderingHints) null);
                itemp = op.filter(bi, (BufferedImage) null);
            }
            if (bb) {
                Image bufferedImage = new BufferedImage(width, height, 1);
                Graphics2D g = bufferedImage.createGraphics();
                g.setColor(Color.white);
                g.fillRect(0, 0, width, height);
                if (width == itemp.getWidth((ImageObserver) null)) {
                    g.drawImage(itemp, 0, (height - itemp.getHeight((ImageObserver) null)) / 2, itemp.getWidth((ImageObserver) null), itemp.getHeight((ImageObserver) null), Color.white, (ImageObserver) null);
                } else {
                    g.drawImage(itemp, (width - itemp.getWidth((ImageObserver) null)) / 2, 0, itemp.getWidth((ImageObserver) null), itemp.getHeight((ImageObserver) null), Color.white, (ImageObserver) null);
                }
                g.dispose();
                itemp = bufferedImage;
            }
            return (BufferedImage) itemp;
        } catch (Exception e) {
            log.error("", e);
            return null;
        }
    }

    public static Font getMediumFont(int ft, float fs) throws IOException {
        String fontUrl = ResourceUtil.getAbsolutePath(Const.MEDIUM_FONT_TTF_FILE);
        InputStream is = null;
        Font definedFont = null;
        BufferedInputStream bis = null;
        try {
            try {
                is = new FileInputStream(new File(fontUrl));
                bis = new BufferedInputStream(is);
                definedFont = Font.createFont(ft, is).deriveFont(fs);
                if (null != bis) {
                    try {
                        bis.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (null != is) {
                    is.close();
                }
            } catch (FontFormatException e2) {
                e2.printStackTrace();
                if (null != bis) {
                    try {
                        bis.close();
                    } catch (IOException e3) {
                        e3.printStackTrace();
                    }
                }
                if (null != is) {
                    is.close();
                }
            } catch (IOException e4) {
                e4.printStackTrace();
                if (null != bis) {
                    try {
                        bis.close();
                    } catch (IOException e5) {
                        e5.printStackTrace();
                    }
                }
                if (null != is) {
                    is.close();
                }
            }
            return definedFont;
        } catch (Throwable th) {
            if (null != bis) {
                try {
                    bis.close();
                } catch (IOException e6) {
                    e6.printStackTrace();
                    throw th;
                }
            }
            if (null != is) {
                is.close();
            }
            throw th;
        }
    }

    public static AutoAvatarPlate getAvatarPlate() {
        int index = RandomUtil.randomInt(0, plateSize);
        return basePlateConf.get(index);
    }

    public static void main(String[] args) throws IOException {
        BufferedImage img = new BufferedImage(168, 168, 1);
        Graphics g = img.getGraphics();
        g.setColor(new Color(108, 168, 238));
        g.fillRect(0, 0, img.getWidth(), img.getHeight());
        OutputStream output = new FileOutputStream("D:/xufei/11.png");
        ImgUtil.pressText(img, output, "#", Color.WHITE, getMediumFont(0, 80.0f), 0, 0, 1.0f);
    }
}
