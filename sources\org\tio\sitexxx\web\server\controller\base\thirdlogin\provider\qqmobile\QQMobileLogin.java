package org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.qqmobile;

import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.sitexxx.service.model.main.UserThird;
import org.tio.sitexxx.service.model.main.UserThirdQq;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.MobileLogin;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/provider/qqmobile/QQMobileLogin.class */
public class QQMobileLogin extends MobileLogin {
    private static Logger log = LoggerFactory.getLogger(QQMobileLogin.class);

    /* renamed from: me */
    public static QQMobileLogin f11me = new QQMobileLogin();

    private QQMobileLogin() {
    }

    public static void main(String[] args) {
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.MobileLogin
    public UserThird.SubTable createSubTable(HttpRequest request, Integer type) {
        String is_yellow_vip = request.getParam("is_yellow_vip");
        String yellow_vip_level = request.getParam("yellow_vip_level");
        UserThirdQq userThirdQq = new UserThirdQq();
        if (StrUtil.isNotBlank(is_yellow_vip)) {
            Byte is_yellow_vip_b = Byte.valueOf(Byte.parseByte(is_yellow_vip));
            userThirdQq.setIsYellowVip(is_yellow_vip_b);
            if (is_yellow_vip_b.byteValue() == 1) {
                userThirdQq.setYellowVipLevel(yellow_vip_level);
            }
        }
        return userThirdQq;
    }
}
