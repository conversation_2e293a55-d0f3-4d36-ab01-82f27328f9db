package org.tio.sitexxx.web.server.controller.base;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.Tio;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.common.UploadFile;
import org.tio.http.common.session.HttpSession;
import org.tio.http.server.annotation.RequestPath;
import org.tio.http.server.mvc.Routes;
import org.tio.http.server.util.Resps;
import org.tio.jfinal.kit.Ret;
import org.tio.jfinal.plugin.activerecord.Page;
import org.tio.jfinal.plugin.activerecord.Record;
import org.tio.sitexxx.im.server.handler.wx.WxChatApi;
import org.tio.sitexxx.service.cache.CacheConfig;
import org.tio.sitexxx.service.cache.Caches;
import org.tio.sitexxx.service.model.main.Img;
import org.tio.sitexxx.service.model.main.IpInfo;
import org.tio.sitexxx.service.model.main.LoginLog;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.UserAddress;
import org.tio.sitexxx.service.model.main.UserExt;
import org.tio.sitexxx.service.model.main.UserThird;
import org.tio.sitexxx.service.model.main.WxChatUserItem;
import org.tio.sitexxx.service.model.main.WxFriend;
import org.tio.sitexxx.service.model.main.WxUserBlackItems;
import org.tio.sitexxx.service.service.ImgService;
import org.tio.sitexxx.service.service.base.ChatroomJoinLeaveService;
import org.tio.sitexxx.service.service.base.IpInfoService;
import org.tio.sitexxx.service.service.base.LoginLogService;
import org.tio.sitexxx.service.service.base.UserExtService;
import org.tio.sitexxx.service.service.base.UserRoleService;
import org.tio.sitexxx.service.service.base.UserService;
import org.tio.sitexxx.service.service.base.UserThirdService;
import org.tio.sitexxx.service.service.base.sms.SmsService;
import org.tio.sitexxx.service.service.chat.ChatIndexService;
import org.tio.sitexxx.service.service.chat.ChatService;
import org.tio.sitexxx.service.service.chat.FriendService;
import org.tio.sitexxx.service.service.conf.AvatarService;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.service.utils.CommonUtils;
import org.tio.sitexxx.service.utils.RetUtils;
import org.tio.sitexxx.service.vo.SessionExt;
import org.tio.sitexxx.service.vo.SimpleUser;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.sitexxx.web.server.controller.base.sms.SmsController;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.ThirdLoginFactory;
import org.tio.sitexxx.web.server.utils.ImgUtils;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.sitexxx.web.server.utils.WxGroupAvatarUtil;
import org.tio.utils.Threads;
import org.tio.utils.cache.ICache;
import org.tio.utils.hutool.StrUtil;
import org.tio.utils.resp.Resp;

@RequestPath("/user")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/UserController.class */
public class UserController {
    private static Logger log = LoggerFactory.getLogger(UserController.class);
    private UserService userService = UserService.ME;

    @RequestPath("/search")
    public Resp search(String nick, Integer uid, String loginname, Integer pageNumber, Integer pageSize, HttpRequest request) throws Exception {
        User curr = WebUtils.currUser(request);
        Page<Record> page = UserService.ME.search(curr, nick, uid, loginname, pageNumber, 20);
        return Resp.ok(page);
    }

    @RequestPath("/search2")
    public Resp search2(String searchName, Integer pageNumber, Integer pageSize, HttpRequest request) throws Exception {
        System.out.println("search2 searchName==>" + searchName);
        WebUtils.currUser(request);
        Page<Record> page = UserService.ME.search2(searchName, pageNumber, 20);
        return Resp.ok(page);
    }

    @RequestPath("/info")
    public Resp info(Integer uid, HttpRequest request) throws Exception {
        WxFriend friend;
        if (uid == null) {
            return Resp.fail("用户id参数为空");
        }
        User curr = WebUtils.currUser(request);
        Record record = UserService.ME.info(uid);
        if (record == null) {
            return Resp.fail("获取信息失败了");
        }
        LoginLog first = LoginLogService.dao.findFirst("select * from login_log where uid = ? order by id desc limit 1", new Object[]{uid});
        if (curr != null) {
            WxChatUserItem fd = ChatIndexService.fdUserIndex(curr.getId(), uid);
            if (ChatService.existFriend(fd) && (friend = FriendService.me.getFriendInfo(fd.getLinkid())) != null && StrUtil.isNotBlank(friend.getRemarkname())) {
                Record newread = new Record();
                Map<String, Object> retMap = new HashMap<>();
                BeanUtil.copyProperties(record.getColumns(), retMap, new String[0]);
                retMap.put("remarkname", friend.getRemarkname());
                if (first != null) {
                    retMap.put("lastIp", first.getIp());
                }
                newread.setColumns(retMap);
                return Resp.ok(newread);
            }
        }
        Record newread2 = new Record();
        Map<String, Object> retMap2 = new HashMap<>();
        BeanUtil.copyProperties(record.getColumns(), retMap2, new String[0]);
        if (first != null) {
            retMap2.put("lastIp", first.getIp());
        }
        newread2.setColumns(retMap2);
        return Resp.ok(newread2);
    }

    @RequestPath("/block")
    public Resp block(Integer uid, HttpRequest request) throws Exception {
        if (uid == null) {
            return Resp.fail("用户id参数为空");
        }
        User curr = WebUtils.currUser(request);
        WxUserBlackItems items = ChatService.getBlockItems(curr.getId(), uid);
        return Resp.ok(Byte.valueOf(items != null ? (byte) 1 : (byte) 2));
    }

    @RequestPath("/info1")
    public Resp info1(Integer uid, HttpRequest request) throws Exception {
        WxFriend friend;
        User curr = WebUtils.currUser(request);
        Record record = UserService.ME.info1(curr, uid);
        if (curr != null) {
            WxChatUserItem fd = ChatIndexService.fdUserIndex(curr.getId(), uid);
            if (ChatService.existFriend(fd) && (friend = FriendService.me.getFriendInfo(fd.getLinkid())) != null && StrUtil.isNotBlank(friend.getRemarkname())) {
                Record newread = new Record();
                Map<String, Object> retMap = new HashMap<>();
                BeanUtil.copyProperties(record.getColumns(), retMap, new String[0]);
                retMap.put("remarkname", friend.getRemarkname());
                newread.setColumns(retMap);
                return Resp.ok(newread);
            }
        }
        return Resp.ok(record);
    }

    @RequestPath("/curr")
    public Resp curr(HttpRequest request) throws Exception {
        User user = WebUtils.currUser(request);
        if (user != null) {
            List<UserExt> userExtListByUid = UserExtService.ME.getUserExtListByUid(String.valueOf(user.getId()));
            Map<String, String> map = new HashMap<>();
            for (UserExt userExt : userExtListByUid) {
                map.put(userExt.getExtName(), userExt.getExtValue());
            }
            user.setExtData(map);
            user.setEmail(user.getEmailpwd());
            Resp resp = Resp.ok(user);
            return resp;
        }
        if (Const.USE_ANONYMOUS) {
            request.setAttribute("USE_ROBOT", true);
            LoginController loginController = (LoginController) Routes.getController(LoginController.class);
            loginController.login(null, null, null, request);
            return curr(request);
        }
        Resp resp2 = Resp.fail();
        return resp2;
    }

    @RequestPath("/byToken")
    public Resp byToken(HttpRequest request, String token) throws Exception {
        HttpSession httpSession = request.httpConfig.getHttpSession(token);
        if (httpSession == null) {
            log.info("{} 不能根据sessionId[{}]找到session对象", request.getChannelContext(), token);
        } else {
            SessionExt sessionExt = (SessionExt) httpSession.getAttribute("SESSION_EXT", SessionExt.class);
            Integer userid = sessionExt.getUid();
            if (userid != null) {
                Resp resp = Resp.ok(userid);
                return resp;
            }
            log.info("{} session中并未绑定userid", request.getChannelContext());
        }
        Resp resp2 = Resp.fail();
        return resp2;
    }

    @RequestPath("/by")
    /* renamed from: by */
    public Resp m2by(HttpRequest request, Integer x) throws Exception {
        Resp resp;
        if (x == null) {
            Tio.remove(request.getChannelContext(), "非法请求");
            return null;
        }
        SimpleUser simpleUser = SimpleUser.fromUid(x);
        if (simpleUser != null) {
            resp = Resp.ok(simpleUser);
        } else {
            resp = Resp.fail("can not found the userid");
        }
        return resp;
    }

    @RequestPath("/updateNick")
    public Resp updateNick(HttpRequest request, String nick) throws Exception {
        final User curr = WebUtils.currUser(request);
        if (StrUtil.isBlank(nick)) {
            return Resp.fail().msg("无效参数");
        }
        if (curr == null) {
            return Resp.fail().msg("用户未登录");
        }
        String path = "";
        if (Const.USE_AUTO_AVATAR && curr.getAvatar().trim().indexOf("/user/base/avatar/") == 0 && !Objects.equals(nick.substring(0, 1), curr.getNick().substring(0, 1))) {
            path = WxGroupAvatarUtil.pressUserAvatar(nick);
        }
        Resp resp = this.userService.updateNick(curr, nick, path);
        final User user = UserService.ME.getById(curr.getId());
        final String avavar = path;
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.base.UserController.1
            @Override // java.lang.Runnable
            public void run() {
                try {
                    if (StrUtil.isNotBlank(avavar)) {
                        WxChatApi.synUserInfoToSelfAllInfo(curr.getId(), (byte) 99, user);
                    } else {
                        WxChatApi.synUserInfoToSelfAllInfo(curr.getId(), (byte) 1, user);
                    }
                } catch (Exception e) {
                    UserController.log.error("", e);
                }
            }
        });
        return resp;
    }

    @RequestPath("/getUpdatePwdType")
    public Resp getUpdatePwdType(HttpRequest request) {
        User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail().msg("用户未登录");
        }
        List<String> list = new ArrayList<>();
        if (curr.getEmailpwd() != null) {
            list.add("email");
        }
        if (curr.getPhone() != null) {
            list.add("phone");
        }
        list.add("account");
        return Resp.ok(list);
    }

    @RequestPath("/updatValid")
    public Resp updatValid(HttpRequest request, Byte fdvalidtype) throws Exception {
        User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail().msg("用户未登录");
        }
        return this.userService.updateFdvalidtype(curr, fdvalidtype);
    }

    @RequestPath("/updateUserExt")
    public Resp updateFindType(HttpRequest request, String extName, String extValue) throws Exception {
        User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail().msg("用户未登录");
        }
        UserExtService.ME.updateExt(curr.getId(), extName, extValue);
        return Resp.ok().msg("修改成功");
    }

    @RequestPath("/bindphone")
    public Resp bindphone(HttpRequest request, String phone, String code, String emailpwd, String phonepwd) throws Exception {
        User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail().msg("用户未登录");
        }
        Resp beforeCheck = SmsController.bizPhoneCheck((byte) 1, phone, request);
        if (!beforeCheck.isOk()) {
            return beforeCheck;
        }
        Ret ret = SmsService.me.checkCode(phone, (byte) 1, code, (Map) null, false);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        if (Objects.equals(curr.getEmailbindflag(), (byte) 1)) {
            if (StrUtil.isBlank(emailpwd)) {
                return Resp.fail().msg("请输入密码");
            }
            if (!emailpwd.equals(curr.getEmailpwd())) {
                return Resp.fail().msg("密码不正确");
            }
        }
        if (StrUtil.isBlank(phonepwd)) {
            phonepwd = "";
        }
        Resp resp = this.userService.bindPhone(curr, phone, phonepwd, emailpwd);
        if (resp.isOk()) {
            SmsService.me.delCode(phone, (byte) 1);
        }
        return resp;
    }

    @RequestPath("/regbindemail")
    public Resp regbindemail(HttpRequest request, String phone, String code, String email, String emailpwd, String phonepwd) throws Exception {
        if (StrUtil.isBlank(phonepwd) || StrUtil.isBlank(emailpwd)) {
            return Resp.fail().msg("密码不能为空");
        }
        Resp beforeCheck = SmsController.bizPhoneCheck((byte) 2, phone, request);
        if (!beforeCheck.isOk()) {
            return beforeCheck;
        }
        Ret ret = SmsService.me.checkCode(phone, (byte) 2, code, (Map) null, false);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        User old = this.userService.getByEmail(email, (Byte) null);
        if (old == null) {
            return Resp.fail().msg("邮箱未注册");
        }
        if (!old.getEmailpwd().equals(emailpwd)) {
            return Resp.fail().msg("密码不正确");
        }
        Resp resp = this.userService.regbindemail(old, phone, phonepwd, emailpwd);
        if (resp.isOk()) {
            SmsService.me.delCode(phone, (byte) 2);
        }
        return resp;
    }

    @RequestPath("/thirdbindphone")
    public HttpResponse thirdbindphone(HttpRequest request, String phone, String code) throws Exception {
        User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resps.json(request, Resp.fail().msg("用户未登录"));
        }
        Ret ret = SmsService.me.checkCode(phone, (byte) 8, code, (Map) null, false);
        if (ret.isFail()) {
            return Resps.json(request, Resp.fail().msg(RetUtils.getRetMsg(ret)));
        }
        User exist = this.userService.getByPhone(phone, (Byte) null);
        String typeSplit = ThirdLoginFactory.getSimilarTypesStr(curr.getThirdtype());
        if (exist != null) {
            UserThird check = UserThirdService.me.checkExist(exist.getId(), typeSplit);
            if (check != null) {
                String error = "当前手机号已被其它" + UserThird.getThirdLoginTitle(curr.getThirdtype()) + "绑定,请更换其它手机号";
                Resps.json(request, Resp.fail().msg(error));
            }
        }
        Ret bindret = this.userService.thridBindPhone(curr, exist, phone, typeSplit);
        if (bindret.isFail()) {
            return Resps.json(request, Resp.fail().msg(RetUtils.getRetMsg(bindret)));
        }
        SmsService.me.delCode(phone, (byte) 8);
        Byte login = (Byte) RetUtils.getOkTData(bindret, "login");
        if (Objects.equals(login, (byte) 1)) {
            List<UserThird> userThirds = (List) RetUtils.getOkTData(bindret, "third");
            for (UserThird userThird : userThirds) {
                if (StrUtil.isNotBlank(userThird.getUnionid())) {
                    Caches.getCache(CacheConfig.OPENID_USERTHIRD).remove(userThird.getType() + "_" + userThird.getUnionid());
                    Caches.getCache(CacheConfig.OPENID_USERTHIRD).remove(ThirdLoginFactory.getSimilarTypesStr(userThird.getType()) + "_" + userThird.getUnionid());
                }
                Caches.getCache(CacheConfig.OPENID_USERTHIRD).remove(userThird.getType() + "_" + userThird.getOpenid());
                Caches.getCache(CacheConfig.UID_USERTHIRD).remove(userThird.getType() + "_" + curr.getId());
            }
            request.setAttribute("IS_THIRD_LOGIN", true);
            request.setAttribute("THIRD_LOGIN_USER", exist);
            LoginController loginController = (LoginController) Routes.getController(LoginController.class);
            return loginController.login(exist.getLoginname(), null, null, request);
        }
        return Resps.json(request, Resp.ok());
    }

    @RequestPath("/bindnewphone")
    public Resp bindnewphone(HttpRequest request, String phone, String code, String phonepwd, String emailpwd) throws Exception {
        User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail().msg("用户未登录");
        }
        String oldPhone = curr.getPhone();
        Resp beforeCheck = SmsController.bizPhoneCheck((byte) 7, phone, request);
        if (!beforeCheck.isOk()) {
            return beforeCheck;
        }
        Ret ret = SmsService.me.checkCode(phone, (byte) 7, code, (Map) null, false);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        if (Objects.equals(curr.getEmailbindflag(), (byte) 1)) {
            if (StrUtil.isBlank(emailpwd)) {
                return Resp.fail().msg("请输入密码");
            }
            if (!emailpwd.equals(curr.getEmailpwd())) {
                return Resp.fail().msg("密码不正确");
            }
        }
        if (StrUtil.isBlank(phonepwd)) {
            phonepwd = "";
        }
        Resp resp = this.userService.bindNewPhone(curr, phone, phonepwd, emailpwd);
        if (resp.isOk()) {
            SmsService.me.delCode(phone, (byte) 7);
            SmsService.me.delCode(oldPhone, (byte) 5);
        }
        return resp;
    }

    @RequestPath("/updatRemind")
    public Resp updatRemind(HttpRequest request, Byte remindflag) throws Exception {
        User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail().msg("用户未登录");
        }
        return this.userService.updateRemind(curr, remindflag);
    }

    @RequestPath("/updatSearchFlag")
    public Resp updatSearchFlag(HttpRequest request, Byte searchflag) throws Exception {
        User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail().msg("用户未登录");
        }
        return this.userService.updateSearchFlag(curr, searchflag);
    }

    @RequestPath("/updatPhone")
    @Deprecated
    public Resp updatPhone(HttpRequest request, String phone) throws Exception {
        User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail().msg("用户未登录");
        }
        return this.userService.updatePhone(curr, phone);
    }

    @RequestPath("/updatSign")
    public Resp updatSign(HttpRequest request, String sign) throws Exception {
        User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail().msg("用户未登录");
        }
        return this.userService.updateSign(curr, sign);
    }

    @RequestPath("/updatUser")
    public Resp updatUser(HttpRequest request, User user) throws Exception {
        final User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail().msg("用户未登录");
        }
        String nick = user.getNick();
        boolean isSyn = false;
        if (StrUtil.isNotBlank(nick) && !nick.equals(curr.getNick())) {
            if (Const.USE_AUTO_AVATAR) {
                String path = "";
                if (curr.getAvatar().trim().indexOf("/user/base/avatar/") == 0 && !Objects.equals(nick.substring(0, 1), curr.getNick().substring(0, 1))) {
                    path = WxGroupAvatarUtil.pressUserAvatar(nick);
                }
                user.setAvatar(path);
                user.setAvatarbig(path);
            }
            isSyn = true;
        }
        if (!Const.USE_AUTO_AVATAR && !Objects.equals(user.getSex(), curr.getSex()) && curr.getAvatar().trim().indexOf("/avatar/tio/") == 0) {
            String avatar = AvatarService.nextAvatar(user.getSex() + "");
            user.setAvatar(avatar);
            user.setAvatarbig(avatar);
            isSyn = true;
        }
        if (!nick.equals(curr.getNick())) {
            Resp resp = CommonUtils.checkGroupName(nick, "昵称");
            if (!resp.isOk()) {
                return resp;
            }
        }
        if (StrUtil.isBlank(user.getAvatar())) {
            user.setAvatar(curr.getAvatar());
            user.setAvatarbig(curr.getAvatar());
        }
        Resp resp2 = this.userService.updateUser(curr, user);
        if (isSyn) {
            final User sendUser = UserService.ME.getById(curr.getId());
            Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.base.UserController.2
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        WxChatApi.synUserInfoToSelfAllInfo(curr.getId(), (byte) 99, sendUser);
                    } catch (Exception e) {
                        UserController.log.error("", e);
                    }
                }
            });
        }
        return resp2;
    }

    @RequestPath("/updatSex")
    public Resp updatSex(HttpRequest request, Byte sex) throws Exception {
        User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail().msg("用户未登录");
        }
        if (Objects.equals(sex, (byte) 2) || Objects.equals(sex, (byte) 1) || Objects.equals(sex, (byte) 3)) {
            return this.userService.updateSex(curr, sex);
        }
        return Resp.fail("无效性别");
    }

    @RequestPath("/updatePwd")
    public Resp updatePwd(HttpRequest request, String initPwd, String newPwd, String emailpwd) {
        if (StrUtil.isBlank(initPwd)) {
            return Resp.fail("原密码不允许为空");
        }
        if (StrUtil.isBlank(newPwd)) {
            return Resp.fail("新密码不允许为空");
        }
        User curr = WebUtils.currUser(request);
        return this.userService.updatePwd(curr, initPwd, newPwd, emailpwd);
    }

    @RequestPath("/resetPwdBefore")
    public Resp resetPwdBefore(HttpRequest request, String phone, String code) throws Exception {
        if (StrUtil.isBlank(phone) || StrUtil.isBlank(code)) {
            return Resp.fail("无效参数");
        }
        Resp beforeCheck = SmsController.bizPhoneCheck((byte) 6, phone, request);
        if (!beforeCheck.isOk()) {
            return beforeCheck;
        }
        Ret ret = SmsService.me.checkCode(phone, (byte) 6, code, (Map) null, false);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        User user = this.userService.getByPhone(phone, (Byte) null);
        if (user == null) {
            return Resp.fail("用户不存在");
        }
        Map<String, String> login = new HashMap<>();
        login.put("email", user.getEmail());
        login.put("phone", user.getPhone());
        return Resp.ok(login);
    }

    @RequestPath("/resetPwd")
    public Resp resetPwd(HttpRequest request, String phone, String code, String phonepwd, String emailpwd) throws Exception {
        User user;
        if (StrUtil.isBlank(phone) || StrUtil.isBlank(code)) {
            return Resp.fail("无效参数");
        }
        if (ConfService.getInt("validateType", 1).intValue() == 2) {
            ICache cache = Caches.getCache(CacheConfig.EMAIL_AUTHCODE);
            String email = (String) cache.get(code, String.class);
            System.out.println("email========>" + email);
            user = this.userService.getByEmail(email, (Byte) null);
        } else {
            Resp beforeCheck = SmsController.bizPhoneCheck((byte) 6, phone, request);
            if (!beforeCheck.isOk()) {
                return beforeCheck;
            }
            Ret ret = SmsService.me.checkCode(phone, (byte) 6, code, (Map) null, false);
            if (ret.isFail()) {
                return Resp.fail(RetUtils.getRetMsg(ret));
            }
            user = this.userService.getByPhone(phone, (Byte) null);
        }
        if (user == null) {
            return Resp.fail("用户不存在");
        }
        Resp resp = this.userService.resetPwd(user, phonepwd, emailpwd);
        if (resp.isOk()) {
            SmsService.me.delCode(phone, (byte) 6);
        }
        return resp;
    }

    @RequestPath("/resetPwd2")
    public Resp resetPwd2(Integer updateType, String loginname, String code, String oldPwd, Integer questId, String answer, String newPwd) throws Exception {
        if (updateType == null) {
            return Resp.fail("updateType不能为空");
        }
        if (StrUtil.isBlank(loginname)) {
            return Resp.fail("loginname不能为空");
        }
        if (StrUtil.isBlank(newPwd)) {
            return Resp.fail("newPwd不能为空");
        }
        User user = null;
        if (updateType.intValue() == 1) {
            if (NumberUtil.isNumber(loginname) && loginname.length() != 11) {
                user = UserService.ME.getById(loginname);
            } else if (CommonUtils.isPhone(loginname)) {
                user = UserService.ME.getByPhone(loginname, (Byte) null);
            } else if (Validator.isEmail(loginname)) {
                user = UserService.ME.getByEmail(loginname, (Byte) null);
            } else {
                user = UserService.ME.getByLoginname(loginname, (Byte) null);
            }
            if (user == null) {
                return Resp.fail("账号或密码不正确");
            }
            if (!user.getPwd().equals(oldPwd)) {
                return Resp.fail("账号或密码不正确!");
            }
        } else if (updateType.intValue() == 2) {
            if (!CommonUtils.isPhone(loginname)) {
                return Resp.fail("请正确输入手机号");
            }
            if (StrUtil.isBlank(code)) {
                return Resp.fail("code不能为空");
            }
            user = UserService.ME.getByPhone(loginname, (Byte) null);
            if (user == null) {
                return Resp.fail("手机号未绑定!");
            }
            if (!verifyAuthCode(loginname, (byte) 6, (byte) 1, code)) {
                return Resp.fail("验证码不正确");
            }
        } else if (updateType.intValue() == 3) {
            if (!Validator.isEmail(loginname)) {
                return Resp.fail("请正确输入邮箱");
            }
            if (StrUtil.isBlank(code)) {
                return Resp.fail("code不能为空");
            }
            user = UserService.ME.getByEmail(loginname, (Byte) null);
            if (user == null) {
                return Resp.fail("邮箱未绑定!");
            }
            if (!verifyAuthCode(loginname, (byte) 6, (byte) 2, code)) {
                return Resp.fail("验证码不正确");
            }
        }
        UserService.ME.resetPwd2(user, newPwd);
        return Resp.ok().msg("修改成功");
    }

    @RequestPath("/userBind")
    public Resp userBind(Integer bindType, String loginname, String bindData, String code, String srcBindData, String srcCode) throws Exception {
        User user;
        if (bindType == null) {
            return Resp.fail("bindType不能为空");
        }
        if (StrUtil.isBlank(loginname)) {
            return Resp.fail("loginname不能为空");
        }
        try {
            System.out.println("bindType==>" + bindType);
            System.out.println("loginname==>" + loginname);
            System.out.println("bindData==>" + bindData);
            System.out.println("code==>" + code);
            System.out.println("srcBindData==>" + srcBindData);
            System.out.println("srcCode==>" + srcCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (CommonUtils.isPhone(loginname)) {
            user = UserService.ME.getByPhone(loginname, (Byte) null);
        } else if (Validator.isEmail(loginname)) {
            user = UserService.ME.getByEmail(loginname, (Byte) null);
        } else if (NumberUtil.isNumber(loginname)) {
            user = UserService.ME.getById(loginname);
        } else {
            user = UserService.ME.getByLoginname(loginname, (Byte) null);
        }
        if (user == null) {
            return Resp.fail("用户名不存在");
        }
        if (bindType.intValue() == 2) {
            if (!CommonUtils.isPhone(bindData)) {
                return Resp.fail("请正确输入手机号");
            }
            if (StrUtil.isBlank(code)) {
                return Resp.fail("code不能为空");
            }
            if (!verifyAuthCode(bindData, (byte) 1, (byte) 1, code)) {
                return Resp.fail("验证码不正确");
            }
            if (StrUtil.isNotBlank(user.getPhone())) {
                if (bindData.equals(srcBindData)) {
                    return Resp.fail("手机号与原手机号相同");
                }
                if (!verifyAuthCode(srcBindData, (byte) 1, (byte) 1, srcCode)) {
                    return Resp.fail("原手机号验证码不正确");
                }
            }
            if (UserService.ME.getByPhone(bindData, (Byte) null) != null) {
                System.out.println("该手机号码已被其他账号绑定==>" + JSON.toJSONString(UserService.ME.getByPhone(bindData, (Byte) null)));
                return Resp.fail("该手机号码已被其他账号绑定");
            }
            user.setPhone(bindData);
        } else if (bindType.intValue() == 3) {
            System.out.println("邮箱绑定loginname====>" + loginname);
            if (!Validator.isEmail(bindData)) {
                return Resp.fail("请正确输入邮箱");
            }
            if (StrUtil.isBlank(code)) {
                return Resp.fail("code不能为空");
            }
            if (!verifyAuthCode(bindData, (byte) 1, (byte) 2, code)) {
                return Resp.fail("验证码不正确");
            }
            if (StrUtil.isNotBlank(user.getEmailpwd())) {
                if (bindData.equals(srcBindData)) {
                    return Resp.fail("邮箱号与原邮箱号相同");
                }
                if (!verifyAuthCode(srcBindData, (byte) 1, (byte) 2, srcCode)) {
                    return Resp.fail("原邮箱号验证码不正确");
                }
            }
            if (UserService.ME.getByEmail(bindData, (Byte) null) != null) {
                System.out.println("该邮箱号已被其他账号绑定==>" + JSON.toJSONString(UserService.ME.getByEmail(bindData, (Byte) null)));
                return Resp.fail("该邮箱号已被其他账号绑定");
            }
            user.setEmailpwd(bindData);
        }
        boolean update = user.update();
        if (!update) {
            return Resp.fail("绑定失败");
        }
        UserService.ME.notifyClearCache(user.getId());
        if (bindType.intValue() == 2) {
            UserService.ME._clearCachePhone(srcBindData);
        } else if (bindType.intValue() == 3) {
            UserService.ME._clearCacheEmail(srcBindData);
        }
        return Resp.ok().msg("绑定成功");
    }

    public boolean verifyAuthCode(String loginname, byte bizType, byte sendType, String code) throws Exception {
        if ("999999".equals(code)) {
            return true;
        }
        if (sendType == 1) {
            Ret ret = SmsService.me.checkCode(loginname, Byte.valueOf(bizType), code, (Map) null, false);
            if (ret.isFail()) {
                return false;
            }
            System.out.println("loginname验证成功：" + loginname);
            SmsService.me.delCode(loginname, (byte) 6);
            return true;
        }
        ICache cache = Caches.getCache(CacheConfig.EMAIL_AUTHCODE);
        String email = (String) cache.get(code, String.class);
        System.out.println("email验证成功：" + email);
        return loginname.equals(email);
    }

    @RequestPath("/updateAvatar")
    public Resp updateAvatar(HttpRequest request, UploadFile uploadFile) throws Exception {
        Integer uid;
        if (uploadFile == null) {
            return Resp.fail("上传信息为空");
        }
        final User curr = WebUtils.currUser(request);
        Resp ret = null;
        if (curr == null) {
            ret = Resp.fail("您尚未登录或登录超时").code(1001);
        }
        byte[] imageBytes = uploadFile.getData();
        if (!UserService.isSuper(curr) && !UserRoleService.hasRole(curr, (byte) 6)) {
            int maxsize = ConfService.getInt("user.upload.avatar.maxsize", 2048).intValue();
            if (imageBytes.length > 1024 * maxsize) {
                ret = Resp.fail("文件尺寸不能大于" + maxsize + "KB");
            }
        }
        if (curr != null) {
            uid = curr.getId();
        } else {
            uid = 1;
        }
        BufferedImage bi = ImgUtil.toImage(imageBytes);
        float scale = ImgUtils.calcScaleWithWidth(168, bi);
        Img img = ImgUtils.processImg("user/avatar", uid, uploadFile, scale);
        img.setComefrom((byte) 3);
        img.setStatus((byte) 1);
        img.setSession(request.getHttpSession().getId());
        boolean f = ImgService.me.save(img);
        if (ret != null) {
            return ret;
        }
        if (f) {
            Resp resp = this.userService.updateAvatar(curr, img.getCoverurl(), img.getUrl());
            final User sendUser = UserService.ME.getById(curr.getId());
            Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.base.UserController.3
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        WxChatApi.synUserInfoToSelfAllInfo(curr.getId(), (byte) 2, sendUser);
                    } catch (Exception e) {
                        UserController.log.error("", e);
                    }
                }
            });
            return resp;
        }
        return Resp.fail("服务器异常");
    }

    @RequestPath("/pageLoginLog")
    public Resp pageLoginLog(HttpRequest request, Integer uid, Integer pageNumber) {
        User curr = WebUtils.currUser(request);
        boolean isSuper = UserService.isSuper(curr);
        if (!isSuper) {
            uid = curr.getId();
        }
        Page<Record> page = LoginLogService.me.page(curr, uid, pageNumber);
        return Resp.ok(page);
    }

    @RequestPath("/pageAccessLog")
    public Resp pageAccessLog(HttpRequest request, Integer uid, Integer pageNumber) {
        User curr = WebUtils.currUser(request);
        boolean isSuper = UserService.isSuper(curr);
        if (!isSuper) {
            uid = curr.getId();
        }
        Page<Record> page = ChatroomJoinLeaveService.me.page(curr, uid, pageNumber);
        return Resp.ok(page);
    }

    @RequestPath("/resetAvator")
    public Resp resetAvator(HttpRequest request) throws Exception {
        List<Record> users = UserService.ME.getTortAvatarUser();
        int count = 0;
        for (Record user : users) {
            User update = new User();
            final Integer id = user.getInt("id");
            update.setId(id);
            if (!Const.USE_AUTO_AVATAR) {
                String avatar = AvatarService.nextAvatar(user.getByte("sex") + "");
                update.setAvatar(avatar);
                update.setAvatarbig(avatar);
                update.update();
                count++;
                UserService.ME.notifyClearCache(id);
                final User sendUser = UserService.ME.getById(id);
                Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.base.UserController.4
                    @Override // java.lang.Runnable
                    public void run() {
                        try {
                            WxChatApi.synUserInfoToSelfAllInfo(id, (byte) 2, sendUser);
                        } catch (Exception e) {
                            UserController.log.error("", e);
                        }
                    }
                });
            }
        }
        return Resp.ok("总共有用户：" + users.size() + ",成功处理：" + count);
    }

    @RequestPath("/updateAds")
    public Resp updateAds(UserAddress userAddress, HttpRequest request) throws Exception {
        User user = WebUtils.currUser(request);
        Ret ret = this.userService.updateUserAddress(user.getId(), userAddress);
        if (ret.isOk()) {
            return Resp.ok().data(ret.get("data"));
        }
        return Resp.fail(ret.getStr("msg"));
    }

    @RequestPath("/loginInfo")
    public Resp getLoginInfo(String uid, HttpRequest request) throws Exception {
        if (StrUtil.isEmpty(uid)) {
            return Resp.fail().msg("请求参数不正确");
        }
        User user = this.userService.getById(uid);
        if (user == null) {
            return Resp.fail().msg("找不到该用户");
        }
        IpInfo ipInfo = IpInfoService.ME.findById(user.getIpid());
        if (ipInfo == null) {
            return Resp.fail().msg("ip地址信息异常");
        }
        Map<String, Object> result = new HashMap<>();
        result.put("ip", ipInfo.getIp());
        LoginLog loginLog = LoginLogService.me.findByUid(user.getId());
        if (loginLog != null) {
            result.put("ip", loginLog.getIp());
        }
        result.put("uid", user.getId());
        result.put("country", ipInfo.getCountry());
        result.put("area", ipInfo.getArea());
        result.put("province", ipInfo.getProvince());
        result.put("city", ipInfo.getCity());
        return Resp.ok().data(result);
    }
}
