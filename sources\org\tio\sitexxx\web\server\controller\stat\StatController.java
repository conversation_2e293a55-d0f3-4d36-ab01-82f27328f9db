package org.tio.sitexxx.web.server.controller.stat;

import cn.hutool.core.date.DateUtil;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.plugin.activerecord.Page;
import org.tio.jfinal.plugin.activerecord.Record;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.service.base.UserRoleService;
import org.tio.sitexxx.service.service.base.UserService;
import org.tio.sitexxx.service.service.stat.StatService;
import org.tio.sitexxx.web.server.controller.base.AccessTokenController;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/stat")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/stat/StatController.class */
public class StatController {
    private static Logger log = LoggerFactory.getLogger(StatController.class);

    public static void main(String[] args) {
        log.info("");
    }

    @RequestPath("/requestCountByDay")
    public Resp requestCountByDay(Integer days, HttpRequest request) throws Exception {
        List<Record> list = StatService.me.requestCountByDay(days);
        return Resp.ok(list);
    }

    @RequestPath("/ipCountByDay")
    public Resp ipCountByDay(Integer days, HttpRequest request) throws Exception {
        List<Record> list = StatService.me.ipCountByDay(days);
        return Resp.ok(list);
    }

    @RequestPath("/statIpAndHitsByProvince")
    public Resp statIpAndHitsByProvince(Integer days, HttpRequest request) throws Exception {
        List<Record> list = StatService.me.statIpAndHitsByProvince(days);
        return Resp.ok(list);
    }

    @RequestPath("/ip")
    /* renamed from: ip */
    public Resp m5ip(Boolean mergeRequest, String starttime, String endtime, Integer pageNumber, Integer pageSize, HttpRequest request) throws Exception {
        User curr = WebUtils.currUser(request);
        if (!UserService.isSuper(curr)) {
            long iv = DateUtil.parseDateTime(endtime).getTime() - DateUtil.parseDateTime(starttime).getTime();
            if (iv < 18000000) {
                return Resp.fail("<5小时的时间段数据，只有超管可以查询");
            }
            if (iv >= 864000000) {
                return Resp.fail(">=10天的时间段数据，只有超管可以查询");
            }
            if (iv >= 432000000 && !UserRoleService.hasRole(curr, (byte) 9)) {
                return Resp.fail(">=5天的时间段数据，只有官网源代码授权用户可以查询");
            }
            if (iv >= AccessTokenController.MAX_TIME_INTERVAL && !UserRoleService.hasRole(curr, (byte) 8)) {
                return Resp.fail(">=1天的时间段数据，只有文档VIP授权用户可以查询");
            }
        }
        Page<Record> page = StatService.me.ip(mergeRequest, starttime, endtime, pageNumber, pageSize);
        return Resp.ok(page);
    }
}
