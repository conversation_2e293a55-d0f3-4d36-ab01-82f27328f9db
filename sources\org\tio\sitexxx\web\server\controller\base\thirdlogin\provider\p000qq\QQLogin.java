package org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.p000qq;

import cn.hutool.core.util.StrUtil;
import com.qq.connect.QQConnectException;
import com.qq.connect.api.OpenID;
import com.qq.connect.api.qzone.UserInfo;
import com.qq.connect.javabeans.AccessToken;
import com.qq.connect.javabeans.qzone.UserInfoBean;
import com.qq.connect.oauth.Oauth;
import com.qq.connect.utils.QQConnectConfig;
import java.net.URLEncoder;
import java.util.Objects;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.sitexxx.service.model.main.UserThird;
import org.tio.sitexxx.service.model.main.UserThirdQq;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.ThirdLoginUtils;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login;
import org.tio.sitexxx.web.server.init.WebApiInit;
import org.tio.sitexxx.web.server.recharge.provider.alipay.AlipayConfig;
import org.tio.utils.http.HttpUtils;
import org.tio.utils.json.Json;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/provider/qq/QQLogin.class */
public class QQLogin extends Auth2Login {
    private static Logger log = LoggerFactory.getLogger(QQLogin.class);

    /* renamed from: me */
    public static QQLogin f10me = new QQLogin();

    private QQLogin() {
        updateProperties();
    }

    private void updateProperties() {
        QQConnectConfig.updateProperties("app_ID", ConfService.getString("third.login.qq.pc.AppID", ""));
        QQConnectConfig.updateProperties("app_KEY", ConfService.getString("third.login.qq.pc.AppSecret", ""));
        QQConnectConfig.updateProperties("redirect_URI", ThirdLoginUtils.getCallbackUrl(WebApiInit.httpConfig, 1));
    }

    public static void main(String[] args) {
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login
    public String loginUrl(HttpRequest request, Integer type, String state) throws Exception {
        String scope = QQConnectConfig.getValue("scope");
        return new Oauth().getAuthorizeURL(scope, state);
    }

    private static String getQQUnionID(String accessToken) throws Exception {
        String url = "https://graph.qq.com/oauth2.0/me?access_token=" + URLEncoder.encode(accessToken, AlipayConfig.charset) + "&unionid=1";
        Response response = HttpUtils.get(url);
        if (response.isSuccessful()) {
            String string = response.body().string();
            int i = StrUtil.indexOf(string, '(');
            int j = StringUtils.lastIndexOf(string, ")");
            UnionID unionidObj = (UnionID) Json.toBean(StrUtil.trim(string.substring(i + 1, j - 1)), UnionID.class);
            if (StrUtil.isNotBlank(unionidObj.getUnionID())) {
                return unionidObj.getUnionID();
            }
            return null;
        }
        return null;
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: com.qq.connect.QQConnectException */
    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login
    public UserThird getUserThird(HttpRequest request, Integer type, String state, String code) throws Exception {
        int iIntValue;
        String queryString = request.getRequestLine().getQueryString();
        try {
            AccessToken accessTokenObj = new Oauth().getAccessTokenByQueryString(queryString, state);
            if (accessTokenObj == null) {
                return null;
            }
            if (StrUtil.isBlank(accessTokenObj.getAccessToken())) {
                log.error("QQ登录， AccessToken is null");
                request.close();
                return null;
            }
            String accessToken = accessTokenObj.getAccessToken();
            accessTokenObj.getExpireIn();
            OpenID openIDObj = new OpenID(accessToken);
            String openID = openIDObj.getUserOpenID();
            String unionID = getQQUnionID(accessToken);
            if (StrUtil.isBlank(unionID)) {
                return null;
            }
            UserInfo userinfo = new UserInfo(accessToken, openID);
            UserInfoBean userInfoBean = userinfo.getUserInfo();
            if (userInfoBean.getRet() == 0) {
                UserThird userThird = new UserThird();
                userThird.setOpenid(openID);
                userThird.setAvatar(userInfoBean.getAvatar().getAvatarURL100());
                userThird.setNick(userInfoBean.getNickname());
                userThird.setUnionid(unionID);
                String gender = userInfoBean.getGender();
                if (Objects.equals("男", gender)) {
                    iIntValue = 1;
                } else {
                    iIntValue = (Objects.equals("女", gender) ? 2 : null).intValue();
                }
                Integer sex = Integer.valueOf(iIntValue);
                if (sex != null) {
                    userThird.setSex(sex);
                }
                UserThirdQq userThirdQq = new UserThirdQq();
                userThirdQq.setIsYellowVip(Byte.valueOf(userInfoBean.isYellowYearVip() ? (byte) 1 : (byte) 2));
                if (userInfoBean.isYellowYearVip()) {
                    userThirdQq.setYellowVipLevel(userInfoBean.getLevel() + "");
                }
                userThird.setSubTable(userThirdQq);
                return userThird;
            }
            return null;
        } catch (QQConnectException e) {
            throw e;
        }
    }
}
