package org.tio.sitexxx.web.server.controller.base;

import java.util.HashMap;
import java.util.Map;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.sitexxx.service.init.PropInit;
import org.tio.sitexxx.service.init.RedisInit;
import org.tio.sitexxx.service.model.conf.YxConf;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.service.service.conf.MgWebsiteService;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.sitexxx.servicecommon.vo.topic.TopicVo;
import org.tio.utils.jfinal.P;
import org.tio.utils.resp.Resp;

@RequestPath("/config")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/ConfigController.class */
public class ConfigController {
    private static Logger log = LoggerFactory.getLogger(ConfigController.class);
    public static Map<String, Object> CONFIG_MAP = new HashMap();
    public static Map<String, Object> VIEW_MODEL = new HashMap();

    static {
        String title = ConfService.getString("seo.title", "t-io - 解决其它网络框架没有解决的痛点");
        String keywords = ConfService.getString("seo.keywords", "t-io,tio,开源,netty,mina,rpc,jfinal,layui,hutool,osc,io,socket,tcp,nio,aio,nio2,im,游戏,java,长连接");
        String description = ConfService.getString("seo.description", "t-io - 解决其它网络框架没有解决的痛点");
        String jsVersion = ConfService.getString("js_version", "1");
        CONFIG_MAP.put("res_server", Const.RES_SERVER);
        CONFIG_MAP.put("session_cookie_name", Const.Http.SESSION_COOKIE_NAME);
        CONFIG_MAP.put("api_ctx", Const.API_CONTEXTPATH);
        CONFIG_MAP.put("api_suf", Const.API_SUFFIX);
        CONFIG_MAP.put("js_version", jsVersion);
        CONFIG_MAP.put("sitename", ConfService.getString("sitename", "t-io社交IM平台"));
        CONFIG_MAP.put("im_heartbeat_timeout", Long.valueOf(Const.IM_HEARTBEAT_TIMEOUT));
        CONFIG_MAP.put("check_code", ConfService.getString("phone.register.code.check", "0"));
        String tioim_title = ConfService.getString("tioim.title", "谭聊，让所有公司用得上靠谱的IM");
        String tioim_keywords = ConfService.getString("tioim.keywords", "IM,t-io,高并发,集群,私有部署");
        String tioim_description = ConfService.getString("tioim.description", "谭聊是基于t-io研发的类似微信的完整IM，支持集群、语音通话、视频通话、私聊、群聊");
        VIEW_MODEL.put("tioim_title", tioim_title);
        VIEW_MODEL.put("tioim_keywords", tioim_keywords);
        VIEW_MODEL.put("tioim_description", tioim_description);
        VIEW_MODEL.put("title", title);
        VIEW_MODEL.put("keywords", keywords);
        VIEW_MODEL.put("description", description);
        VIEW_MODEL.put("check_code", ConfService.getString("phone.register.code.check", "0"));
        VIEW_MODEL.putAll(CONFIG_MAP);
    }

    public static void main(String[] args) {
    }

    @RequestPath("/update")
    public Resp update(HttpRequest request) throws Exception {
        P.clear();
        PropInit.forceInit();
        return Resp.ok();
    }

    @RequestPath("/clearConf")
    public Resp clearConf(HttpRequest request) throws Exception {
        ConfService.clearCache();
        RedissonClient redisson = RedisInit.get();
        RTopic topic1 = redisson.getTopic("COMMON_TOPIC");
        TopicVo topicVo = new TopicVo();
        topicVo.setType((byte) 3);
        topic1.publish(topicVo);
        return Resp.ok();
    }

    @RequestPath("/clearAll")
    public Resp clearAll(HttpRequest request) throws Exception {
        clearConf(request);
        update(request);
        return Resp.ok();
    }

    @RequestPath("/viewmodel")
    public Resp viewmodel(HttpRequest request) throws Exception {
        return Resp.ok(VIEW_MODEL);
    }

    @RequestPath("/base")
    public Resp config(HttpRequest request) {
        CONFIG_MAP.put("website", MgWebsiteService.me.list());
        CONFIG_MAP.put("conf", ConfService.getClientCacheData());
        return Resp.ok(CONFIG_MAP);
    }

    @RequestPath("/query")
    public Resp query(HttpRequest request, String configName) {
        YxConf yxConf = ConfService.getYxConfigByName(configName);
        if (yxConf != null) {
            return Resp.ok(yxConf);
        }
        return Resp.fail();
    }
}
