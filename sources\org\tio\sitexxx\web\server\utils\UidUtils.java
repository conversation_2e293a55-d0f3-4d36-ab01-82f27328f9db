package org.tio.sitexxx.web.server.utils;

import java.util.Random;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/utils/UidUtils.class */
public class UidUtils {
    private static int mUidLength = 8;

    public int getUidLength() {
        return mUidLength;
    }

    public static int getRandomUid(int uidLength) {
        if (uidLength <= 1 || uidLength > 32) {
            mUidLength = 8;
        } else {
            mUidLength = uidLength;
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < mUidLength; i++) {
            if (i == 0) {
                sb.append(new Random().nextInt(9) + 1);
            } else {
                sb.append(new Random().nextInt(10));
            }
        }
        return Integer.parseInt(sb.toString());
    }
}
