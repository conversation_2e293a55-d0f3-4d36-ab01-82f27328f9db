package org.tio.sitexxx.web.server.controller.base;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import java.io.Serializable;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.Tio;
import org.tio.http.common.HttpConfig;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.annotation.RequestPath;
import org.tio.http.server.util.Resps;
import org.tio.sitexxx.service.cache.CacheConfig;
import org.tio.sitexxx.service.cache.Caches;
import org.tio.sitexxx.service.init.PropInit;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.SystemTimer;
import org.tio.utils.cache.ICache;
import org.tio.utils.crypto.ACEUtils;
import org.tio.utils.crypto.Md5;
import org.tio.utils.jfinal.P;
import org.tio.utils.resp.Resp;

@RequestPath("/a")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/AccessTokenController.class */
public class AccessTokenController {
    private static Logger log = LoggerFactory.getLogger(AccessTokenController.class);
    public static final String androidKey1 = P.get("access.token.android.key1");
    public static final String androidKey2 = P.get("access.token.android.key2");
    public static final String androidKey3 = P.get("access.token.android.key3");
    public static final String iosKey1 = P.get("access.token.ios.key1");
    public static final String iosKey2 = P.get("access.token.ios.key2");
    public static final String iosKey3 = P.get("access.token.ios.key3");
    public static final String pcKey1 = P.get("access.token.pc.key1");
    public static final String pcKey2 = P.get("access.token.pc.key2");
    public static final String pcKey3 = P.get("access.token.pc.key3");
    public static final long MAX_TIME_INTERVAL = 86400000;

    /* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/AccessTokenController$AccessTokenResp1.class */
    public static class AccessTokenResp1 implements Serializable {
        private static final long serialVersionUID = -4042961444820016173L;
        public static final int RANDOM_LEN = 9;

        /* renamed from: x */
        private String f0x = RandomUtil.randomString(9);

        /* renamed from: y */
        private String f1y = RandomUtil.randomString(9);

        /* renamed from: z */
        private String f2z = RandomUtil.randomString(9);

        /* renamed from: i */
        private String f3i = RandomUtil.randomString(9);

        /* renamed from: t */
        private Long f4t;

        public AccessTokenResp1(Long t) {
            this.f4t = null;
            this.f4t = t;
        }

        public String getX() {
            return this.f0x;
        }

        public void setX(String x) {
            this.f0x = x;
        }

        public String getY() {
            return this.f1y;
        }

        public void setY(String y) {
            this.f1y = y;
        }

        public String getZ() {
            return this.f2z;
        }

        public void setZ(String z) {
            this.f2z = z;
        }

        public String getI() {
            return this.f3i;
        }

        public void setI(String i) {
            this.f3i = i;
        }

        public Long getT() {
            return this.f4t;
        }

        public void setT(Long t) {
            this.f4t = t;
        }
    }

    public static void main(String[] args) {
        PropInit.init();
        byte[] key = SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue()).getEncoded();
        System.out.println(new String(key));
        AES aes = SecureUtil.aes(key);
        byte[] encrypt = aes.encrypt("test中文");
        aes.decrypt(encrypt);
        String encryptHex = aes.encryptHex("test中文");
        String decryptStr = aes.decryptStr(encryptHex);
        System.out.println(encryptHex);
        System.out.println(decryptStr);
    }

    private static void error(HttpRequest request, String msg) {
        log.error(request.getClientIp() + "\r\n" + request.getRequestLine() + "\r\n" + msg);
    }

    @RequestPath("/x")
    public HttpResponse step1(HttpRequest request, String r, Long t, String s) throws Exception {
        if (t == null) {
            error(request, "参数t为空");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        if (r == null || r.length() != 9) {
            error(request, "参数r为空或其长度不为9");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        long time = SystemTimer.currTime;
        if (Math.abs(time - t.longValue()) > MAX_TIME_INTERVAL) {
            error(request, "参数t与服务器时间相隔超过1天");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        if (StrUtil.isBlank(s)) {
            error(request, "参数s为空");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        RequestExt requestExt = WebUtils.getRequestExt(request);
        String key1 = getKey1(request, requestExt);
        String sign = Md5.getMD5("${" + key1 + r + t + "}");
        if (!Objects.equals(sign, s)) {
            error(request, "验签失败");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        AccessTokenResp1 accessTokenResp1 = new AccessTokenResp1(t);
        ICache cache = Caches.getCache(CacheConfig.ACCESS_TOKEN_RESP_1);
        cache.put(accessTokenResp1.getX() + request.getClientIp() + accessTokenResp1.getY(), accessTokenResp1);
        HttpResponse ret = Resps.json(request, Resp.ok(accessTokenResp1));
        return ret;
    }

    @RequestPath("/y")
    public HttpResponse step2(HttpRequest request, String x, String y, String z, String i, Long t, String s) throws Exception {
        if (t == null) {
            error(request, "参数t为空");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        if (StrUtil.isBlank(x) || x.length() != 9) {
            error(request, "参数x为空或长度不对");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        if (StrUtil.isBlank(y) || x.length() != 9) {
            error(request, "参数y为空或长度不对");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        if (StrUtil.isBlank(z) || x.length() != 9) {
            error(request, "参数z为空或长度不对");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        if (StrUtil.isBlank(i) || x.length() != 9) {
            error(request, "参数i为空或长度不对");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        long time = SystemTimer.currTime;
        if (Math.abs(time - t.longValue()) > MAX_TIME_INTERVAL) {
            error(request, "参数t与服务器时间相隔超过1天");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        if (StrUtil.isBlank(s)) {
            error(request, "参数s为空");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        RequestExt requestExt = WebUtils.getRequestExt(request);
        String key2 = getKey2(request, requestExt);
        String sign = Md5.getMD5("${" + key2 + y + i + "}");
        if (!Objects.equals(sign, s)) {
            error(request, "验签失败");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        ICache cache = Caches.getCache(CacheConfig.ACCESS_TOKEN_RESP_1);
        AccessTokenResp1 accessTokenResp1 = (AccessTokenResp1) cache.get(y + request.getClientIp() + x, AccessTokenResp1.class);
        if (accessTokenResp1 == null) {
            error(request, "获取不到AccessTokenResp1，也许已经超时，或者这是个非法请求");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        if (!Objects.equals(i, accessTokenResp1.getZ()) || !Objects.equals(z, accessTokenResp1.getI())) {
            error(request, "i和z的值不对");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        long iv = t.longValue() - accessTokenResp1.getT().longValue();
        if (iv <= 0 || iv > 5000) {
            error(request, "这次传的t和上次的t相隔" + iv + "毫秒");
            Tio.remove(request.getChannelContext(), "");
            return null;
        }
        String key3 = getKey3(request, requestExt);
        String sessionid = request.getHttpSession().getId();
        ICache cacheTemp = Caches.getCache(CacheConfig.TIO_ACCESS_TOKEN_TEMP);
        String xxxx = (String) cacheTemp.get(sessionid, String.class);
        if (StrUtil.isBlank(xxxx)) {
            HttpConfig httpConfig = request.httpConfig;
            xxxx = httpConfig.getSessionIdGenerator().sessionId(httpConfig, request);
            cacheTemp.put(sessionid, xxxx);
        }
        String zzz = ACEUtils.encrypt(xxxx, key3, key3);
        ICache cache2 = Caches.getCache(CacheConfig.TIO_ACCESS_TOKEN);
        cache2.put(request.getHttpSession().getId(), xxxx);
        HttpResponse ret = Resps.json(request, Resp.ok(zzz));
        ICache cache3 = Caches.getCache(CacheConfig.TIO_ACCESSTOKEN_USERAGENT);
        cache3.put(xxxx, request.getUserAgent());
        return ret;
    }

    private static String getKey1(HttpRequest request, RequestExt requestExt) {
        boolean isFromAndroid = requestExt.isFromAppAndroid();
        if (isFromAndroid) {
            return androidKey1;
        }
        boolean isFromPc = requestExt.isFromBrowser();
        if (isFromPc) {
            return pcKey1;
        }
        boolean isFromIos = requestExt.isFromAppIos();
        if (isFromIos) {
            return iosKey1;
        }
        Tio.remove(request.getChannelContext(), "这个http请求不来自pc、ios、android任何一方");
        return null;
    }

    private static String getKey2(HttpRequest request, RequestExt requestExt) {
        boolean isFromAndroid = requestExt.isFromAppAndroid();
        if (isFromAndroid) {
            return androidKey2;
        }
        boolean isFromPc = requestExt.isFromBrowser();
        if (isFromPc) {
            return pcKey2;
        }
        boolean isFromIos = requestExt.isFromAppIos();
        if (isFromIos) {
            return iosKey2;
        }
        Tio.remove(request.getChannelContext(), "这个http请求不来自pc、ios、android任何一方");
        return null;
    }

    private static String getKey3(HttpRequest request, RequestExt requestExt) {
        boolean isFromAndroid = requestExt.isFromAppAndroid();
        if (isFromAndroid) {
            return androidKey3;
        }
        boolean isFromPc = requestExt.isFromBrowser();
        if (isFromPc) {
            return pcKey3;
        }
        boolean isFromIos = requestExt.isFromAppIos();
        if (isFromIos) {
            return iosKey3;
        }
        Tio.remove(request.getChannelContext(), "这个http请求不来自pc、ios、android任何一方");
        return null;
    }
}
