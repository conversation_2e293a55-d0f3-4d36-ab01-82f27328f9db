package org.tio.sitexxx.web.server.controller.p004wx;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mysql.cj.jdbc.exceptions.MysqlDataTruncation;
import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.Tio;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.UploadFile;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.jfinal.plugin.activerecord.ActiveRecordException;
import org.tio.jfinal.plugin.activerecord.Db;
import org.tio.jfinal.plugin.activerecord.Page;
import org.tio.jfinal.plugin.activerecord.Record;
import org.tio.sitexxx.im.common.Command;
import org.tio.sitexxx.im.common.ImPacket;
import org.tio.sitexxx.im.common.bs.wx.friend.WxUserOperNtf;
import org.tio.sitexxx.im.common.bs.wx.group.WxGroupOperNtf;
import org.tio.sitexxx.im.server.Ims;
import org.tio.sitexxx.im.server.TioSiteImServerStarter;
import org.tio.sitexxx.im.server.handler.wx.WxChatApi;
import org.tio.sitexxx.im.server.handler.wx.WxSynApi;
import org.tio.sitexxx.service.cache.CacheConfig;
import org.tio.sitexxx.service.cache.Caches;
import org.tio.sitexxx.service.model.main.Audio;
import org.tio.sitexxx.service.model.main.File;
import org.tio.sitexxx.service.model.main.Img;
import org.tio.sitexxx.service.model.main.IpInfo;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.UserVipLevel;
import org.tio.sitexxx.service.model.main.Video;
import org.tio.sitexxx.service.model.main.WxChatGroupItem;
import org.tio.sitexxx.service.model.main.WxChatItems;
import org.tio.sitexxx.service.model.main.WxChatUserItem;
import org.tio.sitexxx.service.model.main.WxFriend;
import org.tio.sitexxx.service.model.main.WxFriendApplyItems;
import org.tio.sitexxx.service.model.main.WxFriendMsg;
import org.tio.sitexxx.service.model.main.WxGroup;
import org.tio.sitexxx.service.model.main.WxGroupApply;
import org.tio.sitexxx.service.model.main.WxGroupMsg;
import org.tio.sitexxx.service.model.main.WxGroupUser;
import org.tio.sitexxx.service.model.main.WxReadAck;
import org.tio.sitexxx.service.model.stat.GroupStat;
import org.tio.sitexxx.service.service.ImgService;
import org.tio.sitexxx.service.service.VideoService;
import org.tio.sitexxx.service.service.base.IpInfoService;
import org.tio.sitexxx.service.service.base.UserRoleService;
import org.tio.sitexxx.service.service.base.UserService;
import org.tio.sitexxx.service.service.base.UserVipLevelService;
import org.tio.sitexxx.service.service.chat.ChatIndexService;
import org.tio.sitexxx.service.service.chat.ChatMsgService;
import org.tio.sitexxx.service.service.chat.ChatService;
import org.tio.sitexxx.service.service.chat.FriendApplyService;
import org.tio.sitexxx.service.service.chat.FriendService;
import org.tio.sitexxx.service.service.chat.GroupService;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.service.utils.RetUtils;
import org.tio.sitexxx.servicecommon.utils.PeriodUtils;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.sitexxx.servicecommon.vo.Devicetype;
import org.tio.sitexxx.servicecommon.vo.MulLauUtils;
import org.tio.sitexxx.servicecommon.vo.wx.SysMsgVo;
import org.tio.sitexxx.servicecommon.vo.wx.WxForbiddenVo;
import org.tio.sitexxx.web.server.controller.base.AccessTokenController;
import org.tio.sitexxx.web.server.recharge.IRechargeProvider;
import org.tio.sitexxx.web.server.utils.ImgUtils;
import org.tio.sitexxx.web.server.utils.UploadUtils;
import org.tio.sitexxx.web.server.utils.VideoUtils;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.sitexxx.web.server.utils.WxGroupAvatarUtil;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.Constants;
import org.tio.utils.Threads;
import org.tio.utils.cache.CacheUtils;
import org.tio.utils.cache.FirsthandCreater;
import org.tio.utils.json.Json;
import org.tio.utils.resp.Resp;

@RequestPath("/chat")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/wx/ChatController.class */
public class ChatController {
    private static Logger log = LoggerFactory.getLogger(ChatController.class);
    private static final FriendService friendService = FriendService.me;
    private static final ChatService chatService = ChatService.me;
    private static final GroupService groupService = GroupService.me;

    @RequestPath("/list")
    public Resp list(HttpRequest request) throws Exception {
        User curr = WebUtils.currUser(request);
        Byte devicetype = Byte.valueOf(WebUtils.getRequestExt(request).getDeviceType());
        Ret ret = chatService.chatItemList(curr, devicetype);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }

    @RequestPath("/chatInfo")
    @Deprecated
    public Resp chatInfo(HttpRequest request, Long chatlinkid) throws Exception {
        User curr = WebUtils.currUser(request);
        if (chatlinkid == null) {
            return Resp.fail("会话id参数为空");
        }
        if (chatlinkid.longValue() <= 0) {
            WxChatGroupItem groupItem = ChatIndexService.chatGroupIndex(curr.getId(), Long.valueOf(-chatlinkid.longValue()));
            if (groupItem == null || groupItem.getChatlinkid() == null) {
                return Resp.fail("会话id为空");
            }
            chatlinkid = groupItem.getChatlinkid();
        }
        WxChatItems chatItems = chatService.getBaseChatItems(chatlinkid);
        if (chatItems == null) {
            return Resp.fail("会话不存在");
        }
        if (!Objects.equals(curr.getId(), chatItems.getUid())) {
            log.error("异常登录请求会话信息：登录uid：{}，请求会话的uid：{}", curr.getId(), chatItems.getUid());
            return Resp.fail("权限不足");
        }
        return Resp.ok(chatItems);
    }

    @RequestPath("/group")
    public Resp group(HttpRequest request, Long groupid, Byte userflag) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = groupService.groupInfo(groupid, Objects.equals(userflag, (byte) 1) ? curr.getId() : null);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(ret);
    }

    @RequestPath("/updateGroupAvatar/{groupId}")
    public Resp updateGroupAvatar(final HttpRequest request, final String groupId, UploadFile uploadFile) {
        if (uploadFile == null) {
            return Resp.fail("上传信息为空");
        }
        final User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail("您尚未登录或登录超时").code(1001);
        }
        WxGroupUser groupUser = GroupService.me.getGroupUser(curr.getId(), Long.valueOf(Long.parseLong(groupId)));
        if (groupUser.getGrouprole().byteValue() != 1 && groupUser.getGrouprole().byteValue() != 3) {
            return Resp.fail("权限不足");
        }
        byte[] imageBytes = uploadFile.getData();
        if (!UserService.isSuper(curr) && !UserRoleService.hasRole(curr, (byte) 6)) {
            int maxsize = ConfService.getInt("user.upload.avatar.maxsize", 2048).intValue();
            if (imageBytes.length > 1024 * maxsize) {
                return Resp.fail("文件尺寸太大");
            }
        }
        try {
            final Img img = ImgUtils.processImg("user/avatar", curr.getId(), uploadFile, 1.0f);
            img.setComefrom((byte) 3);
            img.setStatus((byte) 1);
            img.setSession(request.getHttpSession().getId());
            boolean f = ImgService.me.save(img);
            if (!f) {
                return Resp.fail("保存失败");
            }
            Ret ret = groupService.modifyAvatar(Long.valueOf(Long.parseLong(groupId)), img.getUrl(), false);
            Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.1
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        Ret ret2 = WxChatApi.updateGroupInfoAvatar(request, curr, Long.valueOf(Long.parseLong(groupId)), img.getUrl());
                        if (ret2.isFail()) {
                            ChatController.log.error(RetUtils.getRetMsg(ret2));
                        }
                    } catch (Exception e) {
                        ChatController.log.error("", e);
                    }
                }
            });
            return ret.isOk() ? Resp.ok() : Resp.fail();
        } catch (Exception e) {
            e.printStackTrace();
            return Resp.fail(e.getMessage());
        }
    }

    @RequestPath("/isFriend")
    public Resp isFriend(HttpRequest request, Integer touid) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = friendService.isFriend(curr, touid);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/applyList")
    public Resp applyList(HttpRequest request) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = FriendApplyService.me.applyList(curr.getId());
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }

    @RequestPath("/applyData")
    public Resp applyData(HttpRequest request) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = FriendApplyService.me.applyData(curr.getId());
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/applyInfo")
    public Resp applyInfo(HttpRequest request, Integer applyid) throws Exception {
        Ret ret = FriendApplyService.me.applyInfo(applyid);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Record record = (Record) RetUtils.getOkTData(ret);
        if (record == null) {
            return Resp.fail().msg("申请记录不存在");
        }
        return Resp.ok(record);
    }

    @RequestPath("/mailList")
    public Resp mailList(HttpRequest request, Byte mode, String searchkey, Integer pageNumber) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = chatService.mailList(curr, mode, searchkey, pageNumber);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/applyGroupFdList")
    public Resp applyGroupFdList(HttpRequest request, String searchkey, Long groupid) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = friendService.getOutGroupFdList(curr, searchkey, groupid);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/forbidden")
    public Resp forbidden(final HttpRequest request, final WxForbiddenVo forbiddenVo) throws Exception {
        final User curr = WebUtils.currUser(request);
        if (forbiddenVo == null || forbiddenVo.checkIsNull()) {
            return Resp.fail("无效参数");
        }
        Ret ret = groupService.forbidden(forbiddenVo, curr.getId());
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.2
            @Override // java.lang.Runnable
            public void run() {
                try {
                    WxChatApi.sendTimeTipMsg(forbiddenVo.getGroupid(), (Integer) null, curr.getId());
                    if (Objects.equals(forbiddenVo.getMode(), (byte) 4)) {
                        String groupText = MulLauUtils.getMsg("本群已禁言");
                        if (Objects.equals(forbiddenVo.getOper(), (byte) 2)) {
                            groupText = MulLauUtils.getMsg("本群已解除禁言");
                        }
                        WxChatApi.sendGroupMsgEach(request, groupText, (byte) 1, curr.getId(), forbiddenVo.getGroupid(), (byte) 1, (SysMsgVo) null);
                    } else {
                        User user = UserService.ME.getById(forbiddenVo.getUid());
                        SysMsgVo sysMsgVo = new SysMsgVo(curr.getNick(), ChatMsgService.MsgTemplate.cancelforbidden, user.getNick(), "cancelforbidden");
                        if (!Objects.equals(forbiddenVo.getOper(), (byte) 2)) {
                            sysMsgVo = new SysMsgVo(curr.getNick(), ChatMsgService.MsgTemplate.forbidden, user.getNick(), "forbidden");
                        }
                        WxChatApi.sendGroupMsgEach(request, sysMsgVo.toText(), (byte) 1, curr.getId(), forbiddenVo.getGroupid(), (byte) 1, sysMsgVo);
                    }
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        return Resp.ok();
    }

    @RequestPath("/setGroupManager")
    public Resp groupManager(final HttpRequest request, final Integer toUid, final Integer groupId, final Integer flag) {
        final User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail("用户未登录或登录超时");
        }
        if (toUid == null || groupId == null || flag == null) {
            return Resp.fail("缺少参数");
        }
        if (flag.intValue() < 2 || flag.intValue() > 3) {
            return Resp.fail("参数不合法");
        }
        Ret ret = groupService.groupManager(curr.getId(), toUid, groupId, flag);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.3
            @Override // java.lang.Runnable
            public void run() {
                try {
                    User user = UserService.ME.getById(toUid);
                    WxChatApi.sendTimeTipMsg(Long.valueOf(groupId.longValue()), (Integer) null, curr.getId());
                    SysMsgVo sysMsgVo = new SysMsgVo(curr.getNick(), flag.byteValue() == 3 ? ChatMsgService.MsgTemplate.setmanager : ChatMsgService.MsgTemplate.canclemanager, user.getNick(), flag.byteValue() == 3 ? "setManager" : "cancleManager");
                    WxChatApi.sendGroupMsgEach(request, sysMsgVo.toText(), (byte) 1, curr.getId(), Long.valueOf(groupId.longValue()), (byte) 1, sysMsgVo, String.valueOf(toUid));
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        return Resp.ok(MulLauUtils.getMsg("操作成功"));
    }

    @RequestPath("/forbiddenFlag")
    public Resp forbiddenFlag(HttpRequest request, Integer uid, Long groupid) throws Exception {
        User curr = WebUtils.currUser(request);
        Map<String, String> flagMap = new HashMap<>();
        flagMap.put("grant", "2");
        flagMap.put("rolegrant", "2");
        flagMap.put("kickgrant", "2");
        if (Objects.equals(uid, curr.getId())) {
            return Resp.ok(flagMap);
        }
        WxGroup group = GroupService.me.getByGroupid(groupid);
        if (group == null) {
            return Resp.ok(flagMap);
        }
        WxChatGroupItem groupItem = ChatIndexService.chatGroupIndex(curr.getId(), groupid);
        if (!ChatService.groupChatLink(groupItem)) {
            return Resp.ok(flagMap);
        }
        WxGroupUser groupUser = GroupService.me.getGroupUser(groupItem.getGpulinkid());
        if (Objects.equals(groupUser.getGrouprole(), (byte) 2)) {
            return Resp.ok(flagMap);
        }
        WxChatGroupItem toGroupItem = ChatIndexService.chatGroupIndex(uid, groupid);
        if (!ChatService.groupChatLink(toGroupItem)) {
            return Resp.ok(flagMap);
        }
        WxGroupUser toGroupUser = GroupService.me.getGroupUser(toGroupItem.getGpulinkid());
        if (Objects.equals(groupUser.getGrouprole(), (byte) 1)) {
            flagMap.put("grouprole", toGroupUser.getGrouprole() + "");
            flagMap.put("rolegrant", "1");
        } else if (!Objects.equals(toGroupUser.getGrouprole(), (byte) 2)) {
            return Resp.ok(flagMap);
        }
        flagMap.put("grant", "1");
        flagMap.put("flag", toGroupUser.getForbiddenflag() + "");
        flagMap.put("kickgrant", "1");
        return Resp.ok(flagMap);
    }

    @RequestPath("/groupUserList")
    public Resp groupUserList(HttpRequest request, Long groupid, Integer pageNumber, String searchkey) throws Exception {
        Ret ret = groupService.groupUserList(groupid, pageNumber, searchkey);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/forbiddenUserList")
    public Resp forbiddenUserList(HttpRequest request, Long groupid, Integer pageNumber, String searchkey) throws Exception {
        Ret ret = groupService.forbiddenUserList(groupid, pageNumber, searchkey);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/atGroupUserList")
    public Resp atGroupUserList(HttpRequest request, Long groupid, String searchkey) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = groupService.atGroupUserList(groupid, searchkey, curr.getId());
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }

    @RequestPath("/p2pMsgList")
    @Deprecated
    public Resp p2pMsgList(HttpRequest request, Integer uid, Long chatlinkid, Long startmid) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = ChatMsgService.me.p2pMsgList(chatlinkid, curr.getId(), startmid, (Long) null);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/groupMsgList")
    @Deprecated
    public Resp groupMsgList(HttpRequest request, Long chatlinkid, Long startmid) throws Exception {
        User curr = WebUtils.currUser(request);
        WxChatGroupItem groupItem = ChatIndexService.chatGroupIndex(curr.getId(), Long.valueOf(Math.abs(chatlinkid.longValue())));
        if (groupItem == null || groupItem.getChatlinkid() == null) {
            return Resp.fail("会话为空");
        }
        Long chatlinkid2 = groupItem.getChatlinkid();
        Ret ret = ChatMsgService.me.groupMsgList(chatlinkid2, curr.getId(), startmid, (Long) null);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/joinGroup")
    public Resp joinGroup(final HttpRequest request, final String uids, final Long groupid, final Integer applyuid) throws Exception {
        final User curr = WebUtils.currUser(request);
        final WxGroup group = groupService.getByGroupid(groupid);
        final WxGroupUser groupUser = groupService.getGroupUser(curr.getId(), groupid);
        if (applyuid != null) {
            Byte devicetype = Byte.valueOf(WebUtils.getRequestExt(request).getDeviceType());
            WxGroupUser applyUser = groupService.getGroupUser(applyuid, groupid);
            if (group.getJoinmode().byteValue() == 1 && applyUser.getGrouprole().byteValue() == 2) {
                if (uids.startsWith(Constants.SPE1)) {
                    uids = uids.substring(1);
                }
                String userIdStr = uids.split(Constants.SPE1)[0];
                User byId = UserService.ME.getById(userIdStr);
                final Ret ret = groupService.joinGroupApply(UserService.ME.getById(applyuid), groupid, uids, byId.getNick() + (uids.split(Constants.SPE1).length > 1 ? "等" + uids.split(Constants.SPE1).length + "人" : "") + "申请进群");
                if (ret.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(ret));
                }
                Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.4
                    @Override // java.lang.Runnable
                    public void run() {
                        try {
                            WxGroupApply apply = (WxGroupApply) RetUtils.getOkTData(ret, "apply");
                            Integer count = (Integer) RetUtils.getOkTData(ret);
                            WxGroup group2 = (WxGroup) RetUtils.getOkTData(ret, "group");
                            Ret ret2 = WxChatApi.joinGroupApply(request, UserService.ME.getById(applyuid), group2, count, apply);
                            if (ret2.isFail()) {
                                ChatController.log.error(RetUtils.getRetMsg(ret2));
                            }
                        } catch (Exception e) {
                            ChatController.log.error("", e);
                        }
                    }
                });
                return Resp.ok("已发送申请，等待审核");
            }
            Ret ret2 = groupService.joinGroup(curr, groupid, uids, applyuid);
            if (ret2.isFail()) {
                return Resp.fail(RetUtils.getRetMsg(ret2));
            }
            Short joinnum = (Short) RetUtils.getOkTData(ret2, "joinnum");
            Byte nameUpdate = (Byte) RetUtils.getOkTData(ret2, "nameupdate");
            if (joinnum.shortValue() != 0) {
                User msgUser = UserService.ME.getById(applyuid);
                boolean auot = auotUpdateGroupInfo(request, devicetype, groupid, nameUpdate, joinnum, msgUser);
                WxChatApi.joinGroup(request, msgUser, groupid, uids, (String) RetUtils.getOkTData(ret2), (List) RetUtils.getOkTData(ret2, "rebind"), auot);
            }
        } else {
            if (group.getJoinmode().byteValue() == 1 && groupUser.getGrouprole().byteValue() == 2) {
                if (uids.startsWith(Constants.SPE1)) {
                    uids = uids.substring(1);
                }
                String userIdStr2 = uids.split(Constants.SPE1)[0];
                User byId2 = UserService.ME.getById(userIdStr2);
                final Ret ret3 = groupService.joinGroupApply(curr, groupid, uids, byId2.getNick() + (uids.split(Constants.SPE1).length > 1 ? "等" + uids.split(Constants.SPE1).length + "人" : "") + "申请进群");
                if (ret3.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(ret3));
                }
                Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.5
                    @Override // java.lang.Runnable
                    public void run() {
                        try {
                            WxGroupApply apply = (WxGroupApply) RetUtils.getOkTData(ret3, "apply");
                            Integer count = (Integer) RetUtils.getOkTData(ret3);
                            WxGroup group2 = (WxGroup) RetUtils.getOkTData(ret3, "group");
                            Ret ret4 = WxChatApi.joinGroupApply(request, curr, group2, count, apply);
                            if (ret4.isFail()) {
                                ChatController.log.error(RetUtils.getRetMsg(ret4));
                            }
                        } catch (Exception e) {
                            ChatController.log.error("", e);
                        }
                    }
                });
                return Resp.ok(RetUtils.OPER_RIGHT);
            }
            Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.6
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        if (group == null) {
                            return;
                        }
                        if (group.getJoinmode().byteValue() == 1 && groupUser.getGrouprole().byteValue() == 2) {
                            Resp resp = ChatController.this.joinGroupApply(request, uids, groupid, "邀请进群");
                            WxChatApi.sendFriendErrorMsg(request, curr.getId(), curr.getId(), curr.getId(), Long.valueOf(-groupid.longValue()), 30001, resp.getMsg());
                            return;
                        }
                        Byte devicetype2 = Byte.valueOf(WebUtils.getRequestExt(request).getDeviceType());
                        Ret ret4 = ChatController.groupService.joinGroup(curr, groupid, uids, applyuid);
                        if (ret4.isFail()) {
                            WxChatApi.sendFriendErrorMsg(request, curr.getId(), curr.getId(), curr.getId(), Long.valueOf(-groupid.longValue()), 30001, RetUtils.getRetMsg(ret4));
                            return;
                        }
                        Short joinnum2 = (Short) RetUtils.getOkTData(ret4, "joinnum");
                        Byte nameUpdate2 = (Byte) RetUtils.getOkTData(ret4, "nameupdate");
                        if (joinnum2 != null && joinnum2.shortValue() != 0) {
                            User msgUser2 = curr;
                            boolean auot2 = ChatController.auotUpdateGroupInfo(request, devicetype2, groupid, nameUpdate2, joinnum2, msgUser2);
                            WxChatApi.joinGroup(request, msgUser2, groupid, uids, (String) RetUtils.getOkTData(ret4), (List) RetUtils.getOkTData(ret4, "rebind"), auot2);
                        }
                    } catch (Exception e) {
                        ChatController.log.error("", e);
                    }
                }
            });
        }
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/joinGroupApply")
    public Resp joinGroupApply(final HttpRequest request, String uids, Long groupid, String applymsg) throws Exception {
        final User curr = WebUtils.currUser(request);
        Ret checkRet = groupService.checkJoinGroup(curr.getId(), groupid, (Integer) null, (Date) null);
        if (checkRet.isOk()) {
            return joinGroup(request, uids, groupid, null);
        }
        final Ret ret = groupService.joinGroupApply(curr, groupid, uids, applymsg);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.7
            @Override // java.lang.Runnable
            public void run() {
                try {
                    WxGroupApply apply = (WxGroupApply) RetUtils.getOkTData(ret, "apply");
                    Integer count = (Integer) RetUtils.getOkTData(ret);
                    WxGroup group = (WxGroup) RetUtils.getOkTData(ret, "group");
                    Ret ret2 = WxChatApi.joinGroupApply(request, curr, group, count, apply);
                    if (ret2.isFail()) {
                        ChatController.log.error(RetUtils.getRetMsg(ret2));
                    }
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/groupApplyInfo")
    public Resp groupApplyInfo(HttpRequest request, Integer aid) throws Exception {
        Ret ret = groupService.groupApplyInfo(aid);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/dealGroupApply")
    public Resp dealGroupApply(HttpRequest request, Integer aid, Long mid) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = groupService.dealGroupApply(curr, aid, mid);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        Byte dealflag = (Byte) RetUtils.getOkTData(ret);
        if (Objects.equals(dealflag, (byte) 2)) {
            WxGroupApply apply = (WxGroupApply) RetUtils.getOkTData(ret, "apply");
            String uids = (String) RetUtils.getOkTData(ret, "uids");
            return joinGroup(request, uids, apply.getGroupid(), null);
        }
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/modifyApply")
    public Resp modifyApply(final HttpRequest request, final Long groupid, Byte mode) {
        final User curr = WebUtils.currUser(request);
        final SysMsgVo sysMsgVo = new SysMsgVo(curr.getNick(), "", "", "");
        switch (mode.byteValue()) {
            case IRechargeProvider.CallType.RETURN /* 1 */:
                String text = ChatMsgService.MsgTemplate.applyopen;
                sysMsgVo.setMsgbody(text);
                sysMsgVo.setMsgkey("applyopen");
                break;
            case IRechargeProvider.CallType.NOTIFY /* 2 */:
                String text2 = ChatMsgService.MsgTemplate.applyclose;
                sysMsgVo.setMsgbody(text2);
                sysMsgVo.setMsgkey("applyclose");
                break;
            default:
                return Resp.fail().msg("无效入群方式");
        }
        Ret ret = groupService.modifyApply(curr, groupid, mode);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.8
            @Override // java.lang.Runnable
            public void run() {
                try {
                    WxChatApi.sendTimeTipMsg(groupid, (Integer) null, curr.getId());
                    WxChatApi.sendGroupMsgEach(request, sysMsgVo.toText(), (byte) 1, curr.getId(), groupid, (byte) 1, sysMsgVo);
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/modifyGroupNick")
    public Resp modifyGroupNick(HttpRequest request, final Long groupid, String nick) {
        final User curr = WebUtils.currUser(request);
        final Ret ret = groupService.modifyGroupNick(groupid, nick, curr.getId());
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.9
            @Override // java.lang.Runnable
            public void run() {
                try {
                    WxChatApi.sendTimeTipMsg(groupid, (Integer) null, curr.getId());
                    WxChatApi.synGroupInfo(curr.getId(), groupid, (String) RetUtils.getOkTData(ret));
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/msgContent")
    public Resp msgContent(HttpRequest request, byte chatMode, String msgIds) {
        if (StrUtil.isBlank(msgIds)) {
            return Resp.fail("消息ID为空");
        }
        return Resp.ok(getMsgListContent(chatMode, msgIds, false));
    }

    private JSONArray getMsgListContent(final byte chatMode, final String msgIds, final boolean simple) {
        String key = "msg_query_" + ((int) chatMode) + msgIds + simple;
        JSONArray result = CacheUtils.get(Caches.getCache(CacheConfig.MSG_QUERY), key, true, new FirsthandCreater<JSONArray>() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.10
            /* renamed from: create, reason: merged with bridge method [inline-methods] */
            public JSONArray m49create() {
                JSONArray jsonArray = new JSONArray();
                for (String str : msgIds.split(Constants.SPE1)) {
                    if (Objects.equals(Byte.valueOf(chatMode), (byte) 1)) {
                        WxFriendMsg byId = WxFriendMsg.dao.findById(Long.valueOf(Long.parseLong(str)));
                        if (byId != null) {
                            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(byId));
                            if (simple) {
                                jsonObject.remove("c");
                                jsonObject.remove("srctext");
                                jsonObject.remove("resume");
                                jsonObject.remove("session");
                            }
                            JSONObject jsonObject2 = JSONObject.parseObject(JSON.toJSONString(byId));
                            User user = UserService.ME.getById(byId.getUid());
                            jsonObject2.put("avatar", user.getAvatar());
                            jsonObject2.put("nick", user.getNick());
                            jsonArray.add(jsonObject2);
                        }
                    } else {
                        WxGroupMsg byId2 = WxGroupMsg.dao.findById(Long.valueOf(Long.parseLong(str)));
                        if (byId2 != null) {
                            JSONObject jsonObject3 = JSONObject.parseObject(JSON.toJSONString(byId2));
                            if (simple) {
                                jsonObject3.remove("c");
                                jsonObject3.remove("srctext");
                                jsonObject3.remove("resume");
                                jsonObject3.remove("session");
                            }
                            jsonArray.add(jsonObject3);
                        }
                    }
                }
                return jsonArray;
            }
        });
        return result;
    }

    @RequestPath("/transSend")
    public Resp transSend(HttpRequest request, byte chatMode, String bizid, String msgIds, Long chatlinkid) throws Exception {
        String title;
        if (StrUtil.isBlank(msgIds)) {
            return Resp.fail("消息为空");
        }
        String msgIds2 = msgIds.trim();
        if (msgIds2.startsWith(Constants.SPE1) || msgIds2.endsWith(Constants.SPE1)) {
            return Resp.fail("消息格式不正确");
        }
        User curr = WebUtils.currUser(request);
        if (chatlinkid.longValue() <= 0) {
            Ret check = GroupService.checkGroupMsg(Long.valueOf(-chatlinkid.longValue()), curr.getId());
            if (check.isFail()) {
                return Resp.fail(RetUtils.getRetMsg(check));
            }
            chatlinkid = (Long) RetUtils.getOkTData(check);
        }
        WxChatItems chatItems = chatService.getBaseChatItems(chatlinkid);
        if (chatItems == null) {
            return Resp.fail("无效参数");
        }
        JSONObject jsonObject = new JSONObject();
        if (Objects.equals(Byte.valueOf(chatMode), (byte) 1)) {
            User byId = UserService.ME.getById(bizid);
            if (byId == null) {
                return Resp.fail("聊天UID不存在");
            }
            title = byId.getNick() + "与" + curr.getNick() + "的聊天记录";
        } else {
            WxGroup wxGroup = WxGroup.dao.findById(Long.valueOf(Long.parseLong(bizid)));
            if (wxGroup == null) {
                return Resp.fail("群不存在");
            }
            title = wxGroup.getName() + "的聊天记录";
        }
        List msgListContent = getMsgListContent(chatMode, msgIds2, true);
        jsonObject.put("fromChatMode", Byte.valueOf(chatMode));
        jsonObject.put("fromBizId", bizid);
        jsonObject.put("msgIds", msgIds2);
        jsonObject.put("title", title);
        jsonObject.put("content", msgListContent.size() > 3 ? msgListContent.subList(0, 3) : msgListContent);
        String text = jsonObject.toJSONString();
        try {
            if (Objects.equals(chatItems.getChatmode(), (byte) 1)) {
                WxChatApi.sendTimeTipMsg((Long) null, Integer.valueOf(chatItems.getBizid().intValue()), curr.getId());
                Ret ret = WxChatApi.sendFdMsgEach(request, text, (byte) 17, chatItems.getUid(), Integer.valueOf(chatItems.getBizid().intValue()), (byte) 2);
                if (ret.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(ret));
                }
                return Resp.ok(RetUtils.OPER_RIGHT);
            }
            WxChatApi.sendTimeTipMsg(chatItems.getBizid(), (Integer) null, curr.getId());
            Ret ret2 = WxChatApi.sendGroupMsgEach(request, text, (byte) 17, chatItems.getUid(), chatItems.getBizid(), (byte) 2, (SysMsgVo) null);
            if (ret2.isFail()) {
                return Resp.fail(RetUtils.getRetMsg(ret2));
            }
            return Resp.ok(RetUtils.OPER_RIGHT);
        } catch (ActiveRecordException | MysqlDataTruncation e) {
            if (e.getMessage() != null && e.getMessage().contains("Data too long")) {
                return Resp.fail("该消息转发次数受限");
            }
            e.printStackTrace();
            return Resp.ok();
        }
    }

    @RequestPath("/leaveGroup")
    public Resp leaveGroup(final HttpRequest request, Integer uid, final Long groupid) throws Exception {
        final User curr = WebUtils.currUser(request);
        final Ret ret = groupService.leaveGroup(curr, groupid);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        final Byte nameUpdate = (Byte) RetUtils.getOkTData(ret, "nameupdate");
        Tio.unbindGroup(TioSiteImServerStarter.serverTioConfigWs, curr.getId() + "", groupid + "");
        WxGroupOperNtf leaveNtf = new WxGroupOperNtf();
        leaveNtf.setC("自动退群");
        leaveNtf.setMid((Long) null);
        leaveNtf.setT(Long.valueOf(System.currentTimeMillis()));
        leaveNtf.setUid(curr.getId());
        leaveNtf.setG(groupid);
        leaveNtf.setChatlinkid(Long.valueOf(-groupid.longValue()));
        leaveNtf.setOper((byte) 5);
        ImPacket imPacket = new ImPacket(Command.WxGroupOperNtf, Json.toJson(leaveNtf));
        Ims.sendToUser(curr.getId(), imPacket);
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.11
            @Override // java.lang.Runnable
            public void run() {
                try {
                    Byte devicetype = Byte.valueOf(WebUtils.getRequestExt(request).getDeviceType());
                    WxChatGroupItem newowner = (WxChatGroupItem) RetUtils.getOkTData(ret, "owner");
                    WxChatGroupItem leave = (WxChatGroupItem) RetUtils.getOkTData(ret, "leave");
                    boolean auto = ChatController.auotUpdateGroupInfo(request, devicetype, groupid, nameUpdate, (short) -1, curr);
                    WxChatApi.leaveGroup(request, curr, leave, newowner, auto);
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/kickGroup")
    public Resp kickGroup(final HttpRequest request, String uids, final Long groupid) throws Exception {
        final User curr = WebUtils.currUser(request);
        final Ret ret = groupService.kickGroup(curr, groupid, uids);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        String uids2 = uids;
        if (uids.startsWith(Constants.SPE1)) {
            uids2 = uids.substring(1);
        } else if (uids.endsWith(Constants.SPE1)) {
            uids2 = uids.substring(0, uids.length() - 1);
        }
        final Short joinnum = (Short) RetUtils.getOkTData(ret, "joinnum");
        final Byte nameUpdate = (Byte) RetUtils.getOkTData(ret, "nameupdate");
        if (joinnum.shortValue() != 0) {
            Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.12
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        Byte devicetype = Byte.valueOf(WebUtils.getRequestExt(request).getDeviceType());
                        List<WxChatGroupItem> kickGroupItem = (List) RetUtils.getOkTData(ret, "kick");
                        String nickStr = (String) RetUtils.getOkTData(ret);
                        boolean auot = ChatController.auotUpdateGroupInfo(request, devicetype, groupid, nameUpdate, joinnum, curr);
                        WxChatApi.kickGroup(request, curr, groupid, kickGroupItem, nickStr, auot);
                    } catch (Exception e) {
                        ChatController.log.error("", e);
                    }
                }
            });
        }
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/createGroup")
    public Resp createGroup(final HttpRequest request, final WxGroup wxGroup, final String uidList) throws Exception {
        final User curr = WebUtils.currUser(request);
        if (Const.MILSERVER) {
            System.out.println("vipLevel===>" + curr.getViplevel());
            UserVipLevel userVipLevel = UserVipLevelService.ME.getByLevel(curr.getViplevel());
            if (userVipLevel.getMaxCreateGroup().intValue() < 1) {
                return Resp.fail("建群上限");
            }
        }
        String name = "";
        if (StrUtil.isNotBlank(wxGroup.getName())) {
            String name2 = wxGroup.getName();
            name = name2.length() > 30 ? name2.substring(0, 30) : name2;
            wxGroup.setName(name);
            wxGroup.setAutoflag((byte) 2);
        }
        String[] uidArr = StrUtil.split(uidList, Constants.SPE1);
        Ret imgRet = getGroupImg(uidArr, curr, name);
        Img img = (Img) RetUtils.getOkTData(imgRet, "img");
        final String nicks = (String) RetUtils.getOkTData(imgRet, "nicks");
        if (StrUtil.isBlank(name)) {
            String name3 = (String) RetUtils.getOkTData(imgRet, "name");
            wxGroup.setName(name3);
            wxGroup.setAutoflag((byte) 1);
        }
        wxGroup.setAvatar(img.getCoverurl());
        final Byte devicetype = Byte.valueOf(WebUtils.getRequestExt(request).getDeviceType());
        String sessionid = request.getHttpSession().getId();
        String ip = request.getClientIp();
        short joinnum = uidArr != null ? (short) (uidArr.length + 1) : (short) 1;
        wxGroup.setCreatetime(new Date());
        final Ret ret = groupService.createGroup(curr, wxGroup, nicks, devicetype, sessionid, ip, joinnum, WebUtils.getRequestExt(request).getAppVersion());
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.13
            @Override // java.lang.Runnable
            public void run() {
                try {
                    WxGroupMsg msg = (WxGroupMsg) RetUtils.getOkTData(ret, "msg");
                    SysMsgVo sysMsgVo = (SysMsgVo) RetUtils.getOkTData(ret, "sysmsgvo");
                    WxChatApi.creatGroupOwner(request, devicetype, curr, wxGroup, msg, sysMsgVo);
                    WxChatApi.creatGroup(request, curr, uidList, nicks, wxGroup, msg, sysMsgVo);
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.14
            @Override // java.lang.Runnable
            public void run() {
                try {
                    String dayperiod = PeriodUtils.dateToPeriodByType(wxGroup.getCreatetime(), (byte) 1);
                    if (GroupStat.dao.findFirst("select * from group_stat where dayperiod = ? and type = ?", new Object[]{dayperiod, (byte) 1}) == null) {
                        GroupStat groupStat = new GroupStat();
                        groupStat.setAddcount(1);
                        groupStat.setDayperiod(dayperiod);
                        groupStat.setType((byte) 1);
                        groupStat.ignoreSave();
                    } else {
                        Db.use("tio_site_stat").update("update group_stat set addcount = addcount + 1 where dayperiod = ? and type = ?", new Object[]{dayperiod, (byte) 1});
                    }
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        return Resp.ok(wxGroup);
    }

    @RequestPath("/friendApply")
    public Resp friendApply(final HttpRequest request, final Integer touid, String greet) throws Exception {
        final User curr = WebUtils.currUser(request);
        if (Const.MILSERVER) {
            UserVipLevel userVipLevel = UserVipLevelService.ME.getByLevel(curr.getViplevel());
            if (userVipLevel.getMaxAddFriend().intValue() < 1) {
                return Resp.fail("好友上限");
            }
        }
        Ret ret = friendService.addApply(curr, touid, greet);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        final WxFriendApplyItems applyItems = (WxFriendApplyItems) RetUtils.getOkTData(ret);
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.15
            @Override // java.lang.Runnable
            public void run() {
                try {
                    if (WxSynApi.isSynVersion()) {
                        WxSynApi.synFriendApply(touid, applyItems, (byte) 1);
                    } else {
                        WxChatApi.useSysChatNtf(request, touid, (byte) 30, curr.getNick() + " 想要成为你的好友：" + applyItems.getGreet(), applyItems.getId() + "");
                    }
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        return Resp.ok(applyItems);
    }

    @RequestPath("/addFriend")
    public Resp addFriend(final HttpRequest request, Integer touid) throws Exception {
        User curr = WebUtils.currUser(request);
        if (Const.MILSERVER) {
            UserVipLevel userVipLevel = UserVipLevelService.ME.getByLevel(curr.getViplevel());
            if (userVipLevel.getMaxAddFriend().intValue() < 1) {
                return Resp.fail("好友上限!");
            }
        }
        final Ret ret = friendService.addFriend(curr, touid);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.16
            @Override // java.lang.Runnable
            public void run() {
                try {
                    WxFriend friend = (WxFriend) RetUtils.getOkTData(ret, "friend");
                    WxFriend toFriend = (WxFriend) RetUtils.getOkTData(ret, "tofriend");
                    WxChatApi.friendChangeAddNtf(request, friend, toFriend);
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.getOkData(ret));
    }

    public Resp delFriendBothway(HttpRequest request, Integer touid, boolean isManagerDel) {
        return null;
    }

    @RequestPath("/delFriend")
    public Resp delFriend(HttpRequest request, Integer touid, Integer isManagerDel) throws Exception {
        Ret ret;
        User curr = WebUtils.currUser(request);
        Integer uid = curr.getId();
        if (Objects.equals(uid, touid)) {
            return Resp.fail("不能删除自己");
        }
        IpInfo ipInfo = IpInfoService.ME.save(request.getClientIp());
        if (Objects.equals(isManagerDel, 1)) {
            ret = friendService.delFriendForManager(curr, touid, ipInfo.getId());
        } else {
            ret = friendService.delFriend(curr, touid, ipInfo.getId());
        }
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Long chatlinkid = (Long) RetUtils.getOkTData(ret, "chatlinkid");
        Long tochatlinkid = (Long) RetUtils.getOkTData(ret, "tochatlinkid");
        if (WxSynApi.isSynVersion()) {
            WxSynApi.synFriendDel(uid, touid, chatlinkid, tochatlinkid, (Long) RetUtils.getOkTData(ret, "fid"));
        } else {
            if (chatlinkid != null) {
                WxChatApi.userChatOper(request, uid, chatlinkid, (byte) 5, "删除好友", "", (WxChatItems) null);
            }
            if (tochatlinkid != null) {
                WxChatApi.userChatOper(request, touid, tochatlinkid, (byte) 6, "被删除好友", "", (WxChatItems) null);
            }
            WxChatApi.friendChangeDelNtf(request, uid, (Long) RetUtils.getOkTData(ret, "fid"));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/delGroup")
    public Resp delGroup(final HttpRequest request, Integer uid, Long groupid) throws Exception {
        final User curr = WebUtils.currUser(request);
        if (!Objects.equals(curr.getId(), uid)) {
            return Resp.fail("登录信息不一致");
        }
        final Ret ret = groupService.delGroup(curr, groupid);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.17
            @Override // java.lang.Runnable
            public void run() {
                try {
                    WxChatGroupItem groupItem = (WxChatGroupItem) RetUtils.getOkTData(ret, "groupitem");
                    WxGroup group = (WxGroup) RetUtils.getOkTData(ret, "group");
                    WxGroupUser groupUser = (WxGroupUser) RetUtils.getOkTData(ret, "groupuser");
                    WxChatApi.delGroup(request, curr, groupItem, group, groupUser);
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.18
            @Override // java.lang.Runnable
            public void run() {
                try {
                    WxGroup group = (WxGroup) RetUtils.getOkTData(ret, "group");
                    String dayperiod = PeriodUtils.dateToPeriodByType(group.getCreatetime(), (byte) 1);
                    if (GroupStat.dao.findFirst("select * from group_stat where dayperiod = ? and type = ?", new Object[]{dayperiod, (byte) 3}) == null) {
                        GroupStat groupStat = new GroupStat();
                        groupStat.setAddcount(1);
                        groupStat.setDayperiod(dayperiod);
                        groupStat.setType((byte) 3);
                        groupStat.ignoreSave();
                    } else {
                        Db.use("tio_site_stat").update("update group_stat set addcount = addcount + 1 where dayperiod = ? and type = ?", new Object[]{dayperiod, (byte) 3});
                    }
                    Db.use("tio_site_stat").update("update group_stat set addcount = addcount - 1 where dayperiod = ? and type = ?", new Object[]{dayperiod, (byte) 1});
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.okOper());
    }

    @RequestPath("/changeOwner")
    public Resp changeOwner(final HttpRequest request, final Long groupid, Integer otheruid) throws Exception {
        final User curr = WebUtils.currUser(request);
        if (Objects.equals(otheruid, curr.getId())) {
            return Resp.fail().msg("不能对自己操作哦");
        }
        User user = UserService.ME.getById(otheruid);
        if (user == null) {
            return Resp.fail().msg("无效转让用户");
        }
        final Ret ret = groupService.changeOwner(curr.getId(), user.getId(), groupid);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.19
            @Override // java.lang.Runnable
            public void run() {
                try {
                    WxChatGroupItem owner = (WxChatGroupItem) RetUtils.getOkTData(ret, "owner");
                    WxChatGroupItem other = (WxChatGroupItem) RetUtils.getOkTData(ret, "other");
                    Byte devicetype = Byte.valueOf(WebUtils.getRequestExt(request).getDeviceType());
                    boolean auot = ChatController.auotUpdateGroupInfo(request, devicetype, groupid, (byte) 1, (short) 0, curr);
                    WxChatApi.changeOwner(request, curr, owner, other, auot);
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.okOper());
    }

    @RequestPath("/oper")
    public Resp oper(final HttpRequest request, Integer touid, final Byte oper, final Long chatlinkid) throws Exception {
        User curr = WebUtils.currUser(request);
        final Integer uid = curr.getId();
        Long group = null;
        if (chatlinkid != null && chatlinkid.longValue() <= 0) {
            group = Long.valueOf(-chatlinkid.longValue());
            WxChatGroupItem groupItem = ChatIndexService.chatGroupIndex(curr.getId(), Long.valueOf(-chatlinkid.longValue()));
            if (groupItem == null || groupItem.getChatlinkid() == null) {
                return Resp.fail("会话id为空");
            }
            chatlinkid = groupItem.getChatlinkid();
        }
        final Ret ret = chatService.chatUserOper(oper, uid, touid, chatlinkid);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        final Long fGroup = group;
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.20
            @Override // java.lang.Runnable
            public void run() {
                try {
                    WxChatItems chatItems = (WxChatItems) RetUtils.getOkTData(ret, "chat");
                    if ((Objects.equals(oper, (byte) 1) || Objects.equals(oper, (byte) 11)) && chatItems != null && Objects.equals(chatItems.getChatmode(), (byte) 2)) {
                        Tio.unbindGroup(TioSiteImServerStarter.serverTioConfigWs, uid + "", chatItems.getBizid() + "");
                    }
                    if (WxSynApi.isSynVersion()) {
                        switch (oper.byteValue()) {
                            case IRechargeProvider.CallType.RETURN /* 1 */:
                                WxChatItems delChatItems = new WxChatItems();
                                delChatItems.setChatlinkid(chatlinkid);
                                WxSynApi.synChatSession(uid, delChatItems, (byte) 4);
                                break;
                            case 8:
                                WxSynApi.synMsgClear(uid, chatlinkid);
                                break;
                            case 11:
                                WxChatItems hidChatItems = new WxChatItems();
                                hidChatItems.setChatlinkid(chatlinkid);
                                WxSynApi.synChatSession(uid, hidChatItems, (byte) 4);
                                break;
                            case 21:
                                WxChatItems topChatItems = new WxChatItems();
                                topChatItems.setChatlinkid(chatlinkid);
                                topChatItems.setTopflag((byte) 1);
                                topChatItems.setChatuptime(new Date());
                                WxSynApi.synChatSession(uid, topChatItems, (byte) 2);
                                break;
                            case 22:
                                WxChatItems cancelTop = new WxChatItems();
                                cancelTop.setChatlinkid(chatlinkid);
                                cancelTop.setTopflag((byte) 2);
                                cancelTop.setChatuptime(new Date());
                                WxSynApi.synChatSession(uid, cancelTop, (byte) 2);
                                break;
                        }
                    } else {
                        String operBizData = "";
                        Long chatlinkid2 = (Long) RetUtils.getOkTData(ret, "chatlinkid");
                        switch (oper.byteValue()) {
                            case IRechargeProvider.CallType.NOTIFY /* 2 */:
                                break;
                            case 3:
                                if (chatlinkid2 != null) {
                                    WxChatItems removeBlack = ChatController.chatService.getAllChatItems(chatlinkid2);
                                    WxChatApi.userChatOper(request, uid, chatlinkid2, oper, "", operBizData, removeBlack);
                                    break;
                                }
                                break;
                            case 21:
                                WxChatItems topChatItems2 = new WxChatItems();
                                topChatItems2.setChatlinkid(chatlinkid);
                                topChatItems2.setTopflag((byte) 1);
                                topChatItems2.setChatuptime(new Date());
                                operBizData = Json.toJson(topChatItems2);
                                break;
                            case 22:
                                WxChatItems cancelTop2 = new WxChatItems();
                                cancelTop2.setChatlinkid(chatlinkid);
                                cancelTop2.setTopflag((byte) 2);
                                cancelTop2.setChatuptime(new Date());
                                operBizData = Json.toJson(cancelTop2);
                                break;
                        }
                        if (Objects.equals(oper, (byte) 11)) {
                            WxChatApi.userChatOper(request, uid, fGroup == null ? (Long) RetUtils.getOkTData(ret, "chatlinkid") : Long.valueOf(-fGroup.longValue()), (byte) 1, "", operBizData, (WxChatItems) null);
                        } else {
                            WxChatApi.userChatOper(request, uid, fGroup == null ? (Long) RetUtils.getOkTData(ret, "chatlinkid") : Long.valueOf(-fGroup.longValue()), oper, "", operBizData, (WxChatItems) null);
                        }
                    }
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/actChat")
    public Resp actChat(final HttpRequest request, Integer touid, Long groupid) throws Exception {
        Ret ret;
        User curr = WebUtils.currUser(request);
        final Integer uid = curr.getId();
        if (groupid == null) {
            if (touid == null) {
                return Resp.fail("无效参数");
            }
            ret = chatService.actFdChatItems(uid, touid);
        } else {
            ret = chatService.actGroupChatItems(groupid, uid);
        }
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Byte act = (Byte) RetUtils.getOkTData(ret, "actflag");
        final Ret sendRet = ret;
        if (act != null && Objects.equals(act, (byte) 1)) {
            Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.21
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        WxChatItems chatItems = (WxChatItems) RetUtils.getOkTData(sendRet, "chat");
                        if (WxSynApi.isSynVersion()) {
                            WxSynApi.synChatSession(uid, chatItems, (byte) 1);
                        } else {
                            WxChatApi.userActOper(request, uid, chatItems);
                        }
                    } catch (Exception e) {
                        ChatController.log.error("", e);
                    }
                }
            });
        }
        return Resp.ok(ret);
    }

    @RequestPath("/dealApply")
    public Resp dealApply(final HttpRequest request, Integer applyid, String remarkname) throws Exception {
        User curr = WebUtils.currUser(request);
        final WxFriendApplyItems items = FriendApplyService.me.getById(applyid);
        if (items == null) {
            return Resp.fail("记录不存在");
        }
        final Ret ret = friendService.dealApply(curr, items, remarkname);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.22
            @Override // java.lang.Runnable
            public void run() {
                try {
                    Integer uid = items.getFromuid();
                    Integer touid = items.getTouid();
                    String send = RetUtils.retKey(ret, "send");
                    if (StrUtil.isNotBlank(send)) {
                        String greet = RetUtils.retKey(ret, "greet");
                        Long tochatlinkid = RetUtils.retLongKey(ret, "tochatlinkid");
                        if (send.equals("double")) {
                            boolean applysigle = ((Boolean) RetUtils.getOkTData(ret, "applysigle")).booleanValue();
                            Long chatlinkid = RetUtils.retLongKey(ret, "chatlinkid");
                            WxChatApi.addFriendEachOfPassApply(request, uid, touid, chatlinkid, tochatlinkid, greet, applysigle);
                        } else {
                            WxChatApi.addFriendSigleOfPassApply(request, uid, touid, tochatlinkid, greet);
                        }
                    }
                    WxFriend friend = (WxFriend) RetUtils.getOkTData(ret, "friend");
                    WxFriend toFriend = (WxFriend) RetUtils.getOkTData(ret, "tofriend");
                    WxChatApi.friendChangeAddNtf(request, friend, toFriend);
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/msgOper")
    public Resp msgOper(final HttpRequest request, Long chatlinkid, final String mids, final Byte oper) throws Exception {
        if (chatlinkid == null) {
            return Resp.fail("会话id参数为空");
        }
        final User curr = WebUtils.currUser(request);
        Byte chatmode = (byte) 1;
        if (chatlinkid.longValue() <= 0) {
            WxChatGroupItem groupItem = ChatIndexService.chatGroupIndex(curr.getId(), Long.valueOf(-chatlinkid.longValue()));
            if (groupItem == null || groupItem.getChatlinkid() == null) {
                return Resp.fail("会话id为空");
            }
            chatlinkid = groupItem.getChatlinkid();
            chatmode = (byte) 2;
        }
        final Ret ret = ChatMsgService.me.msgOper(curr, chatlinkid, oper, mids, chatmode);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        final Long sendChatLinkid = chatlinkid;
        final Byte sendMode = chatmode;
        final WxChatItems chatItems = (WxChatItems) RetUtils.getOkTData(ret, "chatItems");
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.23
            @Override // java.lang.Runnable
            public void run() {
                try {
                    Object msg = RetUtils.getOkTData(ret, "msg");
                    switch (oper.byteValue()) {
                        case IRechargeProvider.CallType.RETURN /* 1 */:
                            WxChatApi.delMsg(request, curr, sendChatLinkid, sendMode, mids, chatItems);
                            break;
                        case AccessTokenController.AccessTokenResp1.RANDOM_LEN /* 9 */:
                            WxChatApi.backMsg(request, curr, sendChatLinkid, sendMode, msg);
                            break;
                    }
                } catch (Exception e) {
                    ChatController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/msgForward")
    public Resp msgForward(HttpRequest request, Long chatlinkid, String mids, String groupids, String uids) throws Exception {
        if (chatlinkid == null) {
            return Resp.fail("会话id参数为空");
        }
        User curr = WebUtils.currUser(request);
        Byte chatmode = (byte) 1;
        if (chatlinkid.longValue() <= 0) {
            WxChatGroupItem groupItem = ChatIndexService.chatGroupIndex(curr.getId(), Long.valueOf(-chatlinkid.longValue()));
            if (groupItem == null || groupItem.getChatlinkid() == null) {
                return Resp.fail("会话id为空");
            }
            chatlinkid = groupItem.getChatlinkid();
            chatmode = (byte) 2;
        }
        Ret ret = WxChatApi.msgForward(request, curr, chatlinkid, chatmode, mids, groupids, uids);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/shareCard")
    public Resp shareCard(HttpRequest request, Long cardid, String groupids, String uids, Byte chatmode) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = WxChatApi.sharChard(request, curr, cardid, chatmode, groupids, uids);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/checkAddFriend")
    public Resp checkAddFriend(HttpRequest request, Integer touid) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = friendService.checkAddFriend(curr, touid);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getCode(ret));
    }

    @RequestPath("/checkCardJoinGroup")
    public Resp checkCardJoinGroup(HttpRequest request, Long groupid, Integer applyuid, Date sendtime) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = groupService.checkJoinGroup(applyuid, groupid, curr.getId(), sendtime);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkTData(ret, "joinflag"));
    }

    @RequestPath("/checkSendCard")
    public Resp checkCard(HttpRequest request, Long groupid) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = groupService.checkSendCard(curr.getId(), groupid);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok();
    }

    @RequestPath("/checkGroupUser")
    public Resp checkGroupUser(HttpRequest request, Long groupid, Integer uid) throws Exception {
        WxChatGroupItem groupItem = ChatIndexService.chatGroupIndex(uid, groupid);
        if (!ChatService.groupChatLink(groupItem)) {
            return Resp.ok((byte) 2).msg("不是群成员");
        }
        return Resp.ok((byte) 1);
    }

    @RequestPath("/readAck")
    public Resp readAck(HttpRequest request, Long chatlinkid) throws Exception {
        User curr = WebUtils.currUser(request);
        if (chatlinkid == null) {
            log.error("消息已读ack异常:会话为空");
            return Resp.fail("ack参数异常");
        }
        if (chatlinkid.longValue() <= 0) {
            return Resp.ok((byte) 1);
        }
        WxChatUserItem userItem = ChatIndexService.chatUserIndex(chatlinkid);
        if (userItem == null) {
            return Resp.fail("ack索引异常");
        }
        WxReadAck readAck = new WxReadAck();
        readAck.setUid(curr.getId());
        readAck.setTouid(Integer.valueOf(userItem.getBizid().intValue()));
        readAck.setChatlinkid(chatlinkid);
        readAck.setType((byte) 1);
        readAck.setDevicetype(Byte.valueOf(WebUtils.getRequestExt(request).getDeviceType()));
        readAck.save();
        return Resp.ok((byte) 1);
    }

    @RequestPath("/file")
    public Resp file(HttpRequest request, UploadFile uploadFile, Long chatlinkid) throws Exception {
        try {
            if (uploadFile == null) {
                return Resp.fail("上传信息为空");
            }
            if (chatlinkid == null) {
                return Resp.fail("会话id参数为空");
            }
            User curr = WebUtils.currUser(request);
            if (chatlinkid.longValue() <= 0) {
                Ret check = GroupService.checkGroupMsg(Long.valueOf(-chatlinkid.longValue()), curr.getId());
                if (check.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(check));
                }
                chatlinkid = (Long) RetUtils.getOkTData(check);
            }
            WxChatItems chatItems = chatService.getBaseChatItems(chatlinkid);
            if (chatItems == null) {
                return Resp.fail("无效参数");
            }
            if (Objects.equals(chatItems.getChatmode(), (byte) 1)) {
                WxChatUserItem touserItem = ChatIndexService.fdUserIndex(Integer.valueOf(chatItems.getBizid().intValue()), chatItems.getUid());
                if (!ChatService.existTwoFriend(touserItem)) {
                    return Resp.fail(MulLauUtils.getMsg("对方不是你的好友"));
                }
            }
            String sessionid = request.getHttpSession().getId();
            File dbFile = innerUploadFile(curr, uploadFile, sessionid);
            String text = Json.toJson(dbFile);
            if (Objects.equals(chatItems.getChatmode(), (byte) 1)) {
                WxChatApi.sendTimeTipMsg((Long) null, Integer.valueOf(chatItems.getBizid().intValue()), curr.getId());
                Ret ret = WxChatApi.sendFdMsgEach(request, text, (byte) 3, chatItems.getUid(), Integer.valueOf(chatItems.getBizid().intValue()), (byte) 2);
                if (ret.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(ret));
                }
                return Resp.ok(RetUtils.OPER_RIGHT);
            }
            WxChatApi.sendTimeTipMsg(chatItems.getBizid(), (Integer) null, curr.getId());
            Ret ret2 = WxChatApi.sendGroupMsgEach(request, text, (byte) 3, chatItems.getUid(), chatItems.getBizid(), (byte) 2, (SysMsgVo) null);
            if (ret2.isFail()) {
                return Resp.fail(RetUtils.getRetMsg(ret2));
            }
            return Resp.ok(RetUtils.OPER_RIGHT);
        } catch (Exception e) {
            log.error("", e);
            return Resp.fail("系统错误");
        }
    }

    @RequestPath("/queryOnline")
    public Resp queryOnlineStatus(HttpRequest request, String uids) {
        List<Map<String, Object>> list = new ArrayList<>();
        if (uids == null) {
            return Resp.ok(list);
        }
        for (final String uid : uids.split(Constants.SPE1)) {
            if (!StrUtil.isBlank(uid) && !uid.equals("null")) {
                Map<String, Object> map = new HashMap<>();
                map.put("uid", uid);
                boolean online = ((Boolean) CacheUtils.get(Caches.getCache(CacheConfig.USER_ONLIE), "online_" + uid, true, new FirsthandCreater<Boolean>() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.24
                    /* renamed from: create, reason: merged with bridge method [inline-methods] */
                    public Boolean m50create() throws Exception {
                        return Boolean.valueOf(!WxChatApi.isOutline(Integer.valueOf(Integer.parseInt(uid))));
                    }
                })).booleanValue();
                map.put("online", Boolean.valueOf(online));
                if (!online) {
                    map.put("lastOnlineTime", Caches.getCache(CacheConfig.USER_ONLINE_TIME).get(uid));
                }
                list.add(map);
            }
        }
        return Resp.ok(list);
    }

    @RequestPath("/inP2pChatInput")
    public Resp inP2pChatInput(HttpRequest request, Long chatlinkid, Integer toUid) {
        User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail("未登录");
        }
        WxChatUserItem wxChatUserItem = ChatIndexService.chatUserIndex(toUid, curr.getId(), (byte) 1);
        if (wxChatUserItem == null) {
            return Resp.fail("未找到相应的会话ID");
        }
        WxUserOperNtf operNtf = new WxUserOperNtf();
        operNtf.setOperbizdata(String.valueOf(curr.getId()));
        operNtf.setChatlinkid(wxChatUserItem.getChatlinkid());
        operNtf.setOper((byte) 55);
        ImPacket imPacket = new ImPacket(Command.WxUserOperNtf, Json.toJson(operNtf));
        Ims.sendToUser(Integer.valueOf(toUid.intValue()), imPacket);
        return Resp.ok();
    }

    @RequestPath("/location")
    public Resp location(HttpRequest request, UploadFile uploadFile, Long chatlinkid, String lat, String lng, String address) throws Exception {
        try {
            if (uploadFile == null) {
                return Resp.fail("上传信息为空");
            }
            if (chatlinkid == null) {
                return Resp.fail("会话id参数为空");
            }
            User curr = WebUtils.currUser(request);
            if (chatlinkid.longValue() <= 0) {
                Ret check = GroupService.checkGroupMsg(Long.valueOf(-chatlinkid.longValue()), curr.getId());
                if (check.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(check));
                }
                chatlinkid = (Long) RetUtils.getOkTData(check);
            }
            WxChatItems chatItems = chatService.getBaseChatItems(chatlinkid);
            if (chatItems == null) {
                return Resp.fail("无效参数");
            }
            if (Objects.equals(chatItems.getChatmode(), (byte) 1)) {
                WxChatUserItem touserItem = ChatIndexService.fdUserIndex(Integer.valueOf(chatItems.getBizid().intValue()), chatItems.getUid());
                if (!ChatService.existTwoFriend(touserItem)) {
                    return Resp.fail(MulLauUtils.getMsg("对方不是你的好友"));
                }
            }
            request.getHttpSession().getId();
            Resp resp = processUploadedImg(request, uploadFile);
            Img img = (Img) resp.getData();
            JSONObject jsonObject = JSONObject.parseObject(Json.toJson(img));
            jsonObject.put("lat", lat);
            jsonObject.put("lng", lng);
            jsonObject.put("address", address);
            String text = jsonObject.toJSONString();
            if (Objects.equals(chatItems.getChatmode(), (byte) 1)) {
                WxChatApi.sendTimeTipMsg((Long) null, Integer.valueOf(chatItems.getBizid().intValue()), curr.getId());
                Ret ret = WxChatApi.sendFdMsgEach(request, text, (byte) 14, chatItems.getUid(), Integer.valueOf(chatItems.getBizid().intValue()), (byte) 2);
                if (ret.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(ret));
                }
                return Resp.ok(RetUtils.OPER_RIGHT);
            }
            WxChatApi.sendTimeTipMsg(chatItems.getBizid(), (Integer) null, curr.getId());
            Ret ret2 = WxChatApi.sendGroupMsgEach(request, text, (byte) 14, chatItems.getUid(), chatItems.getBizid(), (byte) 2, (SysMsgVo) null);
            if (ret2.isFail()) {
                return Resp.fail(RetUtils.getRetMsg(ret2));
            }
            return Resp.ok(RetUtils.OPER_RIGHT);
        } catch (Exception e) {
            log.error("", e);
            return Resp.fail("系统错误");
        }
    }

    @RequestPath("/emotion")
    public Resp location(HttpRequest request, Integer type, String emotion, Long chatlinkid, int width, int height) throws Exception {
        try {
            if (chatlinkid == null) {
                return Resp.fail("会话id参数为空");
            }
            User curr = WebUtils.currUser(request);
            if (chatlinkid.longValue() <= 0) {
                Ret check = GroupService.checkGroupMsg(Long.valueOf(-chatlinkid.longValue()), curr.getId());
                if (check.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(check));
                }
                chatlinkid = (Long) RetUtils.getOkTData(check);
            }
            WxChatItems chatItems = chatService.getBaseChatItems(chatlinkid);
            if (chatItems == null) {
                return Resp.fail("无效参数");
            }
            if (Objects.equals(chatItems.getChatmode(), (byte) 1)) {
                WxChatUserItem touserItem = ChatIndexService.fdUserIndex(Integer.valueOf(chatItems.getBizid().intValue()), chatItems.getUid());
                if (!ChatService.existTwoFriend(touserItem)) {
                    return Resp.fail(MulLauUtils.getMsg("对方不是你的好友"));
                }
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("type", type);
            jsonObject.put("emotion", emotion);
            jsonObject.put("width", Integer.valueOf(width));
            jsonObject.put("height", Integer.valueOf(height));
            String text = jsonObject.toJSONString();
            if (Objects.equals(chatItems.getChatmode(), (byte) 1)) {
                WxChatApi.sendTimeTipMsg((Long) null, Integer.valueOf(chatItems.getBizid().intValue()), curr.getId());
                Ret ret = WxChatApi.sendFdMsgEach(request, text, (byte) 15, chatItems.getUid(), Integer.valueOf(chatItems.getBizid().intValue()), (byte) 2);
                if (ret.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(ret));
                }
                return Resp.ok(RetUtils.OPER_RIGHT);
            }
            WxChatApi.sendTimeTipMsg(chatItems.getBizid(), (Integer) null, curr.getId());
            Ret ret2 = WxChatApi.sendGroupMsgEach(request, text, (byte) 15, chatItems.getUid(), chatItems.getBizid(), (byte) 2, (SysMsgVo) null);
            if (ret2.isFail()) {
                return Resp.fail(RetUtils.getRetMsg(ret2));
            }
            return Resp.ok(RetUtils.OPER_RIGHT);
        } catch (Exception e) {
            log.error("", e);
            return Resp.fail("系统错误");
        }
    }

    @RequestPath("/video")
    public Resp video(HttpRequest request, UploadFile uploadFile, Long chatlinkid) throws Exception {
        try {
            if (uploadFile == null) {
                return Resp.fail("上传信息为空");
            }
            if (chatlinkid == null) {
                return Resp.fail("会话id参数为空");
            }
            User curr = WebUtils.currUser(request);
            if (chatlinkid.longValue() <= 0) {
                Ret check = GroupService.checkGroupMsg(Long.valueOf(-chatlinkid.longValue()), curr.getId());
                if (check.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(check));
                }
                chatlinkid = (Long) RetUtils.getOkTData(check);
            }
            WxChatItems chatItems = chatService.getBaseChatItems(chatlinkid);
            if (chatItems == null) {
                return Resp.fail("无效参数");
            }
            if (Objects.equals(chatItems.getChatmode(), (byte) 1)) {
                WxChatUserItem touserItem = ChatIndexService.fdUserIndex(Integer.valueOf(chatItems.getBizid().intValue()), chatItems.getUid());
                if (!ChatService.existTwoFriend(touserItem)) {
                    return Resp.fail(MulLauUtils.getMsg("对方不是你的好友"));
                }
            }
            Resp resp = processUploadedVideo(request, uploadFile);
            if (!resp.isOk()) {
                return resp;
            }
            Video video = (Video) resp.getData();
            String text = Json.toJson(video);
            if (Objects.equals(chatItems.getChatmode(), (byte) 1)) {
                WxChatApi.sendTimeTipMsg((Long) null, Integer.valueOf(chatItems.getBizid().intValue()), curr.getId());
                Ret ret = WxChatApi.sendFdMsgEach(request, text, (byte) 5, chatItems.getUid(), Integer.valueOf(chatItems.getBizid().intValue()), (byte) 2);
                if (ret.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(ret));
                }
                return Resp.ok(RetUtils.OPER_RIGHT);
            }
            WxChatApi.sendTimeTipMsg(chatItems.getBizid(), (Integer) null, curr.getId());
            Ret ret2 = WxChatApi.sendGroupMsgEach(request, text, (byte) 5, chatItems.getUid(), chatItems.getBizid(), (byte) 2, (SysMsgVo) null);
            if (ret2.isFail()) {
                return Resp.fail(RetUtils.getRetMsg(ret2));
            }
            return Resp.ok(RetUtils.OPER_RIGHT);
        } catch (Exception e) {
            log.error("", e);
            return Resp.fail("系统错误");
        }
    }

    @RequestPath("/img")
    public Resp img(HttpRequest request, UploadFile uploadFile, Long chatlinkid) throws Exception {
        try {
            if (uploadFile == null) {
                return Resp.fail("上传信息为空");
            }
            if (chatlinkid == null) {
                return Resp.fail("会话id参数为空");
            }
            User curr = WebUtils.currUser(request);
            if (chatlinkid.longValue() <= 0) {
                Ret check = GroupService.checkGroupMsg(Long.valueOf(-chatlinkid.longValue()), curr.getId());
                if (check.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(check));
                }
                chatlinkid = (Long) RetUtils.getOkTData(check);
            }
            WxChatItems chatItems = chatService.getBaseChatItems(chatlinkid);
            if (chatItems == null) {
                return Resp.fail("无效参数");
            }
            if (Objects.equals(chatItems.getChatmode(), (byte) 1)) {
                WxChatUserItem touserItem = ChatIndexService.fdUserIndex(Integer.valueOf(chatItems.getBizid().intValue()), chatItems.getUid());
                if (!ChatService.existTwoFriend(touserItem)) {
                    return Resp.fail(MulLauUtils.getMsg("对方不是你的好友"));
                }
            }
            Resp resp = processUploadedImg(request, uploadFile);
            if (!resp.isOk()) {
                return resp;
            }
            Img img = (Img) resp.getData();
            String text = Json.toJson(img);
            if (Objects.equals(chatItems.getChatmode(), (byte) 1)) {
                WxChatApi.sendTimeTipMsg((Long) null, Integer.valueOf(chatItems.getBizid().intValue()), curr.getId());
                Ret ret = WxChatApi.sendFdMsgEach(request, text, (byte) 6, chatItems.getUid(), Integer.valueOf(chatItems.getBizid().intValue()), (byte) 2);
                if (ret.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(ret));
                }
                return Resp.ok(RetUtils.OPER_RIGHT);
            }
            WxChatApi.sendTimeTipMsg(chatItems.getBizid(), (Integer) null, curr.getId());
            Ret ret2 = WxChatApi.sendGroupMsgEach(request, text, (byte) 6, chatItems.getUid(), chatItems.getBizid(), (byte) 2, (SysMsgVo) null);
            if (ret2.isFail()) {
                return Resp.fail(RetUtils.getRetMsg(ret2));
            }
            return Resp.ok(RetUtils.OPER_RIGHT);
        } catch (Exception e) {
            log.error("", e);
            return Resp.fail("系统错误");
        }
    }

    @RequestPath("/audio")
    public Resp audio(HttpRequest request, UploadFile uploadFile, Long chatlinkid) throws Exception {
        try {
            if (uploadFile == null) {
                return Resp.fail("上传信息为空");
            }
            if (chatlinkid == null) {
                return Resp.fail("会话id参数为空");
            }
            User curr = WebUtils.currUser(request);
            if (chatlinkid.longValue() <= 0) {
                Ret check = GroupService.checkGroupMsg(Long.valueOf(-chatlinkid.longValue()), curr.getId());
                if (check.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(check));
                }
                chatlinkid = (Long) RetUtils.getOkTData(check);
            }
            WxChatItems chatItems = chatService.getBaseChatItems(chatlinkid);
            if (chatItems == null) {
                return Resp.fail("无效参数");
            }
            if (Objects.equals(chatItems.getChatmode(), (byte) 1)) {
                WxChatUserItem touserItem = ChatIndexService.fdUserIndex(Integer.valueOf(chatItems.getBizid().intValue()), chatItems.getUid());
                if (!ChatService.existTwoFriend(touserItem)) {
                    return Resp.fail(MulLauUtils.getMsg("对方不是你的好友"));
                }
            }
            Resp resp = processUploadedAudio(request, uploadFile);
            if (!resp.isOk()) {
                return resp;
            }
            Audio audio = (Audio) resp.getData();
            String text = Json.toJson(audio);
            if (Objects.equals(chatItems.getChatmode(), (byte) 1)) {
                WxChatApi.sendTimeTipMsg((Long) null, Integer.valueOf(chatItems.getBizid().intValue()), curr.getId());
                Ret ret = WxChatApi.sendFdMsgEach(request, text, (byte) 4, chatItems.getUid(), Integer.valueOf(chatItems.getBizid().intValue()), (byte) 2);
                if (ret.isFail()) {
                    return Resp.fail(RetUtils.getRetMsg(ret));
                }
                return Resp.ok(RetUtils.OPER_RIGHT);
            }
            WxChatApi.sendTimeTipMsg(chatItems.getBizid(), (Integer) null, curr.getId());
            Ret ret2 = WxChatApi.sendGroupMsgEach(request, text, (byte) 4, chatItems.getUid(), chatItems.getBizid(), (byte) 2, (SysMsgVo) null);
            if (ret2.isFail()) {
                return Resp.fail(RetUtils.getRetMsg(ret2));
            }
            return Resp.ok(RetUtils.OPER_RIGHT);
        } catch (Exception e) {
            log.error("", e);
            return Resp.fail("系统错误");
        }
    }

    public static File innerUploadFile(User curr, UploadFile uploadFile, String sessionid) throws Exception {
        byte[] bs = uploadFile.getData();
        String filename = uploadFile.getName();
        String ext = FileUtil.extName(filename);
        String urlWithoutExt = UploadUtils.newFile("wx/upload/file", curr.getId().intValue(), uploadFile.getName());
        String url = urlWithoutExt + "." + ext;
        java.io.File file = new java.io.File(Const.RES_ROOT, url);
        FileUtil.writeBytes(bs, file);
        File dbFile = new File();
        dbFile.setExt(ext);
        dbFile.setFilename(uploadFile.getName());
        dbFile.setSession(sessionid);
        dbFile.setSize(Long.valueOf(bs.length));
        dbFile.setUid(curr.getId());
        dbFile.setUrl(url);
        dbFile.save();
        return dbFile;
    }

    private Resp processUploadedVideo(HttpRequest request, UploadFile uploadFile) throws Exception {
        Integer uid;
        User curr = WebUtils.currUser(request);
        byte[] bs = uploadFile.getData();
        String filename = uploadFile.getName();
        String extName = FileUtil.extName(filename);
        Resp ret = null;
        if (curr == null) {
            ret = Resp.fail("您尚未登录或登录超时").code(1001);
        }
        if (curr != null) {
            uid = curr.getId();
        } else {
            uid = 1;
        }
        String videoUrlWithoutExt = UploadUtils.newFile("wx/upload/video", uid.intValue(), filename);
        String videoUrl = videoUrlWithoutExt + "." + extName;
        String videoFilePath = Const.RES_ROOT + videoUrl;
        FileUtil.writeBytes(bs, videoFilePath);
        String coverUrl = UploadUtils.newFile("wx/upload/video", uid.intValue(), FileUtil.mainName(filename)) + ".jpg";
        String coverFilePath = Const.RES_ROOT + coverUrl;
        Video video = VideoUtils.processVideo(videoFilePath, coverFilePath, 400, "jpg");
        video.setUrl(videoUrl);
        video.setUid(uid);
        video.setComefrom((byte) 1);
        video.setCoverurl(coverUrl);
        video.setStatus((byte) 1);
        video.setTitle(filename);
        video.setFilename(filename);
        video.setSession(request.getHttpSession().getId());
        boolean f = VideoService.me.save(video);
        if (ret != null) {
            return ret;
        }
        if (f) {
            return Resp.ok(video);
        }
        return Resp.fail().data(video);
    }

    private Resp processUploadedAudio(HttpRequest request, UploadFile uploadFile) throws Exception {
        Integer uid;
        User curr = WebUtils.currUser(request);
        byte[] bs = uploadFile.getData();
        String filename = uploadFile.getName();
        String extName = FileUtil.extName(filename);
        Resp ret = null;
        if (curr == null) {
            ret = Resp.fail("您尚未登录或登录超时").code(1001);
        }
        if (curr != null) {
            uid = curr.getId();
        } else {
            uid = 1;
        }
        String urlWithoutExt = UploadUtils.newFile("wx/upload/video", uid.intValue(), filename);
        String url = urlWithoutExt + "." + extName;
        String filePath = Const.RES_ROOT + url;
        FileUtil.writeBytes(bs, filePath);
        FFmpegFrameGrabber ff = new FFmpegFrameGrabber(filePath);
        ff.start();
        long millseconds = ff.getLengthInTime() / 1000;
        double seconds = Math.ceil(new Double(millseconds).doubleValue() / 1000.0d);
        ff.stop();
        Audio audio = new Audio();
        audio.setSeconds(Integer.valueOf((int) seconds));
        audio.setUid(uid);
        audio.setUrl(url);
        audio.setFilename(filename);
        audio.save();
        if (ret != null) {
            return ret;
        }
        return Resp.ok(audio);
    }

    private Resp processUploadedImg(HttpRequest request, UploadFile uploadFile) throws Exception {
        Integer uid;
        User curr = WebUtils.currUser(request);
        byte[] imageBytes = uploadFile.getData();
        Resp ret = null;
        if (curr == null) {
            ret = Resp.fail("您尚未登录或登录超时").code(1001);
        }
        if (curr != null) {
            uid = curr.getId();
        } else {
            uid = 1;
        }
        BufferedImage bi = ImgUtil.toImage(imageBytes);
        float scale = ImgUtils.calcScaleWithWidth(400, bi);
        Img img = ImgUtils.processImg("wx/upload/img", uid, uploadFile, scale);
        img.setComefrom((byte) 5);
        img.setStatus((byte) 1);
        img.setSession(request.getHttpSession().getId());
        boolean f = ImgService.me.save(img);
        if (ret != null) {
            return ret;
        }
        if (f) {
            return Resp.ok(img);
        }
        return Resp.fail("服务器异常");
    }

    @RequestPath("/onFocus")
    @Deprecated
    public Resp onFocus(final HttpRequest request, Long chatlinkid) throws Exception {
        User curr = WebUtils.currUser(request);
        Integer uid = curr.getId();
        if (chatlinkid == null) {
            return Resp.fail("会话id参数为空");
        }
        Byte chatmode = (byte) 1;
        Long groupid = null;
        if (chatlinkid.longValue() <= 0) {
            chatmode = (byte) 2;
            groupid = Long.valueOf(-chatlinkid.longValue());
            WxChatGroupItem groupItem = ChatIndexService.chatGroupIndex(uid, groupid);
            if (groupItem == null || groupItem.getChatlinkid() == null) {
                return Resp.fail("会话id为空");
            }
            chatlinkid = groupItem.getChatlinkid();
        }
        Byte devicetype = Byte.valueOf(WebUtils.getRequestExt(request).getAppDevice());
        IpInfo ipInfo = IpInfoService.ME.save(request.getClientIp());
        final Ret ret = ChatMsgService.me.onFocus(curr, chatlinkid, groupid, chatmode, devicetype, ipInfo.getId());
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        boolean isToRead = ((Boolean) RetUtils.getOkTData(ret)).booleanValue();
        if (isToRead) {
            final Byte runChatMode = chatmode;
            final Long fGroup = groupid;
            Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.25
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        Long tochatlinkid = (Long) RetUtils.getOkTData(ret, "tochatlinkid");
                        Integer touid = (Integer) RetUtils.getOkTData(ret, "touid");
                        if (Objects.equals(runChatMode, (byte) 2)) {
                            WxChatApi.userChatOper(request, touid, Long.valueOf(-fGroup.longValue()), (byte) 7, "好友已读你的消息", "", (WxChatItems) null);
                        } else {
                            WxChatApi.userChatOper(request, touid, tochatlinkid, (byte) 7, "好友已读你的消息", "", (WxChatItems) null);
                        }
                    } catch (Exception e) {
                        ChatController.log.error("", e);
                    }
                }
            });
        }
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/leaveChat")
    @Deprecated
    public Resp leaveChat(HttpRequest request) throws Exception {
        User curr = WebUtils.currUser(request);
        Byte devicetype = Byte.valueOf(WebUtils.getRequestExt(request).getDeviceType());
        if (Objects.equals(devicetype, Devicetype.IOS.getValue()) || Objects.equals(devicetype, Devicetype.ANDROID.getValue())) {
            devicetype = Devicetype.APP.getValue();
        }
        Ret ret = ChatMsgService.me.leaveChat(curr, devicetype);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/pushAllNotice")
    public Resp pushAllNotice(HttpRequest request, final Integer noticeId) throws Exception {
        if (noticeId == null) {
            return Resp.fail("通知ID不能为空");
        }
        Record record = Db.use("tio_site_conf").findFirst("select * from announcement where id = ?", new Object[]{noticeId});
        if (record == null) {
            return Resp.fail("查无此通知");
        }
        new Thread(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.ChatController.26
            @Override // java.lang.Runnable
            public void run() {
                List<User> users = User.dao.find("select * from user");
                Integer num = noticeId;
                users.forEach(user -> {
                    boolean isOnline = !WxChatApi.isOutline(user.getId());
                    if (isOnline) {
                        WxUserOperNtf operNtf = new WxUserOperNtf();
                        operNtf.setOperbizdata(String.valueOf(num));
                        operNtf.setOper((byte) 81);
                        ImPacket imPacket = new ImPacket(Command.WxUserOperNtf, Json.toJson(operNtf));
                        Ims.sendToUser(user.getId(), imPacket);
                        try {
                            Thread.sleep(30L);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        }).start();
        return Resp.ok();
    }

    @RequestPath("/getLastAllNotice")
    public Resp getLastAllNotice() {
        Record record = Db.use("tio_site_conf").findFirst("select * from announcement where status = 2 order by createtime desc limit 1");
        if (record == null) {
            return Resp.fail();
        }
        return Resp.ok(record.getColumns());
    }

    private static Ret getGroupImg(String[] uidArr, User curr, String name) throws Exception {
        User user;
        List<String> avatarList = new ArrayList<>();
        avatarList.add(curr.getAvatar());
        boolean createName = StrUtil.isBlank(name);
        if (createName) {
            name = curr.getNick();
        }
        String nicks = "";
        int c = 0;
        for (String uidStr : uidArr) {
            if (StrUtil.isNotBlank(uidStr)) {
                int uid = Integer.parseInt(uidStr);
                if (!Objects.equals(Integer.valueOf(uid), curr.getId()) && (user = UserService.ME.getById(Integer.valueOf(uid))) != null) {
                    if (createName) {
                        String newName = name + "、" + user.getNick();
                        if (newName.length() < 30) {
                            name = newName;
                        }
                    }
                    nicks = nicks + "、" + user.getNick();
                    if (c < 8) {
                        avatarList.add(user.getAvatar());
                        c++;
                    }
                }
            }
        }
        Img img = WxGroupAvatarUtil.generateGroupAvatar(avatarList, curr.getId().intValue());
        if (createName) {
            return Ret.ok().set("img", img).set("nicks", nicks.substring(1)).set("name", name);
        }
        return Ret.ok().set("img", img).set("nicks", nicks.substring(1));
    }

    public static boolean auotUpdateGroupInfo(HttpRequest request, Byte devicetype, Long groupid, Byte nameUpdate, Short joinnum, User curr) throws Exception {
        WxGroup group = groupService.getByGroupid(groupid);
        if (group == null) {
            log.error("群自动修改头像，本群已解散：groupid-{}", groupid);
            return false;
        }
        boolean nameInit = false;
        String name = "";
        String oldName = group.getName();
        if (Objects.equals(group.getAutoflag(), (byte) 1) && Objects.equals(nameUpdate, (byte) 1)) {
            nameInit = true;
        }
        String avatar = "";
        if (Objects.equals(group.getAvatarautoflag(), (byte) 1) && joinnum != null && ((joinnum.shortValue() > 0 && group.getJoinnum().shortValue() - joinnum.shortValue() < 9) || joinnum.shortValue() <= 0)) {
            List<String> avatarList = new ArrayList<>();
            Ret ret = groupService.groupUserList(groupid, (Integer) null, "");
            if (ret.isFail()) {
                return false;
            }
            Page<Record> page = (Page) RetUtils.getOkTPage(ret);
            if (page == null) {
                log.error("获取用户列表为空");
                return false;
            }
            List<Record> groupUserList = page.getList();
            int avatarCount = 0;
            for (Record record : groupUserList) {
                if (avatarCount <= 8) {
                    Integer uid = record.getInt("uid");
                    User user = UserService.ME.getById(uid);
                    if (user != null) {
                        avatarList.add(user.getAvatar());
                        avatarCount++;
                    }
                    if (nameInit) {
                        String newName = name + "、" + user.getNick();
                        if (newName.length() < 31) {
                            name = newName;
                        }
                    }
                }
            }
            try {
                Img img = WxGroupAvatarUtil.generateGroupAvatar(avatarList, groupUserList.get(0).getInt("uid").intValue());
                avatar = img.getCoverurl();
                if (!avatar.equals(group.getAvatar())) {
                    groupService.modifyAvatar(groupid, avatar, true);
                } else {
                    avatar = "";
                }
            } catch (Exception e) {
                log.error("", e);
            }
        } else if (nameInit) {
            Ret ret2 = groupService.groupUserList(groupid, (Integer) null, "");
            if (ret2.isFail()) {
                return false;
            }
            Page<Record> page2 = (Page) RetUtils.getOkTPage(ret2);
            if (page2 == null) {
                log.error("获取用户列表为空");
                return false;
            }
            for (Record record2 : page2.getList()) {
                Integer uid2 = record2.getInt("uid");
                String newName2 = name + "、" + UserService.ME.getById(uid2).getNick();
                if (newName2.length() < 31) {
                    name = newName2;
                }
            }
        }
        if (StrUtil.isNotBlank(name)) {
            name = name.substring(1);
        }
        if (StrUtil.isNotBlank(name) && !oldName.equals(name)) {
            groupService.modifyName(curr.getId(), groupid, name, true);
        } else {
            name = "";
        }
        if (StrUtil.isNotBlank(name) || StrUtil.isNotBlank(avatar)) {
            return true;
        }
        return false;
    }
}
