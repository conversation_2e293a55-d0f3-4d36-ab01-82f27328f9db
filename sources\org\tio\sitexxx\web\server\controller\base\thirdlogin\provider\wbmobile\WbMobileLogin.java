package org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.wbmobile;

import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.sitexxx.service.model.main.UserThird;
import org.tio.sitexxx.service.model.main.UserThirdWeibo;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.ThirdLoginUtils;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login;
import org.tio.sitexxx.web.server.init.WebApiInit;
import org.tio.utils.jfinal.P;
import weibo4j.Oauth;
import weibo4j.Users;
import weibo4j.http.AccessToken;
import weibo4j.model.User;
import weibo4j.model.WeiboException;
import weibo4j.util.WeiboConfig;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/provider/wbmobile/WbMobileLogin.class */
public class WbMobileLogin extends Auth2Login {
    private static Logger log = LoggerFactory.getLogger(WbMobileLogin.class);

    /* renamed from: me */
    public static WbMobileLogin f13me = new WbMobileLogin();

    private WbMobileLogin() {
        updateProperties();
    }

    private void updateProperties() {
        WeiboConfig.updateProperties("client_ID", P.get("third.login.weibo.pc.AppID"));
        WeiboConfig.updateProperties("client_SERCRET", P.get("third.login.weibo.pc.AppSecret"));
        WeiboConfig.updateProperties("redirect_URI", ThirdLoginUtils.getCallbackUrl(WebApiInit.httpConfig, 3));
    }

    public static void main(String[] args) {
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login
    public String loginUrl(HttpRequest request, Integer type, String state) throws Exception {
        Oauth oauth = new Oauth();
        String url = oauth.authorize("code", state);
        return url;
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: weibo4j.model.WeiboException */
    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login
    public UserThird getUserThird(HttpRequest request, Integer type, String state, String code) throws Exception {
        int iIntValue;
        Oauth oauth = new Oauth();
        AccessToken accessToken = oauth.getAccessTokenByCode(code);
        String uid = accessToken.getUid();
        Users um = new Users();
        try {
            User user = um.showUserById(uid);
            String gender = user.getGender();
            if (Objects.equals("m", gender)) {
                iIntValue = 1;
            } else {
                iIntValue = (Objects.equals("f", gender) ? 2 : null).intValue();
            }
            Integer sex = Integer.valueOf(iIntValue);
            UserThird userThird = new UserThird();
            userThird.setOpenid(user.getIdstr());
            userThird.setAvatar(user.getAvatarLarge());
            userThird.setNick(user.getName());
            userThird.setSex(sex);
            UserThirdWeibo userThirdWeibo = new UserThirdWeibo();
            userThirdWeibo.setCity(user.getCity() + "");
            userThirdWeibo.setCreatedAt(user.getCreatedAt());
            userThirdWeibo.setDescription(user.getDescription());
            userThirdWeibo.setDomain(user.getDomain());
            userThirdWeibo.setFavourites(Integer.valueOf(user.getFavouritesCount()));
            userThirdWeibo.setFollowers(Integer.valueOf(user.getFollowersCount()));
            userThirdWeibo.setFriends(Integer.valueOf(user.getFriendsCount()));
            userThirdWeibo.setVerified(Byte.valueOf(user.isVerified() ? (byte) 1 : (byte) 2));
            userThirdWeibo.setWeihao(user.getWeihao());
            userThird.setSubTable(userThirdWeibo);
            return userThird;
        } catch (WeiboException e) {
            throw e;
        }
    }
}
