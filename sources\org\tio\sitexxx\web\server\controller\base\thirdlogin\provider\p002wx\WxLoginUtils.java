package org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.p002wx;

import java.net.URLEncoder;
import java.text.MessageFormat;
import okhttp3.Response;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.web.server.recharge.provider.alipay.AlipayConfig;
import org.tio.utils.http.HttpUtils;
import org.tio.utils.json.Json;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/provider/wx/WxLoginUtils.class */
public class WxLoginUtils {
    public static final String GET_QRCONNECT_URL = "https://open.weixin.qq.com/connect/qrconnect?appid={0}&redirect_uri={1}&response_type=code&scope=snsapi_login&state={2}#wechat_redirect";
    public static final String GET_OAUTH_USER_AGREE_URL = "https://open.weixin.qq.com/connect/oauth2/authorize?appid={0}&redirect_uri={1}&response_type=code&scope={2}&state={3}&connect_redirect=1#wechat_redirect";
    public static final String GET_OAUTH_URL = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={0}&secret={1}&code={2}&grant_type=authorization_code";
    public static final String GET_USER_INFO = "https://api.weixin.qq.com/sns/userinfo?access_token={0}&openid={1}";
    public static final String CHECK_OAUTH_TOKEN_IS_VALID = "https://api.weixin.qq.com/sns/auth?access_token={0}&openid={1}";
    public static final String REFRESH_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid={0}&grant_type=refresh_token&refresh_token={1}";
    public static final String SOCIAL_LOGIN_CLIENT_ID = ConfService.getString("third.login.wechat.pc.AppID", "");
    public static final String SOCIAL_LOGIN_CLIENT_SERCRET = ConfService.getString("third.login.wechat.pc.AppSecret", "");
    public static final String SOCIAL_LOGIN_OPEN_ID = ConfService.getString("wechat.open.appid", "");
    public static final String SOCIAL_LOGIN_OPEN_SECRET = ConfService.getString("wechat.open.appsecret", "");

    public static String getQrconnect(String redirectUri, String state) throws Exception {
        String getQrcodeUrl = MessageFormat.format(GET_QRCONNECT_URL, SOCIAL_LOGIN_CLIENT_ID, redirectUri, state);
        return getQrcodeUrl;
    }

    public static String getAuthrUrl(String redirectUri, String scope) throws Exception {
        String getCodeUrl = MessageFormat.format(GET_OAUTH_USER_AGREE_URL, SOCIAL_LOGIN_CLIENT_ID, URLEncoder.encode(redirectUri, AlipayConfig.charset), scope);
        return getCodeUrl;
    }

    public static String getAuthrOpenUrl(String redirectUri, String scope, String state) throws Exception {
        String getCodeUrl = MessageFormat.format(GET_OAUTH_USER_AGREE_URL, SOCIAL_LOGIN_OPEN_ID, URLEncoder.encode(redirectUri, AlipayConfig.charset), scope, state);
        return getCodeUrl;
    }

    public static Response oauth(String code) throws Exception {
        String oauthUrl = MessageFormat.format(GET_OAUTH_URL, SOCIAL_LOGIN_CLIENT_ID, SOCIAL_LOGIN_CLIENT_SERCRET, code);
        Response response = HttpUtils.get(oauthUrl);
        return response;
    }

    public static Response oauthOpen(String code) throws Exception {
        String oauthUrl = MessageFormat.format(GET_OAUTH_URL, SOCIAL_LOGIN_OPEN_ID, SOCIAL_LOGIN_OPEN_SECRET, code);
        Response response = HttpUtils.get(oauthUrl);
        return response;
    }

    public static WxUserinfo getWxUserInfo(String accessToken, String openId) throws Exception {
        String getUserInfoUrl = MessageFormat.format(GET_USER_INFO, accessToken, openId);
        Response response = HttpUtils.get(getUserInfoUrl);
        if (response.isSuccessful()) {
            WxUserinfo wxUserinfo = (WxUserinfo) Json.toBean(response.body().string(), WxUserinfo.class);
            return wxUserinfo;
        }
        return null;
    }

    public static Response checkTokenIsValid(String accessToken, String openId) throws Exception {
        String checkTokenUrl = MessageFormat.format(CHECK_OAUTH_TOKEN_IS_VALID, accessToken, openId);
        Response response = HttpUtils.get(checkTokenUrl);
        return response;
    }

    public static Response refreshToken(String refreshToken) throws Exception {
        String refreshTokenUrl = MessageFormat.format(REFRESH_TOKEN_URL, SOCIAL_LOGIN_CLIENT_ID, refreshToken);
        Response response = HttpUtils.get(refreshTokenUrl);
        return response;
    }
}
