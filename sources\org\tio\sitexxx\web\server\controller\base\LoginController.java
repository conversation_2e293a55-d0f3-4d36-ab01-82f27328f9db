package org.tio.sitexxx.web.server.controller.base;

import cn.hutool.core.util.StrUtil;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpConfig;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.common.session.HttpSession;
import org.tio.http.server.annotation.RequestPath;
import org.tio.http.server.util.Resps;
import org.tio.jfinal.kit.Ret;
import org.tio.sitexxx.im.server.handler.wx.WxChatApi;
import org.tio.sitexxx.im.server.handler.wx.WxChatQueueApi;
import org.tio.sitexxx.service.cache.CacheConfig;
import org.tio.sitexxx.service.cache.Caches;
import org.tio.sitexxx.service.model.main.IpInfo;
import org.tio.sitexxx.service.model.main.LoginLog;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.UserAgent;
import org.tio.sitexxx.service.model.main.UserToken;
import org.tio.sitexxx.service.service.base.IpInfoService;
import org.tio.sitexxx.service.service.base.LoginLogService;
import org.tio.sitexxx.service.service.base.UserService;
import org.tio.sitexxx.service.service.base.UserTokenService;
import org.tio.sitexxx.service.service.chat.SynService;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.service.service.conf.IpWhiteListService;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.service.vo.SessionExt;
import org.tio.sitexxx.servicecommon.utils.PeriodUtils;
import org.tio.sitexxx.servicecommon.vo.Devicetype;
import org.tio.sitexxx.servicecommon.vo.MulLauUtils;
import org.tio.sitexxx.web.server.init.WebApiInit;
import org.tio.sitexxx.web.server.p007vo.LoginResult;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.Constants;
import org.tio.utils.SystemTimer;
import org.tio.utils.resp.Resp;

@RequestPath("/login")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/LoginController.class */
public class LoginController {
    private static Logger log = LoggerFactory.getLogger(LoginController.class);
    private UserService userService = UserService.ME;

    public static void main(String[] args) {
    }

    private Ret _login(String loginname, String pd5, String authcode, boolean isThirdLogin, User user, HttpRequest request) throws Exception {
        if (StrUtil.isNotBlank(loginname)) {
            loginname = StrUtil.trim(loginname);
        }
        if (user == null) {
            Ret ret = this.userService.login(loginname, pd5, authcode, isThirdLogin);
            if (ret.isFail()) {
                Resp resp = Resp.fail();
                Integer code = (Integer) ret.get("code");
                if (code.intValue() == 3) {
                    resp.code(3);
                } else if (StrUtil.isBlank(authcode)) {
                    resp.code(LoginResult.ErrorCode.USER_OR_PWD_ERROR_PWD.code).msg(LoginResult.ErrorCode.USER_OR_PWD_ERROR_PWD.value);
                } else {
                    resp.code(LoginResult.ErrorCode.USER_OR_PWD_SMSCODE_PWD.code).msg(LoginResult.ErrorCode.USER_OR_PWD_SMSCODE_PWD.value);
                }
                HttpResponse httpResponse = Resps.json(request, resp);
                return Ret.fail().set("resp", httpResponse);
            }
            user = (User) ret.get("user");
        }
        if (user != null) {
            Resp resp2 = checkStatus(user);
            if (resp2.isOk()) {
                return Ret.ok().set("user", user);
            }
            HttpResponse httpResponse2 = Resps.json(request, resp2);
            return Ret.fail().set("resp", httpResponse2);
        }
        Resp resp3 = Resp.fail();
        resp3.code(LoginResult.ErrorCode.USER_OR_PWD_ERROR_PWD.code).msg(LoginResult.ErrorCode.USER_OR_PWD_ERROR_PWD.value);
        HttpResponse httpResponse3 = Resps.json(request, resp3);
        return Ret.fail().set("resp", httpResponse3);
    }

    private static Resp checkStatus(User user) {
        if (Objects.equals(user.getStatus(), (byte) 1)) {
            return Resp.ok();
        }
        if (Objects.equals(user.getStatus(), (byte) 5)) {
            return Resp.fail().code(LoginResult.ErrorCode.USER_INBLACK_ERROR.code).msg(LoginResult.ErrorCode.USER_INBLACK_ERROR.value);
        }
        return Resp.fail().code(LoginResult.ErrorCode.USER_STATUS_ERROR.code).msg(LoginResult.ErrorCode.USER_STATUS_ERROR.value);
    }

    @RequestPath("")
    public HttpResponse login(String loginname, String pd5, String authcode, HttpRequest request) throws Exception {
        HttpSession oldHttpSession;
        String loginname2 = StrUtil.trim(loginname);
        String sessionId = request.getHttpSession().getId();
        HttpSession httpSession = request.getHttpSession();
        HttpConfig httpConfig = request.getHttpConfig();
        Boolean isThirdLogin = (Boolean) request.getAttribute("IS_THIRD_LOGIN", false);
        User userThirdLogin = null;
        if (isThirdLogin.booleanValue()) {
            userThirdLogin = (User) request.getAttribute("THIRD_LOGIN_USER", (Serializable) null);
        }
        Ret ret = _login(loginname2, pd5, authcode, isThirdLogin.booleanValue(), userThirdLogin, request);
        if (ret.isOk()) {
            User user = (User) ret.get("user");
            RequestExt requestExt = WebUtils.getRequestExt(request);
            byte deviceType = requestExt.getDeviceType();
            boolean fromApp = requestExt.isFromApp();
            String ip = request.getClientIp();
            if (ConfService.getInt("open.ip.white.login", 1).intValue() == 1) {
                boolean canLogin = IpWhiteListService.me.canLogin(user.getId(), ip);
                if (!canLogin) {
                    return Resps.json(request, Resp.fail("不允许登录"));
                }
            }
            String black_ip = ConfService.getString("black_ip", "").trim();
            if (ip != null && black_ip != null) {
                for (String ipStr : black_ip.split(Constants.SPE1)) {
                    if (ip.equals(ipStr.trim())) {
                        return Resps.json(request, Resp.fail("IP不允许登录"));
                    }
                }
            }
            IpInfo ipinfo = IpInfoService.ME.save(ip);
            LoginLog loginLog = new LoginLog();
            Date time = new Date();
            loginLog.setIp(ip);
            loginLog.setIpid(ipinfo.getId());
            loginLog.setUid(user.getId());
            loginLog.setType((byte) 1);
            loginLog.setDevicetype(Byte.valueOf(deviceType));
            loginLog.setTime(time);
            loginLog.setDayperiod(PeriodUtils.dateToPeriodByType(time, (byte) 1));
            loginLog.setTimeperiod(PeriodUtils.dateToPeriodByType(time, (byte) 7));
            loginLog.setHourperiod(PeriodUtils.dateToPeriodByType(time, (byte) 6));
            if (fromApp) {
                loginLog.setDeviceinfo("主-" + StringUtils.substring(requestExt.getDeviceinfo(), 0, 128));
                loginLog.setImei(requestExt.getImei());
                loginLog.setAppversion(requestExt.getAppVersion());
            } else {
                UserAgent userAgent = requestExt.getUserAgent();
                if (userAgent != null) {
                    if (userAgent.getId() != null) {
                        loginLog.setUaid(userAgent.getId());
                    }
                    loginLog.setDeviceinfo("主-" + userAgent.getOsName() + " " + userAgent.getOsVersion() + "/" + userAgent.getAgentName() + " " + userAgent.getAgentVersionMajor());
                } else {
                    loginLog.setDeviceinfo("主-" + StringUtils.substring(request.getUserAgent(), 0, 128));
                }
            }
            HttpResponse httpResponse = null;
            if (isThirdLogin.booleanValue()) {
                httpResponse = (HttpResponse) request.getAttribute("THIRD_LOGIN_RESPONSE");
            }
            if (httpResponse == null) {
                httpResponse = Resps.json(request, Resp.ok());
            }
            WebApiInit.requestHandler.updateSessionId(request, httpSession, httpResponse);
            String newSeesionId = request.getHttpSession().getId();
            loginLog.setSessionid(newSeesionId);
            LoginLogService.me.add(loginLog);
            Caches.getCache(CacheConfig.WX_USER_LOGIN_TOKEN_1).put(newSeesionId, (byte) 1);
            UserToken userToken = UserTokenService.me.find(requestExt.getAppDevice(), user.getId().intValue());
            if (userToken == null) {
                UserToken userToken2 = new UserToken();
                userToken2.setUid(user.getId());
                userToken2.setDevicetype(Byte.valueOf(requestExt.getAppDevice()));
                userToken2.setToken(newSeesionId);
                UserTokenService.me.add(userToken2);
            } else {
                String oldToken = userToken.getToken();
                if (!Objects.equals(oldToken, sessionId) && (oldHttpSession = httpConfig.getSessionStore().get(oldToken)) != null && Objects.equals(ConfService.getInt("login.use.sso", 2), 1)) {
                    int kicktedCode = 1003;
                    if (Objects.equals(Byte.valueOf(deviceType), Devicetype.PC.getValue())) {
                        kicktedCode = 1009;
                    } else if (Objects.equals(Byte.valueOf(deviceType), Devicetype.H5.getValue())) {
                        kicktedCode = 1010;
                    }
                    WxChatApi.sendFriendErrorMsg(Byte.valueOf(requestExt.getDeviceType()), sessionId, ip, user.getId(), user.getId(), user.getId(), (Long) null, Integer.valueOf(kicktedCode), MulLauUtils.getMsg("当前账号已在其他设备登录"));
                    SessionExt oldSessionExt = oldHttpSession.getAttribute("SESSION_EXT", SessionExt.class, new SessionExt(), httpConfig);
                    oldSessionExt.setUid((Integer) null);
                    oldSessionExt.setKickedInfo(loginLog);
                    oldHttpSession.update(httpConfig);
                }
                userToken.setToken(newSeesionId);
                UserTokenService.me.update(userToken);
            }
            if (fromApp) {
                SynService.me.delSynTime(Byte.valueOf(requestExt.getAppDevice()), user.getId());
            }
            WxChatQueueApi.leaveFocusQueue(user, Byte.valueOf(requestExt.getAppDevice()), (String) null);
            SessionExt sessionExt = (SessionExt) httpSession.getAttribute("SESSION_EXT", SessionExt.class);
            sessionExt.setUid(user.getId());
            sessionExt.setLoginTime(Long.valueOf(SystemTimer.currTime));
            httpSession.update(httpConfig);
            return httpResponse;
        }
        HttpResponse httpResponse2 = (HttpResponse) ret.get("resp");
        if (httpResponse2 == null) {
            log.error("_login(loginname, pwd, authcode, request)返回值没有包含response信息");
            return Resps.json(request, Resp.fail("服务器异常"));
        }
        return httpResponse2;
    }
}
