package org.tio.sitexxx.web.server.recharge;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/ReturnResultVo.class */
public class ReturnResultVo {
    private Integer tradeStatus;
    private String tradeNo;
    private Boolean isValidCallback;

    /* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/ReturnResultVo$TradeStatus.class */
    public interface TradeStatus {
        public static final Integer SUCCESS = 1;
        public static final Integer FINISHED = 2;
        public static final Integer CLOSED = 3;
        public static final Integer OTHER = 4;
    }

    public ReturnResultVo() {
        this.isValidCallback = false;
    }

    public ReturnResultVo(Integer tradeStatus, String tradeNo, Boolean isValidCallback) {
        this.isValidCallback = false;
        this.tradeStatus = tradeStatus;
        this.tradeNo = tradeNo;
        this.isValidCallback = isValidCallback;
    }

    public static void main(String[] args) {
    }

    public String getTradeNo() {
        return this.tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public Integer getTradeStatus() {
        return this.tradeStatus;
    }

    public void setTradeStatus(Integer tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public Boolean getIsValidCallback() {
        return this.isValidCallback;
    }

    public void setIsValidCallback(Boolean isValidCallback) {
        this.isValidCallback = isValidCallback;
    }
}
