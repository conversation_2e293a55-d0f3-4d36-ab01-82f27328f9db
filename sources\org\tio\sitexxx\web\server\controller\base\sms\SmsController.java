package org.tio.sitexxx.web.server.controller.base.sms;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import java.util.Map;
import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.sitexxx.service.cache.CacheConfig;
import org.tio.sitexxx.service.cache.Caches;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.UserThird;
import org.tio.sitexxx.service.service.base.RegisterService;
import org.tio.sitexxx.service.service.base.UserService;
import org.tio.sitexxx.service.service.base.UserThirdService;
import org.tio.sitexxx.service.service.base.sms.SmsService;
import org.tio.sitexxx.service.service.captcha.CaptchaLocalService;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.service.utils.CommonUtils;
import org.tio.sitexxx.service.utils.RetUtils;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.ThirdLoginFactory;
import org.tio.sitexxx.web.server.recharge.IRechargeProvider;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.cache.ICache;
import org.tio.utils.resp.Resp;

@RequestPath("/sms")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/sms/SmsController.class */
public class SmsController {
    private static Logger log = LoggerFactory.getLogger(SmsController.class);
    private static final CaptchaService captchaService = CaptchaLocalService.captchaService;

    public static void main(String[] args) {
    }

    @RequestPath("/send")
    public Resp send(CaptchaVO captchaVO, Byte biztype, String mobile, HttpRequest request) throws Exception {
        if (biztype == null || mobile == null || captchaVO == null) {
            return Resp.fail().msg("无效参数");
        }
        if (ConfService.getInt("validateType", 1).intValue() == 2) {
            if (biztype.byteValue() == 2) {
                String code = RandomUtils.nextInt(100000, 999999) + "";
                ICache cache = Caches.getCache(CacheConfig.EMAIL_AUTHCODE);
                cache.put(code, mobile);
                System.out.println("当前验证码是：" + code);
                boolean result = RegisterService.me.sendRegiestCodeEmail((byte) 2, "注册验证码", code, mobile, request.getClientIp(), request.getHttpSession().getId(), WebUtils.getRequestExt(request));
                if (!result) {
                    return Resp.fail("验证码发送失败");
                }
                return Resp.ok("验证码已发送");
            }
            if (biztype.byteValue() == 6) {
                User byLoginname = UserService.ME.getByLoginname(mobile, (Byte) null);
                if (byLoginname == null) {
                    return Resp.fail("账号不存在");
                }
                String email = byLoginname.getEmailpwd();
                if (StrUtil.isBlank(email)) {
                    return Resp.fail("未绑定邮箱");
                }
                System.out.println("email=====>" + email);
                String code2 = RandomUtils.nextInt(100000, 999999) + "";
                ICache cache2 = Caches.getCache(CacheConfig.EMAIL_AUTHCODE);
                cache2.put(code2, email);
                System.out.println("当前验证码是：" + code2);
                boolean result2 = RegisterService.me.sendRegiestCodeEmail((byte) 2, "修改密码验证码", code2, email, request.getClientIp(), request.getHttpSession().getId(), WebUtils.getRequestExt(request));
                if (!result2) {
                    return Resp.fail("验证码发送失败");
                }
                return Resp.ok("验证码已发送");
            }
            if (biztype.byteValue() == 3) {
                return Resp.fail("不支持");
            }
        }
        if (biztype.byteValue() == 3) {
            User first = User.dao.findFirst("select * from user where phone = ?", new Object[]{mobile});
            if (first == null) {
                if ("1".equals(ConfService.getString("inviteEnable", "2"))) {
                    return Resp.fail().msg("账号不存在，请先注册");
                }
                return Resp.fail("请先注册").code(98);
            }
        }
        ResponseModel model = captchaService.verification(captchaVO);
        if (model.isError()) {
            return Resp.fail(model.getRepMsg());
        }
        String ip = request.getClientIp();
        String sessionid = request.getHttpSession().getId();
        String referer = request.getReferer();
        Ret ret = SmsService.me.send(mobile, biztype, ip, sessionid, referer, "", (Map) null);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/resetPwdSend")
    public Resp resetPwdSend(CaptchaVO captchaVO, Integer sendType, String loginname, HttpRequest request) throws Exception {
        Ret ret;
        if (sendType == null || loginname == null || captchaVO == null) {
            return Resp.fail().msg("无效参数");
        }
        ResponseModel model = captchaService.verification(captchaVO);
        if (model.isError()) {
            return Resp.fail(model.getRepMsg());
        }
        if (sendType.intValue() == 2) {
            if (!CommonUtils.isPhone(loginname)) {
                return Resp.fail("请输入正确手机号");
            }
            User user = UserService.ME.getByPhone(loginname, (Byte) null);
            if (user == null) {
                return Resp.fail("该手机号不存在");
            }
            ret = sendAuthCode(request, (byte) 1, (byte) 6, loginname);
        } else if (sendType.intValue() == 3) {
            if (!Validator.isEmail(loginname)) {
                return Resp.fail("请输入合法邮箱");
            }
            User user2 = UserService.ME.getByEmail(loginname, (Byte) null);
            if (user2 == null) {
                return Resp.fail("该邮箱不存在");
            }
            ret = sendAuthCode(request, (byte) 2, (byte) 6, loginname);
        } else {
            return Resp.fail("不支持的sendType");
        }
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok().msg("验证码发送成功，请查看");
    }

    @RequestPath("/userBindSend")
    public Resp userBindSend(CaptchaVO captchaVO, Integer sendType, String bindData, HttpRequest request) throws Exception {
        Ret ret;
        if (sendType == null || bindData == null || captchaVO == null) {
            return Resp.fail().msg("无效参数");
        }
        ResponseModel model = captchaService.verification(captchaVO);
        if (model.isError()) {
            return Resp.fail(model.getRepMsg());
        }
        if (sendType.intValue() == 2) {
            if (!CommonUtils.isPhone(bindData)) {
                return Resp.fail("请输入正确手机号");
            }
            ret = sendAuthCode(request, (byte) 1, (byte) 1, bindData);
        } else if (sendType.intValue() == 3) {
            if (!Validator.isEmail(bindData)) {
                return Resp.fail("请输入合法邮箱");
            }
            ret = sendAuthCode(request, (byte) 2, (byte) 1, bindData);
        } else {
            return Resp.fail("不支持的sendType");
        }
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok().msg("验证码发送成功，请查看");
    }

    public Ret sendAuthCode(HttpRequest request, byte sendType, byte bizType, String loginname) throws Exception {
        if (sendType == 1) {
            String ip = request.getClientIp();
            String sessionid = request.getHttpSession().getId();
            String referer = request.getReferer();
            return SmsService.me.send(loginname, Byte.valueOf(bizType), ip, sessionid, referer, "", (Map) null);
        }
        String code = RandomUtils.nextInt(100000, 999999) + "";
        ICache cache = Caches.getCache(CacheConfig.EMAIL_AUTHCODE);
        cache.put(code, loginname);
        System.out.println("当前验证码是：" + code);
        boolean result = RegisterService.me.sendRegiestCodeEmail((byte) 2, "修改密码验证码", code, loginname, request.getClientIp(), request.getHttpSession().getId(), WebUtils.getRequestExt(request));
        if (result) {
            return Ret.ok();
        }
        return Ret.fail("msg", "发送失败");
    }

    @RequestPath("/beforeCheck")
    public Resp beforeCheck(Byte biztype, String mobile, HttpRequest request) throws Exception {
        return bizPhoneCheck(biztype, mobile, request);
    }

    @RequestPath("/check")
    public Resp check(Byte biztype, String mobile, String code, HttpRequest request) throws Exception {
        Ret ret = SmsService.me.checkCode(mobile, biztype, code, (Map) null, false);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok("验证码正确");
    }

    public static Resp bizPhoneCheck(Byte biztype, String mobile, HttpRequest request) {
        if (biztype == null || mobile == null) {
            return Resp.fail().msg("无效参数");
        }
        if (!Validator.isMobile(mobile)) {
            return Resp.fail().msg("无效手机号");
        }
        switch (biztype.byteValue()) {
            case IRechargeProvider.CallType.RETURN /* 1 */:
            case IRechargeProvider.CallType.NOTIFY /* 2 */:
            case 7:
                User register = UserService.ME.getByPhone(mobile, (Byte) null);
                if (register != null) {
                    return Resp.fail("手机号已注册");
                }
                break;
            case 3:
            case 4:
            case 5:
            case 6:
                User login = UserService.ME.getByPhone(mobile, (Byte) null);
                if (login == null) {
                    if ("1".equals(ConfService.getString("inviteEnable", "2"))) {
                        return Resp.fail().msg("账号不存在，请先注册");
                    }
                    return Resp.fail("手机号未注册").code(98);
                }
                break;
            case 8:
                User bindUser = UserService.ME.getByPhone(mobile, (Byte) null);
                if (bindUser != null) {
                    User curr = WebUtils.currUser(request);
                    String typeSplit = ThirdLoginFactory.getSimilarTypesStr(curr.getThirdtype());
                    UserThird check = UserThirdService.me.checkExist(bindUser.getId(), typeSplit);
                    if (check != null) {
                        return Resp.fail("当前手机号已被其它" + UserThird.getThirdLoginTitle(curr.getThirdtype()) + "绑定,请更换其它手机号");
                    }
                    return Resp.ok((byte) 1);
                }
                return Resp.ok((byte) 2);
        }
        return Resp.ok();
    }
}
