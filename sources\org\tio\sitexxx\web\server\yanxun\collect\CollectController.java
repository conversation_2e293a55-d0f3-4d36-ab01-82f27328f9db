package org.tio.sitexxx.web.server.yanxun.collect;

import com.alibaba.fastjson.JSONObject;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.sitexxx.service.model.main.YxCollect;
import org.tio.sitexxx.service.service.yanxun.collect.CollectService;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/collect")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/yanxun/collect/CollectController.class */
public class CollectController {
    private static Logger log = LoggerFactory.getLogger(CollectController.class);
    public static final CollectService collectService = CollectService.me;

    @RequestPath("/addEmotion")
    public Resp addEmotion(HttpRequest request, String url, int width, int height) {
        Integer userId = WebUtils.currUserId(request);
        if (userId == null) {
            return Resp.fail("未登录或登录超时");
        }
        YxCollect collect = new YxCollect();
        collect.setUserId(userId);
        collect.setCollectType(0);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("url", url);
        jsonObject.put("width", Integer.valueOf(width));
        jsonObject.put("height", Integer.valueOf(height));
        collect.setContent(jsonObject.toJSONString());
        if (collect.save()) {
            return Resp.ok("收藏成功");
        }
        return Resp.fail("收藏失败");
    }

    @RequestPath("/emotionList")
    public Resp emotionList(HttpRequest request) {
        Integer userId = WebUtils.currUserId(request);
        if (userId == null) {
            return Resp.fail("未登录或登录超时");
        }
        List<YxCollect> yxCollects = collectService.queryList(0, userId.intValue());
        Map<String, Object> map = new HashMap<>();
        map.put("count", Integer.valueOf(yxCollects.size()));
        map.put("data", yxCollects);
        return Resp.ok(map);
    }

    @RequestPath("/deleteEmotion")
    public Resp deleteEmotion(HttpRequest request, Integer emotionId) {
        Integer userId = WebUtils.currUserId(request);
        if (userId == null) {
            return Resp.fail("未登录或登录超时");
        }
        collectService.deleteCollect(emotionId.intValue());
        return Resp.ok();
    }
}
