package org.tio.sitexxx.web.server.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import java.io.File;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.sitexxx.web.server.init.WebApiInit;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/utils/UploadUtils.class */
public class UploadUtils {
    private static Logger log = LoggerFactory.getLogger(UploadUtils.class);

    public static String dataRootPath(String subDir, int uid) {
        long uid1 = uid + 74541287548L;
        long dir1 = uid1 % 107;
        long dir2 = uid1 % (107 * 107);
        long dir3 = uid1 % ((107 * 107) * 107);
        long dir4 = uid1 % (((107 * 107) * 107) * 107);
        String path = "/" + subDir + "/" + dir1 + "/" + dir2 + "/" + dir3 + "/" + dir4 + "/" + uid1;
        return path;
    }

    public static String newFile(String subDir, int uid, String filename) {
        String path = dataRootPath(subDir, uid);
        Date date = new Date();
        String path2 = path + "/" + RandomUtil.randomInt(1, 120);
        String resRootDir = Const.RES_ROOT;
        File dir = new File(resRootDir, path2);
        FileUtil.mkdir(dir);
        String fileName = DateUtil.format(date, "HHmmss");
        if (filename != null) {
            fileName = fileName + "/" + WebApiInit.sessionIdGenerator.nextId();
        }
        return path2 + "/" + fileName;
    }

    public static String dateFile(String subDir) {
        Date date = new Date();
        String path = (("/" + subDir + "/") + DateUtil.format(new Date(), "yyyyMMdd")) + RandomUtil.randomInt(1, 120);
        String resRootDir = Const.RES_ROOT;
        File dir = new File(resRootDir, path);
        FileUtil.mkdir(dir);
        return path + "/" + DateUtil.format(date, "HHmmss") + WebApiInit.sessionIdGenerator.nextId();
    }

    public static void main(String[] args) {
    }
}
