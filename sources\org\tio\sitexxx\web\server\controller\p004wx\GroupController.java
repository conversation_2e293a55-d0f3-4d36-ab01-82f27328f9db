package org.tio.sitexxx.web.server.controller.p004wx;

import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.sitexxx.im.server.handler.wx.WxChatApi;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.service.chat.ChatMsgService;
import org.tio.sitexxx.service.service.chat.GroupService;
import org.tio.sitexxx.service.utils.RetUtils;
import org.tio.sitexxx.servicecommon.vo.wx.SysMsgVo;
import org.tio.sitexxx.web.server.recharge.IRechargeProvider;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.Threads;
import org.tio.utils.resp.Resp;

@RequestPath("/group")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/wx/GroupController.class */
public class GroupController {
    private static Logger log = LoggerFactory.getLogger(GroupController.class);

    @RequestPath("/modifyIntro")
    public Resp modifyIntro(HttpRequest request, Long groupid, String intro) {
        User curr = WebUtils.currUser(request);
        Resp resp = GroupService.me.modifyIntro(curr, groupid, intro);
        return resp;
    }

    @RequestPath("/modifyName")
    public Resp modifyName(final HttpRequest request, final Long groupid, final String name) {
        final User curr = WebUtils.currUser(request);
        Ret ret = GroupService.me.modifyName(curr.getId(), groupid, name);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.GroupController.1
            @Override // java.lang.Runnable
            public void run() {
                try {
                    Ret ret2 = WxChatApi.updateGroupInfo(request, curr, groupid, name);
                    if (ret2.isFail()) {
                        GroupController.log.error(RetUtils.getRetMsg(ret2));
                    }
                } catch (Exception e) {
                    GroupController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/modifyAvatar")
    public Resp modifyAvatar(final HttpRequest request, final Long groupid, final String avatar) {
        final User curr = WebUtils.currUser(request);
        Ret ret = GroupService.me.modifyUploadAvatar(curr.getId(), groupid, avatar);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.GroupController.2
            @Override // java.lang.Runnable
            public void run() {
                try {
                    Ret ret2 = WxChatApi.updateGroupAvatar(request, curr, groupid, avatar);
                    if (ret2.isFail()) {
                        GroupController.log.error(RetUtils.getRetMsg(ret2));
                    }
                } catch (Exception e) {
                    GroupController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/modifyNotice")
    public Resp modifyNotice(final HttpRequest request, final Long groupid, String notice) {
        final User curr = WebUtils.currUser(request);
        Resp resp = GroupService.me.modifyNotice(curr, groupid, notice);
        if (StrUtil.isNotBlank(notice)) {
            final SysMsgVo sysMsgVo = new SysMsgVo(curr.getNick(), ChatMsgService.MsgTemplate.updatenotice, notice, "updatenotice");
            Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.GroupController.3
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        Ret ret = WxChatApi.sendGroupMsgEach(request, sysMsgVo.toText(), (byte) 1, curr.getId(), groupid, (byte) 1, sysMsgVo);
                        if (ret.isFail()) {
                            GroupController.log.error(RetUtils.getRetMsg(ret));
                        }
                    } catch (Exception e) {
                        GroupController.log.error("", e);
                    }
                }
            });
        }
        return resp;
    }

    @RequestPath("/modifyGroupPush")
    public Resp modifyGroupPush(HttpRequest request, Long groupid, Byte freeflag) {
        User curr = WebUtils.currUser(request);
        Ret ret = GroupService.me.modifyGroupPush(groupid, freeflag, curr.getId());
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/manager")
    public Resp manager(final HttpRequest request, final Long groupid, final Integer uid, final Byte grouprole) {
        final User curr = WebUtils.currUser(request);
        Ret ret = GroupService.me.manager(curr.getId(), uid, groupid, grouprole);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.GroupController.4
            @Override // java.lang.Runnable
            public void run() {
                try {
                    Ret ret2 = WxChatApi.manager(request, curr, groupid, uid, grouprole);
                    if (ret2.isFail()) {
                        GroupController.log.error(RetUtils.getRetMsg(ret2));
                    }
                } catch (Exception e) {
                    GroupController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/modifyFriendFlag")
    public Resp modifyFriendFlag(final HttpRequest request, final Long groupid, final Byte friendflag) {
        final User curr = WebUtils.currUser(request);
        Ret ret = GroupService.me.friendFlag(curr.getId(), groupid, friendflag);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.GroupController.5
            @Override // java.lang.Runnable
            public void run() {
                try {
                    SysMsgVo sysMsgVo = new SysMsgVo(curr.getNick(), friendflag.byteValue() == 2 ? ChatMsgService.MsgTemplate.friendFlagOpen : ChatMsgService.MsgTemplate.friendFlagClose, "", "friendflag");
                    WxChatApi.sendGroupMsgEach(request, sysMsgVo.toText(), (byte) 1, curr.getId(), groupid, (byte) 1, sysMsgVo, String.valueOf(friendflag));
                } catch (Exception e) {
                    GroupController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/modifyExitFlag")
    public Resp modifyExitFlag(final HttpRequest request, final Long groupid, final Byte exitflag) {
        final User curr = WebUtils.currUser(request);
        Ret ret = GroupService.me.exitFlag(curr.getId(), groupid, exitflag);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.GroupController.6
            @Override // java.lang.Runnable
            public void run() {
                try {
                    SysMsgVo sysMsgVo = new SysMsgVo(curr.getNick(), exitflag.byteValue() == 2 ? ChatMsgService.MsgTemplate.exitFlagOpen : ChatMsgService.MsgTemplate.exitFlagClose, "", "exitFlag");
                    WxChatApi.sendGroupMsgEach(request, sysMsgVo.toText(), (byte) 1, curr.getId(), groupid, (byte) 1, sysMsgVo, String.valueOf(exitflag));
                } catch (Exception e) {
                    GroupController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.OPER_RIGHT);
    }

    @RequestPath("/modifyReview")
    public Resp modifyReview(final HttpRequest request, final Long groupid, final Byte mode) {
        final User curr = WebUtils.currUser(request);
        final SysMsgVo sysMsgVo = new SysMsgVo(curr.getNick(), "", "", "");
        switch (mode.byteValue()) {
            case IRechargeProvider.CallType.RETURN /* 1 */:
                String text = ChatMsgService.MsgTemplate.reviewopen;
                sysMsgVo.setMsgbody(text);
                sysMsgVo.setMsgkey("reviewopen");
                break;
            case IRechargeProvider.CallType.NOTIFY /* 2 */:
                String text2 = ChatMsgService.MsgTemplate.reviewclose;
                sysMsgVo.setMsgbody(text2);
                sysMsgVo.setMsgkey("reviewclose");
                break;
            default:
                return Resp.fail().msg("无效入群方式");
        }
        Ret ret = GroupService.me.modifyReview(curr, groupid, mode);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Threads.getGroupExecutor().execute(new Runnable() { // from class: org.tio.sitexxx.web.server.controller.wx.GroupController.7
            @Override // java.lang.Runnable
            public void run() {
                try {
                    WxChatApi.sendGroupMsgEach(request, sysMsgVo.toText(), (byte) 1, curr.getId(), groupid, (byte) 1, sysMsgVo, String.valueOf(mode));
                } catch (Exception e) {
                    GroupController.log.error("", e);
                }
            }
        });
        return Resp.ok(RetUtils.OPER_RIGHT);
    }
}
