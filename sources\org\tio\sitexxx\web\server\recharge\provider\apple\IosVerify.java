package org.tio.sitexxx.web.server.recharge.provider.apple;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import okhttp3.Response;
import org.slf4j.Logger;
import org.tio.http.common.HttpRequest;
import org.tio.sitexxx.service.model.main.RechargeItem;
import org.tio.sitexxx.servicecommon.utils.LogUtils;
import org.tio.sitexxx.web.server.recharge.provider.apple.p006vo.In_app;
import org.tio.sitexxx.web.server.recharge.provider.apple.p006vo.Receipt;
import org.tio.sitexxx.web.server.recharge.provider.apple.p006vo.ReceiptResult;
import org.tio.utils.http.HttpUtils;
import org.tio.utils.jfinal.P;
import org.tio.utils.json.Json;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/provider/apple/IosVerify.class */
public class IosVerify {
    private static Logger log = LogUtils.getCoinLog();
    private static final String URL_SANDBOX = "https://sandbox.itunes.apple.com/verifyReceipt";
    private static final String URL_ONLINE = "https://buy.itunes.apple.com/verifyReceipt";

    public static ReceiptResult verifyReceipt(HttpRequest request, String receipt, RechargeItem rechargeItem) throws Exception {
        return verifyReceipt(request, URL_ONLINE, receipt, true, rechargeItem);
    }

    public static ReceiptResult verifyReceipt(HttpRequest request, String url, String receipt, boolean switchWhenFail, RechargeItem rechargeItem) throws Exception {
        JSONObject data = new JSONObject();
        data.put("receipt-data", receipt);
        String bodyString = data.toJSONString();
        Response response = HttpUtils.post(url, (Map) null, bodyString);
        String resultStr = response.body().string();
        ReceiptResult receiptResult = (ReceiptResult) Json.toBean(resultStr, ReceiptResult.class);
        Receipt receipt1 = receiptResult.getReceipt();
        String mybundleid = P.get("recharge.ios.bundleid");
        String bundleid = receipt1.getBundle_id();
        if (!Objects.equals(bundleid, mybundleid)) {
            throw new Exception("verifyReceipt fail， bundleid is " + bundleid + ", expected value is " + P.get("recharge.ios.bundleid"));
        }
        List<In_app> list = receipt1.getIn_app();
        if (CollUtil.isEmpty(list)) {
            throw new Exception("verifyReceipt fail，in_app 是空的");
        }
        In_app in_app = list.get(0);
        if (!StrUtil.startWith(in_app.getProduct_id(), mybundleid)) {
            throw new Exception("verifyReceipt fail， product_id is not " + mybundleid);
        }
        Integer status = Integer.valueOf(receiptResult.getStatus());
        rechargeItem.setRemark(url);
        if (Objects.equals(status, 0)) {
            log.info("苹果充值成功，resultStr:{}", resultStr);
        } else {
            log.error("苹果充值不成功，resultStr:{}", resultStr);
            if (switchWhenFail) {
                if (Objects.equals(status, 21007) && Objects.equals(URL_ONLINE, url)) {
                    return verifyReceipt(request, URL_SANDBOX, receipt, false, rechargeItem);
                }
                if (Objects.equals(status, 21008) && Objects.equals(URL_SANDBOX, url)) {
                    return verifyReceipt(request, URL_ONLINE, receipt, false, rechargeItem);
                }
            }
        }
        return receiptResult;
    }
}
