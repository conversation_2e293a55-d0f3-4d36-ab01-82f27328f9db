package org.tio.sitexxx.web.server.controller.base.thirdlogin;

import java.io.Serializable;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/ThirdUserInfo.class */
public class ThirdUserInfo implements Serializable {
    private static final long serialVersionUID = -2611183869310773035L;
    private String openid;
    private String avatar;
    private String nick;
    private Integer sex;
    private String city;

    public static void main(String[] args) {
    }

    public String getAvatar() {
        return this.avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNick() {
        return this.nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }

    public String getOpenid() {
        return this.openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public Integer getSex() {
        return this.sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getCity() {
        return this.city;
    }

    public void setCity(String city) {
        this.city = city;
    }
}
