package org.tio.sitexxx.web.server.controller;

import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.service.UserCoinItemService;
import org.tio.sitexxx.service.service.UserCoinService;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/usercoin")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/UserCoinController.class */
public class UserCoinController {
    private static Logger log = LoggerFactory.getLogger(UserCoinController.class);

    @RequestPath("/getRechargeList")
    public Resp getRechargeList(HttpRequest request, Integer pageNumber, Integer pageSize) throws Exception {
        User user = WebUtils.currUser(request);
        Map<String, Object> params = WebUtils.getMapParams(request);
        Ret ret = UserCoinService.me.getUserRecharge(user.getId(), params, pageNumber, pageSize);
        if (ret.isOk()) {
            return Resp.ok().data(ret.get("data"));
        }
        return Resp.fail(ret.getStr("msg"));
    }

    @RequestPath("/getCoinDetail")
    public Resp getCoinDetail(HttpRequest request, Byte coinmode, String month, Integer pageNumber, Integer pageSize) throws Exception {
        User user = WebUtils.currUser(request);
        Ret ret = UserCoinItemService.me.getUserCoinItem(user.getId(), coinmode, month, pageNumber, pageSize);
        if (ret.isOk()) {
            return Resp.ok().data(ret.get("data"));
        }
        return Resp.fail(ret.getStr("msg"));
    }
}
