package org.tio.sitexxx.web.server.recharge.provider.alipay;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradePayModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePagePayRequest;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.util.Resps;
import org.tio.sitexxx.service.model.main.RechargeItem;
import org.tio.sitexxx.servicecommon.utils.LogUtils;
import org.tio.sitexxx.web.server.recharge.IRechargeProvider;
import org.tio.sitexxx.web.server.recharge.RechargeUtils;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.Constants;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/provider/alipay/AlipayRechargeProvider.class */
public class AlipayRechargeProvider implements IRechargeProvider {
    private static Logger log = LogUtils.getCoinLog();

    /* renamed from: me */
    public static final AlipayRechargeProvider f26me = new AlipayRechargeProvider();

    protected AlipayRechargeProvider() {
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: com.alipay.api.AlipayApiException */
    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public HttpResponse toThirdRechargePage(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        Integer paytype = rechargeItem.getPaytype();
        String _subject = rechargeItem.getGoods();
        if (StrUtil.isBlank(_subject)) {
            _subject = "充值";
        }
        String total_fee = String.valueOf(rechargeItem.getAmount());
        AlipayTradePayModel model = new AlipayTradePayModel();
        model.setOutTradeNo(rechargeItem.getTradeno());
        model.setProductCode("FAST_INSTANT_TRADE_PAY");
        model.setTotalAmount(total_fee);
        model.setSubject(_subject);
        model.setBody("");
        AlipayClient alipayClient = AlipayConfig.alipayClient;
        AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();
        alipayRequest.setReturnUrl(RechargeUtils.getReturnUrl(paytype, request));
        alipayRequest.setNotifyUrl(RechargeUtils.getNotifyUrl(paytype, request));
        alipayRequest.setBizModel(model);
        try {
            String form = alipayClient.pageExecute(alipayRequest).getBody();
            return Resps.html(request, form);
        } catch (AlipayApiException e) {
            log.error("支付宝支付生成支付页面时产生异常", e);
            throw e;
        }
    }

    private static Map<String, String> toMap(HttpRequest request) {
        Map<String, String> params = new HashMap<>();
        Map<String, Object[]> requestParams = request.getParams();
        Set<Map.Entry<String, Object[]>> set = requestParams.entrySet();
        for (Map.Entry<String, Object[]> entry : set) {
            String name = entry.getKey();
            if (!"paytype".equals(name)) {
                Object[] values = entry.getValue();
                String valueStr = ArrayUtil.join(values, Constants.SPE1);
                params.put(name, valueStr == null ? "" : valueStr);
            }
        }
        return params;
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public boolean isValidCallback(HttpRequest request, int callbackType) throws Exception {
        Map<String, String> params = toMap(request);
        boolean verify_result = AlipaySignature.rsaCheckV1(params, AlipayConfig.publicKey, AlipayConfig.charset, AlipayConfig.sign_type);
        return verify_result;
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public String getTradeno(HttpRequest request, int callbackType) {
        String out_trade_no = request.getParam("out_trade_no");
        return out_trade_no;
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public void fillOnNotify(HttpRequest request, RechargeItem rechargeItem, int callbackType) {
        String trade_no = request.getParam("trade_no");
        String trade_status = request.getParam("trade_status");
        String buyer_id = request.getParam("buyer_id");
        String total_amount = request.getParam("total_amount");
        Double thirdamount = Double.valueOf(Double.parseDouble(total_amount));
        String gmt_payment = request.getParam("gmt_payment");
        String notify_id = request.getParam("notify_id");
        rechargeItem.setThirdtradeno(trade_no);
        rechargeItem.setThirdstatus(trade_status);
        rechargeItem.setThirdaccount(buyer_id);
        rechargeItem.setThirdamount(thirdamount);
        rechargeItem.setThirdnotifyid(notify_id);
        try {
            rechargeItem.setThirdtradetime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(gmt_payment));
        } catch (Exception e) {
            LogUtils.getCoinLog().error(gmt_payment + "进行日期转换出错了", e);
        }
        if ("TRADE_SUCCESS".equals(trade_status)) {
            rechargeItem.setStatus(2);
        } else if ("TRADE_FINISHED".equals(trade_status)) {
            rechargeItem.setStatus(4);
        } else if ("TRADE_CLOSED".equals(trade_status)) {
            rechargeItem.setStatus(3);
        } else {
            rechargeItem.setStatus(99);
        }
        rechargeItem.setRemark(StringUtils.left(request.getHeaderString() + request.getBodyString(), 2048));
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public HttpResponse notifySuccess(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        return Resps.html(request, "success");
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public HttpResponse notifyFail(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        return Resps.html(request, "fail");
    }
}
