package org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.p002wx;

import java.util.Map;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.sitexxx.service.model.main.UserThird;
import org.tio.sitexxx.service.model.main.UserThirdWx;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.ThirdLoginUtils;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login;
import org.tio.sitexxx.web.server.init.WebApiInit;
import org.tio.utils.json.Json;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/provider/wx/WxOpenLogin.class */
public class WxOpenLogin extends Auth2Login {
    private static Logger log = LoggerFactory.getLogger(WxOpenLogin.class);

    /* renamed from: me */
    public static WxOpenLogin f15me = new WxOpenLogin();

    private WxOpenLogin() {
    }

    public static void main(String[] args) {
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login
    public String loginUrl(HttpRequest request, Integer type, String state) throws Exception {
        String url = WxLoginUtils.getAuthrOpenUrl(ThirdLoginUtils.getCallbackUrl(WebApiInit.httpConfig, type), "snsapi_userinfo", state);
        return url;
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login
    public UserThird getUserThird(HttpRequest request, Integer type, String state, String code) throws Exception {
        Response result = WxLoginUtils.oauthOpen(code);
        if (result.isSuccessful()) {
            String bodyString = result.body().string();
            Map<String, Object> map = (Map) Json.toBean(bodyString, Map.class);
            String openId = (String) map.get("openid");
            String access_token = (String) map.get("access_token");
            WxUserinfo wxUserinfo = WxLoginUtils.getWxUserInfo(access_token, openId);
            if (wxUserinfo == null) {
                return null;
            }
            UserThird userThird = new UserThird();
            userThird.setOpenid(openId);
            userThird.setAvatar(wxUserinfo.getHeadimgurl());
            userThird.setNick(wxUserinfo.getNickname());
            userThird.setSex(Integer.valueOf(wxUserinfo.getSex()));
            userThird.setUnionid(wxUserinfo.getUnionid());
            UserThirdWx userThirdWx = new UserThirdWx();
            userThirdWx.setCity(wxUserinfo.getCity());
            userThirdWx.setCountry(wxUserinfo.getCountry());
            userThirdWx.setProvince(wxUserinfo.getProvince());
            userThird.setSubTable(userThirdWx);
            return userThird;
        }
        return null;
    }
}
