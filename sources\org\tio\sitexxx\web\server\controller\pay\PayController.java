package org.tio.sitexxx.web.server.controller.pay;

import cn.hutool.core.util.StrUtil;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.jfinal.plugin.activerecord.Record;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.WxChatGroupItem;
import org.tio.sitexxx.service.model.main.WxChatUserItem;
import org.tio.sitexxx.service.pay.base.BasePayResp;
import org.tio.sitexxx.service.pay.service.PayService;
import org.tio.sitexxx.service.service.chat.ChatIndexService;
import org.tio.sitexxx.service.service.chat.ChatService;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.service.utils.RetUtils;
import org.tio.sitexxx.service.vo.ClientTokenVo;
import org.tio.sitexxx.service.vo.GrabRedpacketVo;
import org.tio.sitexxx.service.vo.OpenVo;
import org.tio.sitexxx.service.vo.RechargeQueryVo;
import org.tio.sitexxx.service.vo.RechargeVo;
import org.tio.sitexxx.service.vo.SendRedpacketVo;
import org.tio.sitexxx.service.vo.UpdateOpenVo;
import org.tio.sitexxx.service.vo.WalletVo;
import org.tio.sitexxx.service.vo.WithholdQueryVo;
import org.tio.sitexxx.service.vo.WithholdVo;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/pay")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/pay/PayController.class */
public class PayController {
    private static Logger log = LoggerFactory.getLogger(PayController.class);

    @RequestPath("/open")
    public Resp open(HttpRequest request, OpenVo open) throws Exception {
        User curr = WebUtils.currUser(request);
        if (Objects.equals(curr.getOpenflag(), (byte) 1)) {
            return Resp.fail("用户已开户");
        }
        String ip = request.getClientIp();
        if (StrUtil.isBlank(open.getIp())) {
            open.setIp(ip);
        }
        open.setUid(curr.getId());
        BasePayResp resp = PayService.me.openUser(open, request);
        if (!resp.isOk()) {
            return Resp.fail("开户失败:" + resp.getMsg());
        }
        return Resp.ok(resp.getResp());
    }

    @RequestPath("/openflag")
    public Resp openflag(HttpRequest request) throws Exception {
        User curr = WebUtils.currUser(request);
        Map<String, Object> ret = new HashMap<>();
        if (Objects.equals(curr.getOpenflag(), (byte) 1)) {
            ret.put("walletid", curr.getWalletid());
            ret.put("openid", curr.getOpenid());
        }
        ret.put("uid", curr.getId());
        ret.put("openflag", curr.getOpenflag());
        return Resp.ok(ret);
    }

    @RequestPath("/updateOpenInfo")
    public Resp updateOpenInfo(HttpRequest request, UpdateOpenVo update) throws Exception {
        User curr = WebUtils.currUser(request);
        if (Objects.equals(curr.getOpenflag(), (byte) 2)) {
            return Resp.fail("用户未开户");
        }
        if (update == null) {
            return Resp.fail("参数为空");
        }
        if (update.getUid() == null) {
            update.setUid(curr.getId());
        }
        if (update.getWalletid() == null) {
            update.setWalletid(curr.getWalletid());
        }
        BasePayResp resp = PayService.me.updateOpenUser(update, request);
        if (!resp.isOk()) {
            return Resp.fail("修改失败:" + resp.getMsg());
        }
        return Resp.ok(resp.getResp());
    }

    @RequestPath("/recharge")
    public Resp recharge(HttpRequest request, RechargeVo rechargeVo) throws Exception {
        User curr = WebUtils.currUser(request);
        if (Objects.equals(curr.getOpenflag(), (byte) 2)) {
            return Resp.fail("用户未开户");
        }
        if (rechargeVo == null) {
            return Resp.fail("充值参数为空");
        }
        if (StrUtil.isBlank(rechargeVo.getAmount())) {
            return Resp.fail("充值金额为空");
        }
        Double _amount = Double.valueOf(Double.parseDouble(rechargeVo.getAmount()));
        if (_amount.doubleValue() <= 0.0d) {
            return Resp.fail("充值金额为负");
        }
        Integer rechargeLimit = ConfService.getInt("wx.wallet.recharge.max.amount", 100);
        if (_amount.doubleValue() > rechargeLimit.intValue()) {
            return Resp.fail("充值金额最大为" + (new Double(rechargeLimit.intValue()).doubleValue() / 100.0d) + "元");
        }
        if (rechargeVo.getUid() == null) {
            rechargeVo.setUid(curr.getId());
        }
        if (rechargeVo.getWalletid() == null) {
            rechargeVo.setWalletid(curr.getWalletid());
        }
        BasePayResp resp = PayService.me.recharge(rechargeVo, request);
        if (!resp.isOk()) {
            return Resp.fail("预支付失败:" + resp.getMsg());
        }
        return Resp.ok(resp.getResp());
    }

    @RequestPath("/rechargeQuery")
    public Resp rechargeQuery(HttpRequest request, RechargeQueryVo queryVo) throws Exception {
        User curr = WebUtils.currUser(request);
        if (Objects.equals(curr.getOpenflag(), (byte) 2)) {
            return Resp.fail("用户未开户");
        }
        if (queryVo == null || StrUtil.isBlank(queryVo.getSerialnumber())) {
            return Resp.fail("充值参数为空");
        }
        if (queryVo.getUid() == null) {
            queryVo.setUid(curr.getId());
        }
        if (queryVo.getWalletid() == null) {
            queryVo.setWalletid(curr.getWalletid());
        }
        BasePayResp resp = PayService.me.rechargeQuery(queryVo, request);
        if (!resp.isOk()) {
            return Resp.fail("充值查询失败:" + resp.getMsg());
        }
        return Resp.ok(resp.getResp());
    }

    @RequestPath("/rechargelist")
    public Resp rechargelist(HttpRequest request, Integer pageNumber) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = PayService.me.rechargelist(curr.getId(), pageNumber);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/withhold")
    public Resp withhold(HttpRequest request, WithholdVo withholdVo) throws Exception {
        User curr = WebUtils.currUser(request);
        if (Objects.equals(curr.getOpenflag(), (byte) 2)) {
            return Resp.fail("用户未开户");
        }
        if (withholdVo == null) {
            return Resp.fail("充值参数为空");
        }
        if (StrUtil.isBlank(withholdVo.getAmount())) {
            return Resp.fail("充值金额为空");
        }
        Double _amount = Double.valueOf(Double.parseDouble(withholdVo.getAmount()));
        if (_amount.doubleValue() <= 0.0d) {
            return Resp.fail("提现金额为负");
        }
        Integer withholdlimit = ConfService.getInt("wx.wallet.withhold.max.amount", 10);
        if (_amount.doubleValue() > withholdlimit.intValue()) {
            return Resp.fail("提现金额最大为" + (new Double(withholdlimit.intValue()).doubleValue() / 100.0d) + "元");
        }
        Integer minAmount = ConfService.getInt("wx.wallet.withhold.min.amount", 10000);
        if (_amount.doubleValue() < minAmount.intValue()) {
            return Resp.fail("单次提现金额不低于" + (minAmount.intValue() / 100) + "元");
        }
        if (withholdVo.getUid() == null) {
            withholdVo.setUid(curr.getId());
        }
        if (withholdVo.getWalletid() == null) {
            withholdVo.setWalletid(curr.getWalletid());
        }
        BasePayResp resp = PayService.me.withhold(withholdVo, request);
        if (!resp.isOk()) {
            return Resp.fail("预提现失败:" + resp.getMsg());
        }
        return Resp.ok(resp.getResp());
    }

    @RequestPath("/withholdQuery")
    public Resp withholdQuery(HttpRequest request, WithholdQueryVo queryVo) throws Exception {
        User curr = WebUtils.currUser(request);
        if (Objects.equals(curr.getOpenflag(), (byte) 2)) {
            return Resp.fail("用户未开户");
        }
        if (queryVo == null || StrUtil.isBlank(queryVo.getSerialnumber())) {
            return Resp.fail("提现参数为空");
        }
        BasePayResp resp = PayService.me.withholdQuery(queryVo, request);
        if (!resp.isOk()) {
            return Resp.fail("提现查询失败:" + resp.getMsg());
        }
        if (queryVo.getUid() == null) {
            queryVo.setUid(curr.getId());
        }
        if (queryVo.getWalletid() == null) {
            queryVo.setWalletid(curr.getWalletid());
        }
        return Resp.ok(resp.getResp());
    }

    @RequestPath("/withholdlist")
    public Resp withholdlist(HttpRequest request, Integer pageNumber) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = PayService.me.withholdlist(curr.getId(), pageNumber);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/sendRedpacket")
    public Resp sendRedpacket(HttpRequest request, SendRedpacketVo redpacketVo) throws Exception {
        User curr = WebUtils.currUser(request);
        if (Objects.equals(curr.getOpenflag(), (byte) 2)) {
            return Resp.fail("用户未开户");
        }
        if (redpacketVo == null || redpacketVo.getChatlinkid() == null) {
            return Resp.fail("钱包参数为空");
        }
        Double _amount = Double.valueOf(Double.parseDouble(redpacketVo.getAmount()));
        if (_amount.doubleValue() <= 0.0d) {
            return Resp.fail("红包金额为负");
        }
        Integer sendRedpacketLimit = ConfService.getInt("wx.wallet.sendredpacket.max.amount", 10);
        if (_amount.doubleValue() > sendRedpacketLimit.intValue()) {
            return Resp.fail("红包金额最大为" + (new Double(sendRedpacketLimit.intValue()).doubleValue() / 100.0d) + "元");
        }
        if (redpacketVo.getUid() == null) {
            redpacketVo.setUid(curr.getId());
        }
        if (redpacketVo.getWalletid() == null) {
            redpacketVo.setWalletid(curr.getWalletid());
        }
        Long chatlinkid = redpacketVo.getChatlinkid();
        Byte chatmode = (byte) 1;
        if (chatlinkid.longValue() <= 0) {
            chatmode = (byte) 2;
            Long groupid = Long.valueOf(-chatlinkid.longValue());
            WxChatGroupItem groupItem = ChatIndexService.chatGroupIndex(curr.getId(), groupid);
            if (!ChatService.groupExistChat(groupItem)) {
                return Resp.fail("不是群成员");
            }
            groupItem.getChatlinkid();
            redpacketVo.setBizid(groupid);
        } else {
            WxChatUserItem userItem = ChatIndexService.chatUserIndex(chatlinkid);
            if (!ChatService.existTwoFriend(userItem)) {
                return Resp.fail("你们不是互相不是好友");
            }
            redpacketVo.setBizid(userItem.getBizid());
        }
        redpacketVo.setChatmode(chatmode);
        BasePayResp resp = PayService.me.sendRedpacket(redpacketVo, request);
        if (!resp.isOk()) {
            return Resp.fail(resp.getMsg());
        }
        return Resp.ok(resp.getResp());
    }

    @RequestPath("/sendRedpacketlist")
    public Resp sendRedpacketlist(HttpRequest request, Integer pageNumber, String period) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = PayService.me.sendRedpacketlist(curr.getId(), pageNumber, period);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/sendredpacketstat")
    public Resp sendRedpacketStat(HttpRequest request, String period) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = PayService.me.sendRedpacketStat(curr.getId(), period);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Record record = (Record) RetUtils.getOkTData(ret);
        if (record != null) {
            record.set("nick", curr.getNick());
            record.set("avatar", curr.getAvatar());
        }
        return Resp.ok(record);
    }

    @RequestPath("/grabRedpacket")
    public Resp grabRedpacket(HttpRequest request, GrabRedpacketVo grabRedpacketVo) throws Exception {
        User curr = WebUtils.currUser(request);
        if (Objects.equals(curr.getOpenflag(), (byte) 2)) {
            return Resp.fail("用户未开户");
        }
        if (grabRedpacketVo == null) {
            return Resp.fail("钱包参数为空");
        }
        if (grabRedpacketVo.getUid() == null) {
            grabRedpacketVo.setUid(curr.getId());
        }
        if (grabRedpacketVo.getWalletid() == null) {
            grabRedpacketVo.setWalletid(curr.getWalletid());
        }
        Long chatlinkid = grabRedpacketVo.getChatlinkid();
        Byte chatmode = (byte) 1;
        log.error("抢红包的chatlinkid:{}", chatlinkid);
        if (chatlinkid.longValue() <= 0) {
            chatmode = (byte) 2;
            Long groupid = Long.valueOf(-chatlinkid.longValue());
            WxChatGroupItem groupItem = ChatIndexService.chatGroupIndex(curr.getId(), groupid);
            if (!ChatService.groupExistChat(groupItem)) {
                return Resp.fail("不是群成员");
            }
            groupItem.getChatlinkid();
            grabRedpacketVo.setBizid(groupid);
        } else {
            WxChatUserItem userItem = ChatIndexService.chatUserIndex(chatlinkid);
            if (!ChatService.existTwoFriend(userItem)) {
                return Resp.fail("你们不是互相不是好友");
            }
            grabRedpacketVo.setBizid(userItem.getBizid());
        }
        grabRedpacketVo.setChatmode(chatmode);
        BasePayResp resp = PayService.me.grabRedpacket(grabRedpacketVo, request);
        if (!resp.isOk()) {
            return Resp.fail(resp.getMsg());
        }
        return Resp.ok(resp.getResp());
    }

    @RequestPath("/grabRedpacketlist")
    public Resp grabRedpacketlist(HttpRequest request, Integer pageNumber, String period) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = PayService.me.grabRedpacketlist(curr.getId(), pageNumber, period);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/grabredpacketstat")
    public Resp grabRedpacketStat(HttpRequest request, String period) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = PayService.me.grabRedpacketStat(curr.getId(), period);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Record record = (Record) RetUtils.getOkTData(ret);
        if (record != null) {
            record.set("nick", curr.getNick());
            record.set("avatar", curr.getAvatar());
        }
        return Resp.ok(record);
    }

    @RequestPath("/redStatus")
    public Resp redStatus(HttpRequest request, String serialnumber) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = PayService.me.redStatus(curr, serialnumber);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/redInfo")
    public Resp redInfo(HttpRequest request, String serialnumber) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = PayService.me.redInfo(request, serialnumber, curr);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/getWalletInfo")
    public Resp openInfo(HttpRequest request, WalletVo walletVo) throws Exception {
        User curr = WebUtils.currUser(request);
        if (Objects.equals(curr.getOpenflag(), (byte) 2)) {
            return Resp.fail("用户未开户");
        }
        if (walletVo == null) {
            return Resp.fail("钱包参数为空");
        }
        if (walletVo.getUid() == null) {
            walletVo.setUid(curr.getId());
        }
        if (walletVo.getWalletid() == null) {
            walletVo.setWalletid(curr.getWalletid());
        }
        BasePayResp resp = PayService.me.getWalletInfo(walletVo, request);
        if (!resp.isOk()) {
            return Resp.fail(resp.getMsg());
        }
        return Resp.ok(resp.getResp());
    }

    @RequestPath("/getWalletItems")
    public Resp getWalletItems(HttpRequest request, Integer pageNumber, Byte mode) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = PayService.me.getWalletItems(curr.getId(), pageNumber, mode);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/getClientToken")
    public Resp getClientToken(HttpRequest request, ClientTokenVo tokenVo) throws Exception {
        User curr = WebUtils.currUser(request);
        if (Objects.equals(curr.getOpenflag(), (byte) 2)) {
            return Resp.fail("用户未开户");
        }
        if (tokenVo == null) {
            return Resp.fail("token参数为空");
        }
        if (tokenVo.getUid() == null) {
            tokenVo.setUid(curr.getId());
        }
        if (tokenVo.getWalletid() == null) {
            tokenVo.setWalletid(curr.getWalletid());
        }
        tokenVo.setUid(curr.getId());
        tokenVo.setWalletid(curr.getWalletid());
        BasePayResp resp = PayService.me.getClientToken(tokenVo, request);
        if (!resp.isOk()) {
            return Resp.fail(resp.getMsg());
        }
        return Resp.ok(resp.getResp());
    }
}
