package org.tio.sitexxx.web.server.controller.captcha;

import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.server.annotation.RequestPath;
import org.tio.sitexxx.service.service.captcha.CaptchaLocalService;
import org.tio.utils.resp.Resp;

@RequestPath("/anjiCaptcha")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/captcha/AnjiCaptchaController.class */
public class AnjiCaptchaController {
    private static Logger log = LoggerFactory.getLogger(AnjiCaptchaController.class);
    private static final CaptchaService captchaService = CaptchaLocalService.captchaService;

    @RequestPath("/get")
    public Resp get(CaptchaVO captchaVO) {
        ResponseModel model = captchaService.get(captchaVO);
        if (model.isError()) {
            return Resp.fail(model.getRepMsg());
        }
        return Resp.ok(model.getRepData());
    }

    @RequestPath("/check")
    public Resp check(CaptchaVO captchaVO) {
        ResponseModel model = captchaService.check(captchaVO);
        if (model.isError()) {
            return Resp.fail(model.getRepMsg());
        }
        return Resp.ok(model.getRepData());
    }

    @RequestPath("/verify")
    public Resp verify(CaptchaVO captchaVO) {
        ResponseModel model = captchaService.verification(captchaVO);
        if (model.isError()) {
            return Resp.fail(model.getRepMsg());
        }
        return Resp.ok(model.getRepData());
    }
}
