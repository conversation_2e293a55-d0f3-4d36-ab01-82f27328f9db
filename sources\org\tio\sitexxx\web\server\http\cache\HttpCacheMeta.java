package org.tio.sitexxx.web.server.http.cache;

import org.tio.http.common.HttpRequest;
import org.tio.sitexxx.service.model.main.User;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/http/cache/HttpCacheMeta.class */
public interface HttpCacheMeta {
    Boolean isSelf(HttpRequest httpRequest, User user);

    String roleKey(HttpRequest httpRequest, User user);
}
