package org.tio.sitexxx.web.server.controller.base.thirdlogin;

import org.tio.http.common.HttpConfig;
import org.tio.sitexxx.servicecommon.vo.Const;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/ThirdLoginUtils.class */
public class ThirdLoginUtils {
    public static String getCallbackUrl(HttpConfig httpConfig, Integer type) {
        return Const.SITE + httpConfig.getContextPath() + "/tlogin/cb/p/" + type + httpConfig.getSuffix();
    }

    public static void main(String[] args) {
    }
}
