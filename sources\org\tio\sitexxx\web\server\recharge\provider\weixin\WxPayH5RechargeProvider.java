package org.tio.sitexxx.web.server.recharge.provider.weixin;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wxpay.sdk.WXPayConfig;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.util.Resps;
import org.tio.sitexxx.service.model.main.RechargeItem;
import org.tio.sitexxx.service.service.recharge.RechargeItemService;
import org.tio.sitexxx.servicecommon.utils.LogUtils;
import org.tio.sitexxx.web.server.recharge.RechargeUtils;
import org.tio.utils.json.Json;
import org.tio.utils.resp.Resp;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/provider/weixin/WxPayH5RechargeProvider.class */
public class WxPayH5RechargeProvider extends WxPayScan2RechargeProvider {
    private static Logger log = LogUtils.getCoinLog();

    /* renamed from: me */
    public static final WxPayH5RechargeProvider f29me = new WxPayH5RechargeProvider();

    protected WxPayH5RechargeProvider() {
    }

    @Override // org.tio.sitexxx.web.server.recharge.provider.weixin.WxPayScan2RechargeProvider
    public WXPayConfig getWXPayConfig() {
        return WXPayConfigImpl.getInstance();
    }

    @Override // org.tio.sitexxx.web.server.recharge.provider.weixin.WxPayScan2RechargeProvider, org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public HttpResponse toThirdRechargePage(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        Integer paytype = rechargeItem.getPaytype();
        String _subject = rechargeItem.getGoods();
        if (StrUtil.isBlank(_subject)) {
            _subject = "充值";
        }
        String total_fee = String.valueOf((int) NumberUtil.mul(rechargeItem.getAmount().doubleValue(), 100.0f));
        String tradeno = rechargeItem.getTradeno();
        String ip = request.getClientIp();
        Map<String, String> data = new HashMap<>();
        data.put("body", _subject);
        data.put("out_trade_no", tradeno);
        data.put("device_info", "");
        data.put("fee_type", "CNY");
        data.put("total_fee", total_fee);
        data.put("spbill_create_ip", ip);
        data.put("notify_url", RechargeUtils.getNotifyUrl(paytype, request));
        data.put("trade_type", TradeType.MWEB.getValue());
        data.put("product_id", tradeno);
        try {
            Map<String, String> r = this.wxpay.unifiedOrder(data);
            String returnCode = r.get("return_code");
            if ("SUCCESS".equals(returnCode)) {
                String result_code = r.get("result_code");
                if ("SUCCESS".equals(result_code)) {
                    String prepay_id = r.get("prepay_id");
                    if (StrUtil.isNotBlank(prepay_id)) {
                        rechargeItem.setThirdtradeno(prepay_id);
                        RechargeItemService.me.update(rechargeItem);
                        String mweb_url = r.get("mweb_url");
                        Map<String, String> respData = new HashMap<>();
                        respData.put("mweb_url", mweb_url);
                        respData.put("tradeno", tradeno);
                        return Resps.json(request, Resp.ok(respData));
                    }
                } else {
                    log.error("微信H5充值，提交失败:{}", Json.toJson(r));
                }
            } else {
                log.error("微信H5充值，通讯失败:{}", Json.toJson(r));
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return Resps.json(request, Resp.fail());
    }
}
