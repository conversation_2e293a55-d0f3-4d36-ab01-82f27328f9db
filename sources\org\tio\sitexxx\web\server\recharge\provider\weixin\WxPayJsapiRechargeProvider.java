package org.tio.sitexxx.web.server.recharge.provider.weixin;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wxpay.sdk.WXPayConfig;
import com.github.wxpay.sdk.WXPayConstants;
import com.github.wxpay.sdk.WXPayUtil;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.util.Resps;
import org.tio.sitexxx.service.model.main.RechargeItem;
import org.tio.sitexxx.service.service.recharge.RechargeItemService;
import org.tio.sitexxx.servicecommon.utils.LogUtils;
import org.tio.sitexxx.web.server.recharge.RechargeUtils;
import org.tio.sitexxx.web.server.utils.SessionCacheUtils;
import org.tio.utils.json.Json;
import org.tio.utils.resp.Resp;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/provider/weixin/WxPayJsapiRechargeProvider.class */
public class WxPayJsapiRechargeProvider extends WxPayScan2RechargeProvider {
    private static Logger log = LogUtils.getCoinLog();

    /* renamed from: me */
    public static final WxPayJsapiRechargeProvider f30me = new WxPayJsapiRechargeProvider();

    @Override // org.tio.sitexxx.web.server.recharge.provider.weixin.WxPayScan2RechargeProvider
    public WXPayConfig getWXPayConfig() {
        return WXJsapiPayConfigImpl.getInstance();
    }

    @Override // org.tio.sitexxx.web.server.recharge.provider.weixin.WxPayScan2RechargeProvider, org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public HttpResponse toThirdRechargePage(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        Integer paytype = rechargeItem.getPaytype();
        String _subject = rechargeItem.getGoods();
        if (StrUtil.isBlank(_subject)) {
            _subject = "充值";
        }
        String openid = (String) SessionCacheUtils.get(request, "WX_PAY_OPENID");
        if (StrUtil.isBlank(openid)) {
            log.error("没有获取到openid");
            return Resps.json(request, Resp.fail());
        }
        String total_fee = String.valueOf((int) NumberUtil.mul(rechargeItem.getAmount().doubleValue(), 100.0f));
        String tradeno = rechargeItem.getTradeno();
        String ip = request.getClientIp();
        Map<String, String> data = new HashMap<>();
        data.put("body", _subject);
        data.put("out_trade_no", tradeno);
        data.put("device_info", "");
        data.put("fee_type", "CNY");
        data.put("total_fee", total_fee);
        data.put("spbill_create_ip", ip);
        data.put("notify_url", RechargeUtils.getNotifyUrl(paytype, request));
        data.put("trade_type", TradeType.JSAPI.getValue());
        data.put("product_id", tradeno);
        data.put("openid", openid);
        try {
            Map<String, String> r = this.wxpay.unifiedOrder(data);
            log.info("微信JSAPI支付，统一下单收到微信的响应：\r\n{}", Json.toFormatedJson(r));
            String returnCode = r.get("return_code");
            if ("SUCCESS".equals(returnCode)) {
                String result_code = r.get("result_code");
                if ("SUCCESS".equals(result_code)) {
                    String prepay_id = r.get("prepay_id");
                    if (StrUtil.isNotBlank(prepay_id)) {
                        rechargeItem.setThirdtradeno(prepay_id);
                        RechargeItemService.me.update(rechargeItem);
                        Map<String, String> respData = new HashMap<>();
                        respData.put("appId", this.config.getAppID());
                        respData.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
                        respData.put("nonceStr", String.valueOf(System.currentTimeMillis()));
                        respData.put("package", "prepay_id=" + prepay_id);
                        respData.put("signType", "MD5");
                        String paySign = WXPayUtil.generateSignature(respData, this.config.getKey(), WXPayConstants.SignType.MD5);
                        respData.put("paySign", paySign);
                        respData.put("tradeno", tradeno);
                        log.info("微信JSAPI充值，返回给前端页面的参数:\r\n{}", Json.toFormatedJson(respData));
                        return Resps.json(request, Resp.ok(respData));
                    }
                } else {
                    log.error("微信JSAPI充值，提交失败:{}", Json.toJson(r));
                }
            } else {
                log.error("微信JSAPI充值，通讯失败:{}", Json.toJson(r));
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return Resps.json(request, Resp.fail());
    }
}
