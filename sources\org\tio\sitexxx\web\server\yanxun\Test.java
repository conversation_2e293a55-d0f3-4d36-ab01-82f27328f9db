package org.tio.sitexxx.web.server.yanxun;

import java.text.SimpleDateFormat;
import java.util.Date;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/yanxun/Test.class */
public class Test {
    public static void main(String[] args) {
        log(1);
        log(51);
        log(52);
        log(53);
        log(101);
    }

    public static void log(final int i) {
        new Thread(new Runnable() { // from class: org.tio.sitexxx.web.server.yanxun.Test.1
            @Override // java.lang.Runnable
            public void run() {
                synchronized (Integer.valueOf(i % 50)) {
                    try {
                        System.out.println(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.sss").format(new Date()) + "==>i===>" + i);
                        Thread.sleep(2000L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        }).start();
    }
}
