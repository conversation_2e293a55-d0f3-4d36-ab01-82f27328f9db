package org.tio.sitexxx.web.server.recharge.provider.alipay;

import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import org.tio.utils.jfinal.P;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/provider/alipay/AlipayConfig.class */
public class AlipayConfig {
    public static final String url = P.get("recharge.alipay.url");
    public static final String appId = P.get("recharge.alipay.appId");
    public static final String privateKey = P.get("recharge.alipay.privateKey");
    public static final String publicKey = P.get("recharge.alipay.publicKey", "");
    public static final String account = P.get("recharge.alipay.account");
    public static final String charset = "utf-8";
    public static final String sign_type = "RSA2";
    public static final AlipayClient alipayClient = new DefaultAlipayClient(url, appId, privateKey, "json", charset, publicKey, sign_type);

    public static void main(String[] args) {
    }
}
