package org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.douyin;

import java.text.MessageFormat;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.sitexxx.service.model.main.UserThird;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.ThirdLoginUtils;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.auth2.AccessTokenResp;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.douyin.DouyinUserinfoWrap;
import org.tio.sitexxx.web.server.init.WebApiInit;
import org.tio.utils.http.HttpUtils;
import org.tio.utils.json.Json;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/provider/douyin/DouyinLogin.class */
public class DouyinLogin extends Auth2Login {
    public static final String OAUTH_USERINFO_URL = "https://open.douyin.com/oauth/userinfo/?access_token={0}&open_id={1}";
    private static Logger log = LoggerFactory.getLogger(DouyinLogin.class);
    public static final String AppID = ConfService.getString("third.login.douyin.pc.AppID", "");
    public static final String AppSecret = ConfService.getString("third.login.douyin.pc.AppSecret", "");
    public static final String OAUTH_CONNECT_URL = "https://open.douyin.com/platform/oauth/connect?client_key=" + AppID + "&response_type=code&scope=user_info&state={0}&redirect_uri={1}";
    public static final String OAUTH_ACCESSTOKEN_URL = "https://open.douyin.com/oauth/access_token/?client_key=" + AppID + "&client_secret=" + AppSecret + "&code={0}&grant_type=authorization_code";

    /* renamed from: me */
    public static DouyinLogin f7me = new DouyinLogin();

    private DouyinLogin() {
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login
    public String loginUrl(HttpRequest request, Integer type, String state) throws Exception {
        String redirect_uri = ThirdLoginUtils.getCallbackUrl(WebApiInit.httpConfig, type);
        String url = MessageFormat.format(OAUTH_CONNECT_URL, state, redirect_uri);
        return url;
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login
    public UserThird getUserThird(HttpRequest request, Integer type, String state, String code) throws Exception {
        String url = MessageFormat.format(OAUTH_ACCESSTOKEN_URL, code);
        Response response = HttpUtils.get(url);
        String respStr = response.body().string();
        AccessTokenResp accessTokenResp = (AccessTokenResp) Json.toBean(respStr, AccessTokenResp.class);
        if ("success".equals(accessTokenResp.getMessage())) {
            AccessTokenResp.Data data = accessTokenResp.getData();
            String url2 = MessageFormat.format(OAUTH_USERINFO_URL, data.getAccess_token(), data.getOpen_id());
            Response response2 = HttpUtils.get(url2);
            String respStr2 = response2.body().string();
            DouyinUserinfoWrap douyinUserinfoWrap = (DouyinUserinfoWrap) Json.toBean(respStr2, DouyinUserinfoWrap.class);
            if ("success".equals(douyinUserinfoWrap.getMessage())) {
                DouyinUserinfoWrap.DouyinUserinfo douyinUserinfo = douyinUserinfoWrap.getData();
                if (0 == douyinUserinfo.getError_code()) {
                    UserThird userThird = new UserThird();
                    userThird.setOpenid(douyinUserinfo.getOpen_id());
                    userThird.setAvatar(douyinUserinfo.getAvatar());
                    userThird.setNick(douyinUserinfo.getNickname());
                    userThird.setUnionid(douyinUserinfo.getUnion_id());
                    return userThird;
                }
                log.error("抖音登录失败，获取userinfo响应:{}", respStr2);
                return null;
            }
            log.error("抖音登录失败，获取userinfo响应:{}", respStr2);
            return null;
        }
        return null;
    }
}
