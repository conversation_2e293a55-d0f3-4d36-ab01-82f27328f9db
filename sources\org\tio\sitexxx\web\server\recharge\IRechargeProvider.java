package org.tio.sitexxx.web.server.recharge;

import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.sitexxx.service.model.main.RechargeItem;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/IRechargeProvider.class */
public interface IRechargeProvider {

    /* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/IRechargeProvider$CallType.class */
    public interface CallType {
        public static final int RETURN = 1;
        public static final int NOTIFY = 2;
    }

    boolean isValidCallback(HttpRequest httpRequest, int i) throws Exception;

    String getTradeno(HttpRequest httpRequest, int i);

    void fillOnNotify(HttpRequest httpRequest, RechargeItem rechargeItem, int i) throws Exception;

    HttpResponse toThirdRechargePage(HttpRequest httpRequest, RechargeItem rechargeItem, int i) throws Exception;

    HttpResponse notifySuccess(HttpRequest httpRequest, RechargeItem rechargeItem, int i) throws Exception;

    HttpResponse notifyFail(HttpRequest httpRequest, RechargeItem rechargeItem, int i) throws Exception;
}
