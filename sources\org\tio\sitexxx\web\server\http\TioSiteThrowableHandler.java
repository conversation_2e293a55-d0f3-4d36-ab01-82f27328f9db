package org.tio.sitexxx.web.server.http;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.common.RequestLine;
import org.tio.http.server.intf.ThrowableHandler;
import org.tio.http.server.util.Resps;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/http/TioSiteThrowableHandler.class */
public class TioSiteThrowableHandler implements ThrowableHandler {
    private static Logger log = LoggerFactory.getLogger(TioSiteThrowableHandler.class);

    /* renamed from: ME */
    public static final TioSiteThrowableHandler f17ME = new TioSiteThrowableHandler();

    private TioSiteThrowableHandler() {
    }

    public static void main(String[] args) {
    }

    public HttpResponse handler(HttpRequest request, RequestLine requestLine, Throwable throwable) {
        User curr = WebUtils.currUser(request);
        if (curr != null) {
            log.error(curr.toString() + "\r\n" + request.toString(), throwable);
        } else {
            log.error(request.getHttpSession().getId() + "\r\n" + request.toString(), throwable);
        }
        HttpResponse ret = Resps.json(request, Resp.fail());
        RequestExt requestExt = WebUtils.getRequestExt(request);
        requestExt.setCanCache(false);
        return ret;
    }
}
