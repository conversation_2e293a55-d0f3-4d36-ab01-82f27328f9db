package org.tio.sitexxx.web.server.controller.base;

import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.jfinal.P;
import org.tio.utils.resp.Resp;

@RequestPath("/app")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/AppController.class */
public class AppController {
    private static Logger log = LoggerFactory.getLogger(AppController.class);

    @RequestPath("/conf")
    public Resp conf(HttpRequest request) throws Exception {
        WebUtils.getRequestExt(request);
        Map<String, Object> map = new HashMap<>();
        map.put("res_server", P.get("res.server"));
        return Resp.ok(map);
    }
}
