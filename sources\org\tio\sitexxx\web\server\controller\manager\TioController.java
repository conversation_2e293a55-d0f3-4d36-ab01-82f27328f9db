package org.tio.sitexxx.web.server.controller.manager;

import cn.hutool.core.util.StrUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.redisson.api.RTopic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.ChannelContext;
import org.tio.core.maintain.IpStats;
import org.tio.http.common.HeaderName;
import org.tio.http.common.HeaderValue;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.annotation.RequestPath;
import org.tio.http.server.util.Resps;
import org.tio.jfinal.plugin.activerecord.Db;
import org.tio.jfinal.plugin.activerecord.Record;
import org.tio.sitexxx.service.init.RedisInit;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.WxApp;
import org.tio.sitexxx.service.service.base.RegisterService;
import org.tio.sitexxx.service.service.base.TioIpPullblackLogService;
import org.tio.sitexxx.service.service.base.UserService;
import org.tio.sitexxx.service.service.conf.IpWhiteListService;
import org.tio.sitexxx.servicecommon.vo.Devicetype;
import org.tio.sitexxx.servicecommon.vo.topic.CleanViewCacheVo;
import org.tio.sitexxx.web.server.utils.TioIpPullblackUtils;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.Constants;
import org.tio.utils.resp.Resp;
import org.tio.utils.ui.layui.table.LayuiPage;

@RequestPath("/m/tio")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/manager/TioController.class */
public class TioController {
    private static Logger log = LoggerFactory.getLogger(TioController.class);

    public static void main(String[] args) {
    }

    @RequestPath("/initWx")
    public Resp initWx() {
        List<User> list = User.dao.find("select * from user where id <= 23351");
        for (User user : list) {
            try {
                RegisterService.me.initWx(user);
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return Resp.ok();
    }

    @RequestPath("/clearStaticResCache")
    public Resp clearStaticResCache(HttpRequest request) throws Exception {
        CleanViewCacheVo cleanViewCacheVo = new CleanViewCacheVo();
        RTopic topic = RedisInit.get().getTopic("CLEAN_VIEW_CACHE");
        topic.publish(cleanViewCacheVo);
        return Resp.ok("缓存已经清空");
    }

    @RequestPath("/ipStat")
    public Resp ipStat(Long duration, HttpRequest request, ChannelContext channelContext) throws Exception {
        IpStats ipStats = channelContext.tioConfig.ipStats;
        return Resp.ok(LayuiPage.ok(ipStats.values(duration), ipStats.size(duration).longValue()));
    }

    @RequestPath("/addWhiteIp")
    public Resp addWhiteIp(HttpRequest request, String ip) throws Exception {
        String ip2 = StrUtil.trim(ip);
        User currUser = WebUtils.currUser(request);
        String clientip = request.getClientIp();
        Integer curruserid = currUser.getId();
        String remark = curruserid + Constants.SPE1 + clientip;
        IpWhiteListService.me.save(ip2, remark);
        return Resp.ok();
    }

    @RequestPath("/addSelfToWhiteIp")
    public Resp addSelfToWhiteIp(HttpRequest request) throws Exception {
        User currUser = WebUtils.currUser(request);
        String clientip = request.getClientIp();
        Integer curruserid = currUser.getId();
        String remark = "把自己设为白名单, " + curruserid + ", " + clientip;
        IpWhiteListService.me.save(clientip, remark);
        return Resp.ok();
    }

    @RequestPath("/deleteSelfToWhiteIp")
    public Resp deleteSelfToWhiteIp(HttpRequest request) throws Exception {
        User currUser = WebUtils.currUser(request);
        String clientip = request.getClientIp();
        Integer curruserid = currUser.getId();
        String remark = "将自己从白名单中删除, " + curruserid + ", " + clientip;
        IpWhiteListService.me.delete(clientip, remark);
        return Resp.ok();
    }

    @RequestPath("/deleteWhiteIp")
    public Resp deleteWhiteIp(HttpRequest request, String ip) throws Exception {
        String ip2 = StrUtil.trim(ip);
        User currUser = WebUtils.currUser(request);
        String clientip = request.getClientIp();
        Integer curruserid = currUser.getId();
        String remark = curruserid + Constants.SPE1 + clientip;
        IpWhiteListService.me.delete(ip2, remark);
        return Resp.ok();
    }

    @RequestPath("/addBlackIp")
    public Resp addBlackIp(HttpRequest request, String ip) throws Exception {
        String ip2 = StrUtil.trim(ip);
        User currUser = WebUtils.currUser(request);
        String clientip = request.getClientIp();
        Integer curruserid = currUser.getId();
        String remark = curruserid + Constants.SPE1 + clientip;
        TioIpPullblackUtils.addToBlack(request, ip2, remark, (byte) 3);
        return Resp.ok();
    }

    @RequestPath("/deleteBlackIp")
    public Resp deleteBlackIp(HttpRequest request, String ip) throws Exception {
        String ip2 = StrUtil.trim(ip);
        User currUser = WebUtils.currUser(request);
        String clientip = request.getClientIp();
        Integer curruserid = currUser.getId();
        String remark = curruserid + Constants.SPE1 + clientip;
        TioIpPullblackLogService.ME.deleteFromBlack(ip2, request.getChannelContext().getServerNode().getPort(), remark);
        return Resp.ok();
    }

    @RequestPath("/pullBlackUser")
    public Resp pullBlackUser(HttpRequest request, String nick) throws Exception {
        int r = UserService.ME.pullBlackUserByNick(nick);
        if (r > 0) {
            return Resp.ok();
        }
        return Resp.fail("拉黑失败，请检查昵称是否正确");
    }

    @RequestPath("/normalUser")
    public Resp normalUser(HttpRequest request, String nick) throws Exception {
        int r = UserService.ME.normalUserByNick(nick);
        if (r > 0) {
            return Resp.ok();
        }
        return Resp.fail("操作失败，请检查昵称是否正确");
    }

    @RequestPath("/clearAllUserCache")
    public Resp clearAllUserCache(HttpRequest request) throws Exception {
        UserService.ME.notifyClearCache((Integer) null);
        return Resp.ok();
    }

    @RequestPath("/clearUserCache")
    public Resp clearUserCache(HttpRequest request, String nick) throws Exception {
        User user = UserService.ME.getByNick(nick);
        if (user == null) {
            return Resp.fail("昵称不存在");
        }
        UserService.ME.notifyClearCache(user.getId());
        return Resp.ok();
    }

    @RequestPath("/q")
    /* renamed from: q */
    public HttpResponse m3q(HttpRequest request, String db, String sql) throws Exception {
        if (!IpWhiteListService.isWhiteIp(request.getClientIp())) {
            return Resps.html(request, "你没资格执行该操作");
        }
        if (StrUtil.isBlank(db)) {
            db = "tio_site_main";
        }
        List<Record> list = Db.use(db).find(sql);
        HttpResponse response = Resps.json(request, LayuiPage.ok(list, list.size()));
        response.addHeader(HeaderName.Access_Control_Allow_Origin, HeaderValue.from("*"));
        response.addHeader(HeaderName.Access_Control_Allow_Headers, HeaderValue.from("x-requested-with,content-type"));
        return response;
    }

    @RequestPath("/avatar")
    public HttpResponse avatar(HttpRequest request) throws Exception {
        return null;
    }

    @RequestPath("/addRoleByLoginname")
    public Resp addRoleByLoginname(HttpRequest request, String loginname, byte roleid) {
        return UserService.ME.addRoleByLoginname(loginname, roleid);
    }

    @RequestPath("/addRoleByLNick")
    public Resp addRoleByLNick(HttpRequest request, String nick, byte roleid) {
        return UserService.ME.addRoleByNick(nick, roleid);
    }

    @RequestPath("/tioAppConfig")
    public Resp tioAppConfig(HttpRequest request) throws Exception {
        WxApp sysVersion = WxApp.dao.findFirst("select * from wx_app where type = ? and `status` = ?", new Object[]{Devicetype.ANDROID.getValue(), (byte) 1});
        Map<String, WxApp> config = new HashMap<>();
        config.put("android", sysVersion);
        return Resp.ok(config);
    }
}
