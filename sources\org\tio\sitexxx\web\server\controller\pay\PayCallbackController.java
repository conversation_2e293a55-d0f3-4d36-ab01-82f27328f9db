package org.tio.sitexxx.web.server.controller.pay;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.sitexxx.service.pay.base.BasePayResp;
import org.tio.sitexxx.service.pay.service.PayService;

@RequestPath("/paycallback")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/pay/PayCallbackController.class */
public class PayCallbackController {
    private static Logger log = LoggerFactory.getLogger(PayCallbackController.class);

    @RequestPath("/recharge")
    public String recharge(HttpRequest request, Integer uid) throws Exception {
        BasePayResp resp = PayService.me.rechargeCallback(request, uid);
        if (!resp.isOk()) {
            return "SUCCESS";
        }
        return "SUCCESS";
    }

    @RequestPath("/withhold")
    public String withhold(HttpRequest request, Integer uid) throws Exception {
        BasePayResp resp = PayService.me.withholdCallback(request, uid);
        if (!resp.isOk()) {
            log.error(resp.getMsg());
            return "SUCCESS";
        }
        return "SUCCESS";
    }

    @RequestPath("/redpacket")
    public String redpacket(HttpRequest request, Integer uid) throws Exception {
        BasePayResp resp = PayService.me.redpacketCallback(request, uid);
        if (!resp.isOk()) {
            log.error(resp.getMsg());
            return "SUCCESS";
        }
        return "SUCCESS";
    }
}
