package org.tio.sitexxx.web.server.topic;

import cn.hutool.core.util.StrUtil;
import java.util.Objects;
import org.redisson.api.listener.MessageListener;
import org.tio.core.Tio;
import org.tio.sitexxx.servicecommon.vo.topic.PullIpToBlackVo;
import org.tio.sitexxx.web.server.init.WebApiInit;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/topic/TopicPullIpToBlackListener.class */
public class TopicPullIpToBlackListener implements MessageListener<PullIpToBlackVo> {

    /* renamed from: me */
    public static final TopicPullIpToBlackListener f33me = new TopicPullIpToBlackListener();

    private TopicPullIpToBlackListener() {
    }

    public static void main(String[] args) {
    }

    public void onMessage(CharSequence channel, PullIpToBlackVo pullIpToBlackVo) {
        String ip = pullIpToBlackVo.getIp();
        if (StrUtil.isNotBlank(ip)) {
            if (Objects.equals(pullIpToBlackVo.getType(), PullIpToBlackVo.Type.ADD_BLACK_IP)) {
                Tio.IpBlacklist.add(WebApiInit.serverTioConfig, ip);
            } else {
                Tio.IpBlacklist.remove(WebApiInit.serverTioConfig, ip);
            }
        }
    }
}
