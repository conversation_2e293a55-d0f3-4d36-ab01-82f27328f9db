package org.tio.sitexxx.web.server.controller.base;

import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.server.annotation.RequestPath;
import org.tio.sitexxx.service.model.conf.Dict;
import org.tio.sitexxx.service.service.conf.DictService;
import org.tio.utils.resp.Resp;

@RequestPath("/dict")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/DictController.class */
public class DictController {
    private static Logger log = LoggerFactory.getLogger(DictController.class);

    @RequestPath("/child")
    public Resp child(String code) throws Exception {
        List<Dict> child = DictService.getChildDictByParentCode(code);
        return Resp.ok().data(child);
    }

    @RequestPath("/info")
    public Resp videoInfo(String code) throws Exception {
        Dict dict = DictService.getDictByCode(code);
        return Resp.ok().data(dict);
    }
}
