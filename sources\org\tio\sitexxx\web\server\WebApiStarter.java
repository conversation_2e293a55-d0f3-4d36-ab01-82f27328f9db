package org.tio.sitexxx.web.server;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.sitexxx.service.init.CacheInit;
import org.tio.sitexxx.service.init.JFInit;
import org.tio.sitexxx.service.init.JsonInit;
import org.tio.sitexxx.service.init.PropInit;
import org.tio.sitexxx.service.init.RedisInit;
import org.tio.sitexxx.service.ip2region.Ip2RegionInit;
import org.tio.sitexxx.service.service.base.SensitiveWordsService;
import org.tio.sitexxx.web.server.init.TopicInit;
import org.tio.sitexxx.web.server.init.WebApiInit;
import org.tio.utils.quartz.QuartzUtils;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/WebApiStarter.class */
public class WebApiStarter {
    private static Logger log = LoggerFactory.getLogger(WebApiStarter.class);

    public static void main(String[] args) throws Exception {
        System.out.println("------------WebApi初始化---------------");
        try {
            PropInit.init();
            Ip2RegionInit.init();
            SensitiveWordsService.init();
            JsonInit.init();
            JFInit.init();
            CacheInit.init(true);
            RedisInit.init(true);
            TopicInit.init();
            WebApiInit.init();
            QuartzUtils.start();
        } catch (Throwable e) {
            log.error("", e);
            System.exit(1);
        }
    }
}
