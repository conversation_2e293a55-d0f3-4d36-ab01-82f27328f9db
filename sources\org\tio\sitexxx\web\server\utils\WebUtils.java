package org.tio.sitexxx.web.server.utils;

import cn.hutool.core.util.StrUtil;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.session.HttpSession;
import org.tio.sitexxx.service.model.main.IpInfo;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.UserAgent;
import org.tio.sitexxx.service.model.main.UserToken;
import org.tio.sitexxx.service.service.base.IpInfoService;
import org.tio.sitexxx.service.service.base.UserAgentService;
import org.tio.sitexxx.service.service.base.UserRoleService;
import org.tio.sitexxx.service.service.base.UserService;
import org.tio.sitexxx.service.service.base.UserTokenService;
import org.tio.sitexxx.service.topic.Topics;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.service.vo.SessionExt;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.sitexxx.web.server.init.WebApiInit;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.Constants;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.HttpSchema;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/utils/WebUtils.class */
public class WebUtils {
    private static Logger log = LoggerFactory.getLogger(WebUtils.class);
    private static UserService userService = UserService.ME;

    public static void logout(Integer uid) {
        HttpSession session;
        SessionExt sessionExt;
        List<UserToken> list = UserTokenService.me.find(uid.intValue());
        if (list != null && list.size() > 0) {
            for (UserToken userToken : list) {
                String token = userToken.getToken();
                if (StrUtil.isNotBlank(token) && (session = WebApiInit.httpConfig.getSessionStore().get(token)) != null && (sessionExt = session.getAttribute("SESSION_EXT", SessionExt.class, (Serializable) null, WebApiInit.httpConfig)) != null && sessionExt.getUid() != null) {
                    sessionExt.setUid((Integer) null);
                    session.update(WebApiInit.httpConfig);
                }
            }
            UserTokenService.me.delete(uid.intValue());
        }
    }

    public static void removeHttpcache(String path, Map<String, Object> params, Integer currUid) {
        Topics.notifyRemoveHttpCache(path, currUid, params, 1);
    }

    public static void clearHttpcache(String path) {
        Topics.notifyRemoveHttpCache(path, (Integer) null, (Map) null, 2);
    }

    public static String path(String path) {
        String contextpath = Const.API_CONTEXTPATH;
        String suffix = Const.API_SUFFIX;
        int x = StringUtils.indexOf(path, Constants.SPE5);
        if (x == -1) {
            return contextpath + path + suffix;
        }
        String path1 = path.substring(0, x);
        String queryStr = path.substring(x + 1, path.length());
        return contextpath + path1 + suffix + Constants.SPE5 + queryStr;
    }

    public static User currUser(HttpRequest request) {
        Integer userid = currUserId(request);
        if (userid != null) {
            User user = userService.getById(userid);
            if (UserRoleService.checkUserStatus(user)) {
                return user;
            }
            return null;
        }
        return null;
    }

    public static Integer currUserId(HttpRequest request) {
        HttpSession session = request.getHttpSession();
        SessionExt sessionExt = getSessionExt(session);
        Integer userid = sessionExt.getUid();
        return userid;
    }

    public static SessionExt getSessionExt(HttpRequest request) {
        SessionExt sessionExt = getSessionExt(request.getHttpSession());
        return sessionExt;
    }

    public static SessionExt getSessionExt(HttpSession session) {
        SessionExt sessionExt = (SessionExt) session.getAttribute("SESSION_EXT", SessionExt.class);
        return sessionExt;
    }

    public static Integer getUserIdBySession(HttpSession session) {
        if (session == null) {
            return null;
        }
        SessionExt sessionExt = getSessionExt(session);
        Integer userid = sessionExt.getUid();
        return userid;
    }

    public static RequestExt getRequestExt(HttpRequest request) {
        return (RequestExt) request.getAttribute("TIO_SITE_REQUESTEXT");
    }

    public static int getImServerPort(HttpRequest request) {
        RequestExt requestExt = getRequestExt(request);
        if (requestExt.isFromBrowser()) {
            return Const.ImPort.WS.intValue();
        }
        return Const.ImPort.APP.intValue();
    }

    public static String getImServerIP(HttpRequest request) {
        RequestExt requestExt = getRequestExt(request);
        if (requestExt.isFromBrowser()) {
            return Const.MY_IP_WEB;
        }
        return Const.MY_IP_APP;
    }

    public static Map<String, Object> getMapParams(HttpRequest request) {
        Map<String, Object> params = new HashMap<>();
        if (request.getParams() != null) {
            Map<String, Object[]> paramArray = request.getParams();
            for (String key : paramArray.keySet()) {
                Object[] param = paramArray.get(key);
                if (param != null && param.length >= 1) {
                    params.put(key, param[0]);
                }
            }
        }
        return params;
    }

    public static IpInfo getIpInfo(HttpRequest request) {
        String ip = request.getClientIp();
        return IpInfoService.ME.save(ip);
    }

    public static UserAgent getUserAgent(HttpRequest request) {
        String userAgentStr = request.getUserAgent();
        return UserAgentService.ME.save(userAgentStr);
    }

    public static String resUrl(String path) {
        if (StrUtil.startWithIgnoreCase(path, HttpSchema.HTTP) || StrUtil.startWithIgnoreCase(path, HttpSchema.HTTPS) || StrUtil.startWithIgnoreCase(path, "//")) {
            return path;
        }
        return Const.RES_SERVER + path;
    }
}
