package org.tio.sitexxx.web.server.utils;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.util.List;
import net.coobird.thumbnailator.Thumbnails;
import org.opencv.core.Mat;
import org.opencv.core.Size;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.UploadFile;
import org.tio.sitexxx.service.model.main.Img;
import org.tio.sitexxx.servicecommon.vo.Const;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/utils/ImgUtils.class */
public class ImgUtils {
    private static Logger log = LoggerFactory.getLogger(ImgUtils.class);

    /* renamed from: c */
    static long f34c = 0;

    public static void scale(String srcImg, String desImg, double scale) {
        Mat src = Imgcodecs.imread(srcImg);
        Mat dst = src.clone();
        Imgproc.resize(src, dst, new Size(src.width() * scale, src.height() * scale));
        Imgcodecs.imwrite(desImg, dst);
    }

    public static void scale(String srcImg, String desImg, double scale, double quality) throws Exception {
        Thumbnails.of(new String[]{srcImg}).scale(scale).outputQuality(quality).toFile(desImg);
    }

    public static void main(String[] args) throws IOException {
        List<File> files = FileUtil.loopFiles("F:\\work\\tio-site\\some\\技术白皮书", new FileFilter() { // from class: org.tio.sitexxx.web.server.utils.ImgUtils.1
            @Override // java.io.FileFilter
            public boolean accept(File pathname) {
                String ext = FileUtil.extName(pathname);
                if ((!ext.equalsIgnoreCase("png") && !ext.equalsIgnoreCase("jpg")) || pathname.getName().startsWith("scaled_")) {
                    return false;
                }
                return true;
            }
        });
        for (File file : files) {
            try {
                byte[] imageBytes = FileUtil.readBytes(file);
                BufferedImage bi = ImgUtil.toImage(imageBytes);
                if (bi.getWidth() > 1080) {
                    float scale = calcScaleWithWidth(1080, bi);
                    String imgFilePath = file.getCanonicalPath();
                    File desFile = new File(file.getParent(), "scaled_" + scale + "_" + file.getName());
                    String smImgFilePath = desFile.getCanonicalPath();
                    Thumbnails.of(new String[]{imgFilePath}).scale(scale).outputQuality(0.8f).toFile(smImgFilePath);
                }
            } catch (Exception e) {
                try {
                    log.error(file.getCanonicalPath(), e);
                } catch (IOException e1) {
                    log.error(e1.toString(), e1);
                }
            }
        }
    }

    public static float calcScaleWithWidth(int maxWidth, BufferedImage initBufferedImage) {
        int initWidth = initBufferedImage.getWidth();
        int newWidth = Math.min(maxWidth, initWidth);
        return newWidth / initWidth;
    }

    public static float calcScaleWithHeight(int maxHeight, BufferedImage initBufferedImage) {
        int initHeight = initBufferedImage.getHeight();
        int newHeight = Math.min(maxHeight, initHeight);
        return newHeight / initHeight;
    }

    public static Img processImg(String subDir, Integer uid, UploadFile uploadFile, float scale) throws Exception {
        String smImgUrl;
        File smImgFile;
        String ext = FileUtil.extName(uploadFile.getName());
        if (StrUtil.isBlank(ext)) {
            ext = "gif";
        }
        boolean needScale = !"gif".equals(ext);
        String imgUrlWithoutExt = UploadUtils.newFile(subDir, uid.intValue(), uploadFile.getName());
        String imgUrl = imgUrlWithoutExt + "." + ext;
        byte[] imgBytes = uploadFile.getData();
        if (needScale) {
            smImgUrl = imgUrlWithoutExt + "_sm." + ext;
        } else {
            smImgUrl = imgUrl;
        }
        String imgFilePath = Const.RES_ROOT + imgUrl;
        String smImgFilePath = Const.RES_ROOT + smImgUrl;
        File imgFile = new File(imgFilePath);
        FileUtil.mkParentDirs(imgFilePath);
        if (needScale) {
            smImgFile = new File(smImgFilePath);
            FileUtil.mkParentDirs(smImgFile);
        } else {
            smImgFile = imgFile;
        }
        BufferedImage bi = ImgUtil.toImage(imgBytes);
        Img img = new Img();
        if (needScale) {
            Thumbnails.of(new BufferedImage[]{bi}).scale(1.0d).outputQuality(0.5d).toFile(imgFilePath);
            if (scale < 1.0f) {
                Thumbnails.of(new BufferedImage[]{bi}).scale(scale).outputQuality(0.5d).toFile(smImgFilePath);
            } else {
                FileUtil.copy(imgFilePath, smImgFilePath, true);
            }
        } else {
            FileUtil.writeBytes(imgBytes, imgFile);
        }
        BufferedImage smBi = ImgUtil.read(smImgFile);
        img.setCoverheight(Integer.valueOf(smBi.getHeight()));
        img.setCoversize(Integer.valueOf((int) FileUtil.size(smImgFile)));
        img.setCoverwidth(Integer.valueOf(smBi.getWidth()));
        img.setCoverurl(smImgUrl);
        img.setUrl(imgUrl);
        img.setUid(uid);
        String filename = uploadFile.getName();
        img.setFilename(filename);
        img.setHeight(Integer.valueOf(bi.getHeight()));
        img.setWidth(Integer.valueOf(bi.getWidth()));
        img.setSize(Long.valueOf(FileUtil.size(imgFile)));
        img.setTitle(filename);
        return img;
    }

    public static Img processImg(String subDir, UploadFile uploadFile) throws Exception {
        String ext = FileUtil.extName(uploadFile.getName());
        if (StrUtil.isBlank(ext)) {
            ext = "gif";
        }
        String imgUrlWithoutExt = UploadUtils.dateFile(subDir);
        String imgUrl = imgUrlWithoutExt + "." + ext;
        byte[] imgBytes = uploadFile.getData();
        String imgFilePath = Const.RES_ROOT + imgUrl;
        File imgFile = new File(imgFilePath);
        FileUtil.mkParentDirs(imgFilePath);
        BufferedImage bi = ImgUtil.toImage(imgBytes);
        Img img = new Img();
        FileUtil.writeBytes(imgBytes, imgFile);
        img.setCoverheight(Integer.valueOf(bi.getHeight()));
        img.setCoversize(Integer.valueOf((int) FileUtil.size(imgFile)));
        img.setCoverwidth(Integer.valueOf(bi.getWidth()));
        img.setCoverurl(imgUrl);
        img.setUrl(imgUrl);
        img.setUid((Integer) null);
        String filename = uploadFile.getName();
        img.setFilename(filename);
        img.setHeight(Integer.valueOf(bi.getHeight()));
        img.setWidth(Integer.valueOf(bi.getWidth()));
        img.setSize(Long.valueOf(FileUtil.size(imgFile)));
        img.setTitle(filename);
        return img;
    }
}
