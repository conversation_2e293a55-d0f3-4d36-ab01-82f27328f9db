package org.tio.sitexxx.web.server.controller.recharge;

import cn.hutool.core.util.StrUtil;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.tio.http.common.HttpConfig;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.annotation.RequestPath;
import org.tio.http.server.mvc.Routes;
import org.tio.http.server.util.Resps;
import org.tio.sitexxx.service.cache.CacheConfig;
import org.tio.sitexxx.service.cache.Caches;
import org.tio.sitexxx.service.model.main.RechargeItem;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.UserAgent;
import org.tio.sitexxx.service.service.base.UserAgentService;
import org.tio.sitexxx.service.service.recharge.RechargeItemService;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.servicecommon.utils.LogUtils;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.sitexxx.web.server.controller.base.QrCodeController;
import org.tio.sitexxx.web.server.recharge.IRechargeProvider;
import org.tio.sitexxx.web.server.recharge.RechargeServiceFactory;
import org.tio.sitexxx.web.server.recharge.provider.weixin.WxPayScan2RechargeProvider;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.cache.ICache;
import org.tio.utils.jfinal.P;
import org.tio.utils.json.Json;
import org.tio.utils.resp.Resp;

@RequestPath("/recharge")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/recharge/RechargeController.class */
public class RechargeController {
    private static Logger log = LogUtils.getCoinLog();

    public static String newTradeno(HttpRequest request) {
        String ret = WebUtils.currUserId(request) + RandomStringUtils.randomAlphabetic(5) + new SimpleDateFormat("yyMMddHHmmssSSS").format(new Date());
        return ret;
    }

    public static boolean isFromWeixin(HttpRequest request) {
        String userAgent = request.getUserAgent();
        if (userAgent != null && userAgent.contains("MicroMessenger")) {
            return true;
        }
        return false;
    }

    public static boolean isFromAlipay(HttpRequest request) {
        String userAgent = request.getUserAgent();
        if (userAgent != null && userAgent.contains("AlipayClient")) {
            return true;
        }
        return false;
    }

    @RequestPath("/qr")
    /* renamed from: qr */
    public HttpResponse m4qr(HttpRequest request, RechargeItem rechargeItem, Integer width, Integer height) throws Exception {
        if (width == null) {
            width = 200;
        }
        if (height == null) {
            height = 200;
        }
        Integer width2 = Integer.valueOf(Math.min(width.intValue(), 1200));
        Integer height2 = Integer.valueOf(Math.min(height.intValue(), 1200));
        IRechargeProvider rechargeProvider = RechargeServiceFactory.getThirdRechargeService(rechargeItem.getPaytype());
        QrCodeController qrCodeController = (QrCodeController) Routes.getController(QrCodeController.class);
        if (rechargeProvider instanceof WxPayScan2RechargeProvider) {
            request.setAttribute("recharge_only_qr", 1);
            request.setAttribute("recharge_only_qr_width", width2);
            request.setAttribute("recharge_only_qr_height", height2);
            return submit(request, rechargeItem);
        }
        HttpConfig httpConfig = request.httpConfig;
        String uuid = httpConfig.getSessionIdGenerator().sessionId(httpConfig, request);
        ICache cache = Caches.getCache(CacheConfig.RECHARGE_QR);
        cache.put(uuid, rechargeItem);
        String str = Const.SITE + httpConfig.getContextPath() + "/recharge/qrSubmit/" + uuid + "/" + request.getHttpSession().getId() + httpConfig.getSuffix();
        return qrCodeController.index(width2, height2, null, null, str, request);
    }

    @RequestPath("/qrSubmit/{uuid}/{tio_http_sessionid}")
    public HttpResponse qrSubmit(HttpRequest request, String uuid, String tio_http_sessionid) throws Exception {
        ICache cache = Caches.getCache(CacheConfig.RECHARGE_QR);
        RechargeItem rechargeItem = cache.get(uuid);
        if (rechargeItem == null) {
            return Resps.json(request, Resp.fail("二维码失效或过期"));
        }
        if (isFromAlipay(request)) {
            rechargeItem.setPaytype(RechargeItem.Paytype.ALIPAY_H5);
            request.setAttribute("rechargeItem", rechargeItem);
            return Resps.forward(request, "/recharge");
        }
        return Resps.html(request, "请用支付宝");
    }

    @RequestPath("")
    public HttpResponse submit(HttpRequest request, RechargeItem rechargeItem) throws Exception {
        RechargeItem rechargeItem1 = (RechargeItem) request.getAttribute("rechargeItem");
        if (rechargeItem1 != null) {
            rechargeItem = rechargeItem1;
        }
        Double costmoney = rechargeItem.getAmount();
        Double MIN_PAY = Double.valueOf(P.get("recharge.least.money", "1"));
        if (costmoney == null || costmoney.doubleValue() < MIN_PAY.doubleValue()) {
            return Resps.json(request, Resp.fail().msg("最低充值额为" + MIN_PAY + "元哦"));
        }
        Integer paytype = rechargeItem.getPaytype();
        IRechargeProvider rechargeProvider = RechargeServiceFactory.getThirdRechargeService(paytype);
        if (rechargeProvider == null) {
            return Resps.html(request, "请选择正确的支付方式!");
        }
        User user = WebUtils.currUser(request);
        if (user == null) {
            return Resps.html(request, "请先登录!");
        }
        if (StrUtil.isBlank(rechargeItem.getCallback())) {
            rechargeItem.setCallback(rechargeItem.getReferer());
        }
        Integer userid = user.getId();
        String tradeno = newTradeno(request);
        rechargeItem.setStatus(1);
        rechargeItem.setUserid(userid);
        rechargeItem.setTradeno(tradeno);
        RequestExt requestExt = WebUtils.getRequestExt(request);
        rechargeItem.setClienttype(Integer.valueOf(requestExt.getDeviceType()));
        String userAgentStr = request.getUserAgent();
        UserAgent userAgent = UserAgentService.ME.save(userAgentStr);
        if (userAgent.getId() != null) {
            rechargeItem.setUseragentid(userAgent.getId());
        } else {
            rechargeItem.setClientinfo(StringUtils.left(userAgentStr, 255));
        }
        rechargeItem.setRemoteip(request.getClientIp());
        Date date = new Date();
        rechargeItem.setCreatetime(date);
        rechargeItem.setUpdatetime(date);
        RechargeItemService.me.save(rechargeItem);
        return rechargeProvider.toThirdRechargePage(request, rechargeItem, paytype.intValue());
    }

    @RequestPath("/rt/{paytype}")
    public HttpResponse returnurl(HttpRequest request, Integer paytype) throws Exception {
        IRechargeProvider rechargeProvider = RechargeServiceFactory.getThirdRechargeService(paytype);
        if (rechargeProvider == null) {
            request.close();
            return null;
        }
        Boolean isValidCallback = Boolean.valueOf(rechargeProvider.isValidCallback(request, 1));
        if (!isValidCallback.booleanValue()) {
            log.error("非法return url回调\r\n{}", request.toString());
            request.close();
            return null;
        }
        String tradeno = rechargeProvider.getTradeno(request, 1);
        RechargeItem rechargeItem = RechargeItemService.me.selectByTradeno(tradeno);
        if (rechargeItem == null) {
            log.error("找不到充值记录, tradeno: {}\r\n{}", tradeno, request.toString());
            return Resps.redirect(request, Const.SITE + "/");
        }
        return Resps.redirect(request, rechargeItem.getCallback());
    }

    @RequestPath("/nf/{paytype}")
    public HttpResponse notifyurl(HttpRequest request, Integer paytype) throws Exception {
        log.info("收到notifyurl回调,from {}\r\n{}", request.getClientIp(), request.toString());
        IRechargeProvider rechargeProvider = RechargeServiceFactory.getThirdRechargeService(paytype);
        if (rechargeProvider == null) {
            request.close();
            return null;
        }
        Boolean isValidCallback = Boolean.valueOf(rechargeProvider.isValidCallback(request, 2));
        if (!isValidCallback.booleanValue()) {
            log.info("非法notifyurl回调,from {}\r\n{}", request.getClientIp(), request.toString());
            request.close();
            return null;
        }
        String tradeno = rechargeProvider.getTradeno(request, 2);
        RechargeItem rechargeItem = RechargeItemService.me.selectByTradeno(tradeno);
        if (rechargeItem == null) {
            log.error("找不到充值记录, tradeno: {}\r\n{}", tradeno, request.toString());
            return rechargeProvider.notifyFail(request, rechargeItem, paytype.intValue());
        }
        rechargeItem.setRemoteipnotify(request.getClientIp());
        if (!Objects.equals(rechargeItem.getStatus(), 1)) {
            log.error("该订单已经处理， 不允许再次处理 tradeno: {}\r\n{}", tradeno, request.toString());
            return rechargeProvider.notifySuccess(request, rechargeItem, paytype.intValue());
        }
        try {
            rechargeProvider.fillOnNotify(request, rechargeItem, paytype.intValue());
            Integer tradeStatus = rechargeItem.getStatus();
            if (Objects.equals(tradeStatus, 2) || Objects.equals(tradeStatus, 4)) {
                try {
                    log.info("充值成功了:{}", Json.toFormatedJson(rechargeItem));
                    RechargeItemService.me.updateUserAndRecharge(rechargeItem);
                    return rechargeProvider.notifySuccess(request, rechargeItem, paytype.intValue());
                } catch (Exception e) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("msg", "充值交易成功，但更新数据库时出现异常");
                    map.put("recharge", rechargeItem);
                    map.put("exception", ExceptionUtils.getStackTrace(e));
                    log.error(Json.toFormatedJson(map));
                    return rechargeProvider.notifyFail(request, rechargeItem, paytype.intValue());
                }
            }
            if (Objects.equals(tradeStatus, 3)) {
                try {
                    log.error("充值取消了:{}", Json.toFormatedJson(rechargeItem));
                    RechargeItemService.me.update(rechargeItem);
                    return rechargeProvider.notifySuccess(request, rechargeItem, paytype.intValue());
                } catch (Exception e2) {
                    Map<String, Object> map2 = new HashMap<>();
                    map2.put("msg", "充值交易关闭，更新数据库时出现异常");
                    map2.put("recharge", rechargeItem);
                    map2.put("exception", ExceptionUtils.getStackTrace(e2));
                    log.error(Json.toFormatedJson(map2));
                    return rechargeProvider.notifyFail(request, rechargeItem, paytype.intValue());
                }
            }
            if (Objects.equals(tradeStatus, 9)) {
                try {
                    log.error("充值受到攻击了:{}", Json.toFormatedJson(rechargeItem));
                    RechargeItemService.me.attackRecharge(rechargeItem);
                    return rechargeProvider.notifySuccess(request, rechargeItem, paytype.intValue());
                } catch (Exception e3) {
                    Map<String, Object> map3 = new HashMap<>();
                    map3.put("msg", "充值交易状态为未知状态，更新数据库时出现异常");
                    map3.put("recharge", rechargeItem);
                    map3.put("exception", ExceptionUtils.getStackTrace(e3));
                    log.error(Json.toFormatedJson(map3));
                    return rechargeProvider.notifyFail(request, rechargeItem, paytype.intValue());
                }
            }
            if (Objects.equals(tradeStatus, 99)) {
                try {
                    log.error("充值异常了:{}", Json.toFormatedJson(rechargeItem));
                    RechargeItemService.me.update(rechargeItem);
                    return rechargeProvider.notifySuccess(request, rechargeItem, paytype.intValue());
                } catch (Exception e4) {
                    Map<String, Object> map4 = new HashMap<>();
                    map4.put("msg", "充值交易状态为未知状态，更新数据库时出现异常");
                    map4.put("recharge", rechargeItem);
                    map4.put("exception", ExceptionUtils.getStackTrace(e4));
                    log.error(Json.toFormatedJson(map4));
                    return rechargeProvider.notifyFail(request, rechargeItem, paytype.intValue());
                }
            }
            if (Objects.equals(tradeStatus, 1)) {
                try {
                    log.error(rechargeProvider.getClass().getName() + ".fillOnNotify()没有修改订单状态:{}", Json.toFormatedJson(rechargeItem));
                    return rechargeProvider.notifyFail(request, rechargeItem, paytype.intValue());
                } catch (Exception e5) {
                    Map<String, Object> map5 = new HashMap<>();
                    map5.put("msg", "notifyFail时异常了");
                    map5.put("recharge", rechargeItem);
                    map5.put("exception", ExceptionUtils.getStackTrace(e5));
                    log.error(Json.toFormatedJson(map5));
                    return rechargeProvider.notifyFail(request, rechargeItem, paytype.intValue());
                }
            }
            return rechargeProvider.notifyFail(request, rechargeItem, paytype.intValue());
        } catch (Exception e1) {
            log.error("充值回调异常", e1);
            throw e1;
        }
    }

    @RequestPath("/getCallback")
    public Resp getCallback(HttpRequest request, String tradeno) throws Exception {
        User user = WebUtils.currUser(request);
        RechargeItem rechargeItem = RechargeItemService.me.selectByTradeno(tradeno);
        if (rechargeItem == null) {
            log.error("找不到充值记录, tradeno: {}\r\n{}", tradeno, request.toString());
            return Resp.ok().data("/");
        }
        if (!Objects.equals(rechargeItem.getUserid(), user.getId())) {
            log.error("无效用户充值记录, tradeno: {}\r\n{}", tradeno, request.toString());
            return Resp.ok().data("/");
        }
        if (Objects.equals(rechargeItem.getStatus(), 2) || Objects.equals(rechargeItem.getStatus(), 4)) {
            String path = rechargeItem.getCallback();
            if (StrUtil.isBlank(path)) {
                path = "/";
            }
            return Resp.ok().data(path);
        }
        return Resp.fail();
    }

    public static void main(String[] args) {
        Byte xx = (byte) 2;
        int xxx = xx.byteValue();
        System.out.println(xxx);
    }
}
