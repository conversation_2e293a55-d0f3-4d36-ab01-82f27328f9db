package org.tio.sitexxx.web.server.controller.base.oauth2;

import java.util.Set;
import org.apache.oltu.oauth2.common.utils.OAuthUtils;
import org.tio.http.common.HttpRequest;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/oauth2/OAuthRequest.class */
public class OAuthRequest {
    private HttpRequest request;

    public OAuthRequest(HttpRequest request) {
        this.request = request;
    }

    public String getParam(String name) {
        return this.request.getParam(name);
    }

    public String getClientId() {
        String[] creds = OAuthUtils.decodeClientAuthenticationHeader(this.request.getHeader("Authorization"));
        if (creds != null) {
            return creds[0];
        }
        return getParam("client_id");
    }

    public String getRedirectURI() {
        return getParam("redirect_uri");
    }

    public String getClientSecret() {
        String[] creds = OAuthUtils.decodeClientAuthenticationHeader(this.request.getHeader("Authorization"));
        if (creds != null) {
            return creds[1];
        }
        return getParam("client_secret");
    }

    public boolean isClientAuthHeaderUsed() {
        return OAuthUtils.decodeClientAuthenticationHeader(this.request.getHeader("Authorization")) != null;
    }

    public Set<String> getScopes() {
        String scopes = getParam("scope");
        return OAuthUtils.decodeScopes(scopes);
    }

    public String getResponseType() {
        return getParam("response_type");
    }

    public static void main(String[] args) {
    }
}
