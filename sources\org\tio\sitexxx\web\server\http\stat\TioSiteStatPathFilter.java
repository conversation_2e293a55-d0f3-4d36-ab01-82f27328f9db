package org.tio.sitexxx.web.server.http.stat;

import cn.hutool.core.io.FileUtil;
import java.util.HashSet;
import java.util.Set;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.stat.StatPathFilter;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/http/stat/TioSiteStatPathFilter.class */
public class TioSiteStatPathFilter implements StatPathFilter {

    /* renamed from: me */
    public static final TioSiteStatPathFilter f21me = new TioSiteStatPathFilter();
    private static Set<String> skipExtSet = new HashSet();

    static {
        skipExtSet.add("css");
        skipExtSet.add("js");
        skipExtSet.add("ico");
        skipExtSet.add("png");
        skipExtSet.add("jpg");
        skipExtSet.add("swf");
        skipExtSet.add("xml");
        skipExtSet.add("gif");
        skipExtSet.add("jpeg");
        skipExtSet.add("woff");
        skipExtSet.add("map");
        skipExtSet.add("txt");
    }

    private TioSiteStatPathFilter() {
    }

    public boolean filter(String path, HttpRequest request, HttpResponse response) {
        String ext = FileUtil.extName(path);
        if (skipExtSet.contains(ext)) {
            return false;
        }
        return true;
    }

    public static void main(String[] args) {
    }
}
