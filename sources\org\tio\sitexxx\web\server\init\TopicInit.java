package org.tio.sitexxx.web.server.init;

import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.tio.sitexxx.service.init.RedisInit;
import org.tio.sitexxx.servicecommon.vo.ClearHttpCache;
import org.tio.sitexxx.servicecommon.vo.topic.PullIpToBlackVo;
import org.tio.sitexxx.web.server.topic.TopicClearHttpCacheListener;
import org.tio.sitexxx.web.server.topic.TopicPullIpToBlackListener;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/init/TopicInit.class */
public class TopicInit {
    public static void init() {
        RedissonClient redisson = RedisInit.get();
        RTopic topic = redisson.getTopic("CLEAR_HTTP_CACHE");
        topic.addListener(ClearHttpCache.class, TopicClearHttpCacheListener.f32me);
        RTopic pullIpToBlackTopic = redisson.getTopic("PULL_IP_TO_BLACK");
        pullIpToBlackTopic.addListener(PullIpToBlackVo.class, TopicPullIpToBlackListener.f33me);
    }
}
