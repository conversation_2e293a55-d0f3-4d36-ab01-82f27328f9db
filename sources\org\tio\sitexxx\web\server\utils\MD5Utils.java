package org.tio.sitexxx.web.server.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/utils/MD5Utils.class */
public class MD5Utils {
    public static String getMd5(String string) throws NoSuchAlgorithmException {
        if (string == null) {
            return null;
        }
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
        }
        if (md == null) {
            return null;
        }
        md.update(string.getBytes());
        byte[] bytes = md.digest();
        StringBuilder buf = new StringBuilder();
        int length = bytes.length;
        for (int i = 0; i < length; i++) {
            int i2 = bytes[i];
            if (i2 < 0) {
                i2 += 256;
            }
            if (i2 < 16) {
                buf.append("0");
            }
            buf.append(Integer.toHexString(i2));
        }
        return buf.toString();
    }
}
