package org.tio.sitexxx.web.server.utils;

import java.io.Serializable;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.session.HttpSession;
import org.tio.sitexxx.service.cache.CacheConfig;
import org.tio.sitexxx.service.cache.Caches;
import org.tio.utils.cache.ICache;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/utils/SessionCacheUtils.class */
public class SessionCacheUtils {
    private SessionCacheUtils() {
    }

    private static String key(HttpRequest request, String sessionKey) {
        HttpSession httpSession = request.getHttpSession();
        String sessionId = httpSession.getId();
        return sessionId + ".key." + sessionKey;
    }

    public static void put(HttpRequest request, String sessionKey, Serializable value) {
        String key1 = key(request, sessionKey);
        ICache cache = getCache();
        cache.put(key1, value);
    }

    public static void remove(HttpRequest request, String sessionKey) {
        String key1 = key(request, sessionKey);
        ICache cache = getCache();
        cache.remove(key1);
    }

    public static Serializable get(HttpRequest request, String sessionKey) {
        String key1 = key(request, sessionKey);
        ICache cache = getCache();
        Serializable ret = cache.get(key1);
        return ret;
    }

    private static ICache getCache() {
        return Caches.getCache(CacheConfig.SESSION_5_MINUTES);
    }

    public static void main(String[] args) {
    }
}
