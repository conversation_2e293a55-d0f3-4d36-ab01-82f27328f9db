package org.tio.sitexxx.web.server.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import java.io.FileFilter;
import java.io.IOException;
import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.UploadFile;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.plugin.activerecord.Db;
import org.tio.jfinal.plugin.activerecord.Record;
import org.tio.sitexxx.service.model.main.File;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.web.server.controller.p004wx.ChatController;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.jfinal.P;
import org.tio.utils.resp.Resp;

@RequestPath("/upload")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/UploadController.class */
public class UploadController {
    private static Logger log = LoggerFactory.getLogger(UploadController.class);

    public static void main(String[] args) {
    }

    @RequestPath("/file")
    public Resp file(HttpRequest request, UploadFile uploadFile) throws Exception {
        try {
            if (uploadFile == null) {
                return Resp.fail("上传信息为空");
            }
            User curr = WebUtils.currUser(request);
            if (curr == null) {
                return Resp.fail("未登录或登录超时");
            }
            String sessionid = request.getHttpSession().getId();
            File dbFile = ChatController.innerUploadFile(curr, uploadFile, sessionid);
            return Resp.ok(dbFile);
        } catch (Exception e) {
            log.error("", e);
            return Resp.fail("系统错误");
        }
    }

    @RequestPath("/img")
    public List<Record> img(HttpRequest request) throws Exception {
        return m0xx(request, "select * from img where updatetime > ?");
    }

    @RequestPath("/video")
    public List<Record> video(HttpRequest request) throws Exception {
        return m0xx(request, "select * from video where updatetime > ?");
    }

    @RequestPath("/all")
    public List<Map<String, Object>> all(HttpRequest request) throws Exception {
        final String rootPath = P.get("res.root");
        List<java.io.File> list = FileUtil.loopFiles(rootPath, new FileFilter() { // from class: org.tio.sitexxx.web.server.controller.UploadController.1
            @Override // java.io.FileFilter
            public boolean accept(java.io.File pathname) {
                try {
                    String path = pathname.getCanonicalPath().substring(rootPath.length());
                    StrUtil.replace(path, "\\", "/");
                    return true;
                } catch (IOException e) {
                    UploadController.log.error("", e);
                    return true;
                }
            }
        });
        List<Map<String, Object>> listMap = new ArrayList<>(list.size());
        for (java.io.File file : list) {
            try {
                Map<String, Object> map = new HashMap<>();
                listMap.add(map);
                String path = file.getCanonicalPath().substring(rootPath.length());
                String path2 = StrUtil.replace(path, "\\", "/");
                map.put("size", Long.valueOf(FileUtil.size(file)));
                map.put("path", path2);
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return listMap;
    }

    /* renamed from: xx */
    public List<Record> m0xx(HttpRequest request, String sql) throws Exception {
        Date date = new Date(System.currentTimeMillis() - 2592000000L);
        List<Record> list = Db.use("tio_site_main").find(sql, new Object[]{date});
        return list;
    }
}
