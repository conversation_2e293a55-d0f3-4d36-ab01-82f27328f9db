package org.tio.sitexxx.web.server.recharge.provider.alipay;

import cn.hutool.core.util.StrUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradePayModel;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.util.Resps;
import org.tio.sitexxx.service.model.main.RechargeItem;
import org.tio.sitexxx.servicecommon.utils.LogUtils;
import org.tio.sitexxx.web.server.recharge.RechargeUtils;
import org.tio.utils.resp.Resp;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/provider/alipay/AlipayAppRechargeProvider.class */
public class AlipayAppRechargeProvider extends AlipayRechargeProvider {
    private static Logger log = LogUtils.getCoinLog();

    /* renamed from: me */
    public static final AlipayAppRechargeProvider f24me = new AlipayAppRechargeProvider();

    @Override // org.tio.sitexxx.web.server.recharge.provider.alipay.AlipayRechargeProvider, org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public HttpResponse toThirdRechargePage(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        Integer paytype = rechargeItem.getPaytype();
        String _subject = rechargeItem.getGoods();
        if (StrUtil.isBlank(_subject)) {
            _subject = "充值";
        }
        String total_fee = String.valueOf(rechargeItem.getAmount());
        AlipayTradePayModel model = new AlipayTradePayModel();
        model.setOutTradeNo(rechargeItem.getTradeno());
        model.setProductCode("QUICK_MSECURITY_PAY");
        model.setTotalAmount(total_fee);
        model.setTimeoutExpress("30m");
        model.setSubject(_subject);
        model.setBody("");
        AlipayClient alipayClient = AlipayConfig.alipayClient;
        AlipayTradeAppPayRequest alipayRequest = new AlipayTradeAppPayRequest();
        alipayRequest.setReturnUrl(RechargeUtils.getReturnUrl(paytype, request));
        alipayRequest.setNotifyUrl(RechargeUtils.getNotifyUrl(paytype, request));
        alipayRequest.setBizModel(model);
        try {
            AlipayTradeAppPayResponse alipayTradeAppPayResponse = alipayClient.sdkExecute(alipayRequest);
            if (alipayTradeAppPayResponse != null) {
                Object retBody = alipayTradeAppPayResponse.getBody();
                Map<String, Object> ret = new HashMap<>();
                ret.put("alipay", retBody);
                return Resps.json(request, Resp.ok(ret));
            }
            return Resps.json(request, Resp.fail("支付宝SDK生成的"));
        } catch (AlipayApiException e) {
            log.error("支付宝支付生成APP支付页面时产生异常", e);
            return Resps.json(request, Resp.fail("支付宝支付生成APP支付页面时产生异常"));
        }
    }
}
