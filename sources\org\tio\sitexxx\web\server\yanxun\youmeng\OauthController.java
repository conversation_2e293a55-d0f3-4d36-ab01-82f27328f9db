package org.tio.sitexxx.web.server.yanxun.youmeng;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.YxOnekeyLoginTmp;
import org.tio.sitexxx.service.service.base.UserService;
import org.tio.sitexxx.service.service.base.sms.SmsService;
import org.tio.sitexxx.service.service.yanxun.onekeylogin.OneKeyLoginTempService;
import org.tio.sitexxx.service.utils.RetUtils;
import org.tio.sitexxx.web.server.controller.base.LoginController;
import org.tio.sitexxx.web.server.controller.base.RegisterController;
import org.tio.sitexxx.web.server.utils.MD5Utils;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.Client;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.Request;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.Response;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.Constants;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.ContentType;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.HttpHeader;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.SystemHeader;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.enums.Method;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.util.MessageDigestUtil;
import org.tio.utils.hutool.StrUtil;
import org.tio.utils.resp.Resp;

@RequestPath("/oauth")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/yanxun/youmeng/OauthController.class */
public class OauthController {
    private static Logger log = LoggerFactory.getLogger(OauthController.class);
    private static OneKeyLoginTempService oneKeyLoginTemp = OneKeyLoginTempService.ME;
    private static final List<String> CUSTOM_HEADERS_TO_SIGN_PREFIX = new ArrayList();
    private static final UserService userService = UserService.ME;

    @RequestPath("/login")
    public Object oauth(HttpRequest request, String token, String appKey) throws Exception {
        String mobile;
        if (StrUtil.isBlank(token)) {
            log.error("token令牌无效");
            return Resp.fail("令牌不合法");
        }
        if (StrUtil.isBlank(appKey)) {
            log.error("appKey未空");
            return Resp.fail("参数错误：未传appKey");
        }
        YxOnekeyLoginTmp temp = new YxOnekeyLoginTmp();
        YxOnekeyLoginTmp loginTmp = oneKeyLoginTemp.findByToken(token);
        if (loginTmp == null) {
            JSONObject body = new JSONObject();
            body.put("token", token);
            Response response = postString("/api/v1/mobile/info", appKey, body.toString(), "");
            JSONObject jsonObject = JSON.parseObject(response.getBody());
            boolean success = ((Boolean) jsonObject.get("success")).booleanValue();
            if (!success) {
                log.error("获取手机号失败：{}", jsonObject.get("message").toString());
                return Resp.fail(jsonObject.get("message").toString());
            }
            mobile = JSON.parseObject(jsonObject.get("data").toString()).get("mobile").toString();
            temp.setMobile(mobile);
            temp.setToken(token);
            if (!temp.save()) {
                log.error("临时token保存失败");
                return Resp.fail("token保存失败");
            }
        } else {
            mobile = loginTmp.getMobile();
        }
        User user = userService.getByPhone(mobile, (Byte) null);
        if (user != null) {
            HttpResponse login = new LoginController().login(mobile, user.getPhonepwd(), null, request);
            return login;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("mobile", mobile);
        map.put("tempId", temp.getId());
        Resp resp = Resp.ok(map);
        return resp;
    }

    @RequestPath("/register")
    public Object register(HttpRequest request, Integer tokenId, String token, String name, String agreement) throws Exception {
        if (StrUtil.isBlank(name)) {
            return Resp.fail("未填写名称");
        }
        if (StrUtil.isBlank(token)) {
            return Resp.fail("token为空");
        }
        YxOnekeyLoginTmp loginTmp = oneKeyLoginTemp.findById(tokenId);
        if (loginTmp == null || !loginTmp.getToken().equals(token)) {
            return Resp.fail("token校验不通过");
        }
        String mobile = loginTmp.getMobile();
        User user = new User();
        user.setNick(name);
        user.setLoginname(mobile);
        user.setPhone(mobile);
        user.setPwd(getPwd5());
        user.setPhonepwd(getPwd5());
        user.setAgreement(agreement);
        Resp register = new RegisterController().register(user, (Byte) (byte) 2, request);
        if (register.isOk()) {
            HttpResponse login = new LoginController().login(mobile, user.getPhonepwd(), null, request);
            return login;
        }
        return register;
    }

    @RequestPath("/codeRegister")
    public Object register(HttpRequest request, String phone, String code, String nick, String agreement) throws Exception {
        if (StrUtil.isBlank(phone)) {
            return Resp.fail("未填写phone");
        }
        if (StrUtil.isBlank(code)) {
            return Resp.fail("code为空");
        }
        System.out.println(String.format("====>phone=%s, code=%s", phone, code));
        Ret ret = SmsService.me.checkCode(phone, (byte) 2, code, (Map) null, false);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        User user = new User();
        user.setNick(nick);
        user.setLoginname(phone);
        user.setPhone(phone);
        user.setPwd(getPwd5());
        user.setPhonepwd(getPwd5());
        user.setCode(code);
        user.setAgreement(agreement);
        Resp register = new RegisterController().register(user, (Byte) (byte) 2, request);
        if (register.isOk()) {
            HttpResponse login = new LoginController().login(phone, user.getPhonepwd(), null, request);
            return login;
        }
        return register;
    }

    private String getPwd5() throws NoSuchAlgorithmException {
        UUID uuid = UUID.randomUUID();
        String md5 = MD5Utils.getMd5(uuid.toString());
        return md5;
    }

    private Response postString(String path, String appKey, String body, String verifyId) throws Exception {
        HashMap<String, String> headers = new HashMap<>();
        headers.put(HttpHeader.HTTP_HEADER_ACCEPT, "application/json");
        headers.put(HttpHeader.HTTP_HEADER_CONTENT_MD5, MessageDigestUtil.base64AndMD5(body));
        headers.put(HttpHeader.HTTP_HEADER_CONTENT_TYPE, ContentType.CONTENT_TYPE_TEXT);
        headers.put("X-Ca-Version", "1");
        headers.put("X-Ca-Stage", "RELEASE");
        headers.put(SystemHeader.X_CA_KEY, "203929888");
        headers.put(SystemHeader.X_CA_NONCE, UUID.randomUUID().toString());
        CUSTOM_HEADERS_TO_SIGN_PREFIX.clear();
        CUSTOM_HEADERS_TO_SIGN_PREFIX.add("X-Ca-Version");
        CUSTOM_HEADERS_TO_SIGN_PREFIX.add("X-Ca-Stage");
        CUSTOM_HEADERS_TO_SIGN_PREFIX.add(SystemHeader.X_CA_NONCE);
        CUSTOM_HEADERS_TO_SIGN_PREFIX.add(SystemHeader.X_CA_KEY);
        Request request = new Request(Method.POST_STRING, "http://verify5.market.alicloudapi.com", path, "203929888", "roJiyYhznouLsJkS6kaeTgtnmH9XVF10", Constants.DEFAULT_TIMEOUT);
        request.setHeaders(headers);
        request.setSignHeaderPrefixList(CUSTOM_HEADERS_TO_SIGN_PREFIX);
        Map<String, String> querys = new HashMap<>();
        querys.put("appkey", appKey);
        querys.put("verifyId", verifyId);
        request.setQuerys(querys);
        request.setStringBody(body);
        Response response = Client.execute(request);
        return response;
    }
}
