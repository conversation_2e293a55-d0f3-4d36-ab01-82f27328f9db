package org.tio.sitexxx.web.server.controller.base;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Kv;
import org.tio.jfinal.kit.Ret;
import org.tio.jfinal.plugin.activerecord.Db;
import org.tio.sitexxx.im.server.handler.wx.WxChatApi;
import org.tio.sitexxx.im.server.handler.wx.WxSynApi;
import org.tio.sitexxx.service.cache.CacheConfig;
import org.tio.sitexxx.service.cache.Caches;
import org.tio.sitexxx.service.model.main.IpInfo;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.UserInviteAttr;
import org.tio.sitexxx.service.model.main.WxChatItems;
import org.tio.sitexxx.service.model.main.WxGroup;
import org.tio.sitexxx.service.service.atom.RegisterAtom;
import org.tio.sitexxx.service.service.base.IpInfoService;
import org.tio.sitexxx.service.service.base.RegisterService;
import org.tio.sitexxx.service.service.base.TioInviteService;
import org.tio.sitexxx.service.service.base.UserService;
import org.tio.sitexxx.service.service.base.sms.SmsService;
import org.tio.sitexxx.service.service.chat.ChatService;
import org.tio.sitexxx.service.service.chat.FriendService;
import org.tio.sitexxx.service.service.chat.GroupService;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.service.utils.RetUtils;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.sitexxx.servicecommon.vo.MulLauUtils;
import org.tio.sitexxx.web.server.controller.base.sms.SmsController;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.sitexxx.web.server.utils.WxGroupAvatarUtil;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.Constants;
import org.tio.utils.cache.ICache;
import org.tio.utils.resp.Resp;

@RequestPath("/register")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/RegisterController.class */
public class RegisterController {
    private static Logger log = LoggerFactory.getLogger(RegisterController.class);
    private static final RegisterService registerService = RegisterService.me;
    private static final TioInviteService inviteService = TioInviteService.me;
    private static int lastFriendIndex = 0;

    @RequestPath("/accountRegister")
    public Resp accountRegister(User user, HttpRequest request) throws Exception {
        String loginname = user.getLoginname();
        if (loginname.length() < 5) {
            return Resp.fail("账号最少5位");
        }
        if (!Pattern.matches("^[a-z0-9A-Z]+$", loginname)) {
            return Resp.fail("不可包含特殊字符");
        }
        if (NumberUtil.isNumber(loginname)) {
            return Resp.fail(MulLauUtils.getMsg("账号不可以为纯数字"));
        }
        User tmpUser = UserService.ME.getByLoginname(loginname, (Byte) null);
        if (tmpUser != null && tmpUser.getId() != null) {
            return Resp.fail("账号已存在");
        }
        if (user.getId() != null && UserService.ME.getById(user.getId()) != null) {
            return Resp.fail("ID已存在");
        }
        completeUser(user, request);
        user.setReghref(request.getReferer());
        if (StrUtil.isBlank(user.getAvatar())) {
            String path = WxGroupAvatarUtil.pressUserAvatar(user.getNick());
            if (StrUtil.isNotBlank(path)) {
                user.setAvatar(path);
                user.setAvatarbig(path);
            }
        }
        Resp resp = registerService.accountRegister(user);
        if (resp.isOk()) {
            Kv kv = (Kv) resp.getData();
            User reguser = (User) kv.get("user");
            p2pAfterRegister(reguser, request);
        }
        return resp;
    }

    @RequestPath(value = "/{regType}", forward = "/register/submit")
    public Resp register(User user, Byte regType, HttpRequest request) throws Exception {
        System.out.println("regType注册请求：" + JSON.toJSONString(user));
        if (user == null) {
            return Resp.fail("无效参数");
        }
        if (regType.byteValue() == 1) {
            return accountRegister(user, request);
        }
        System.out.println("注册请求：" + JSON.toJSONString(user));
        String inviteEnable = ConfService.getString("inviteEnable", "2");
        if ("1".equals(inviteEnable) && StrUtil.isEmpty(user.getInviteCode())) {
            return Resp.fail("邀请码必填");
        }
        if (ConfService.getInt("validateType", 1).intValue() == 2 && StrUtil.isBlank(user.getEmail())) {
            return Resp.fail("邮箱不能为空");
        }
        if (regType.byteValue() == 1 || regType.byteValue() == 2) {
            completeUser(user, request);
            if (regType.byteValue() == 1) {
                return emailRegister(user, request, "1".equals(inviteEnable));
            }
            return phoneRegister(user, request, "1".equals(inviteEnable));
        }
        return Resp.fail("无效参数");
    }

    @RequestPath("/activate")
    public Resp activate(String authCode, HttpRequest request) throws Exception {
        Resp resp = registerService.activate(authCode, "1".equals(ConfService.getString("inviteEnable", "2")));
        if (resp.isOk()) {
            Kv kv = (Kv) resp.getData();
            User user = (User) kv.get("user");
            p2pAfterRegister(user, request);
        }
        return resp;
    }

    @RequestPath("/emailRegister")
    public Resp emailRegister(User user, HttpRequest request, boolean isOpenInviteCode) throws Exception {
        RequestExt requestExt = WebUtils.getRequestExt(request);
        user.setReghref(request.getReferer());
        if (StrUtil.isBlank(user.getAvatar())) {
            String path = WxGroupAvatarUtil.pressUserAvatar(user.getNick());
            if (StrUtil.isNotBlank(path)) {
                user.setAvatar(path);
                user.setAvatarbig(path);
            }
        }
        Resp resp = registerService.emailRegister(user, request.getClientIp(), request.getHttpSession().getId(), requestExt);
        if (resp.isOk() && isOpenInviteCode && !StrUtil.isEmpty(user.getInviteCode())) {
            User byId = User.dao.findFirst("select * from user where invitecode = ?", new Object[]{user.getInviteCode()});
            if (byId == null) {
                return Resp.fail("推荐码不正确");
            }
            user.setInviteUid(byId.getId());
            Ret ret = inviteService.add(user.getInviteUid(), user.getInvitecode(), user.getId());
            if (ret.isFail()) {
                return Resp.fail(RetUtils.getRetMsg(ret));
            }
        }
        return resp;
    }

    @RequestPath("/phoneRegister")
    public Resp phoneRegister(User user, HttpRequest request, boolean isOpenInviteCode) throws Exception {
        if (ConfService.getInt("validateType", 0).intValue() != 0) {
            boolean isCodePass = "ABCDEF".equals(user.getCode());
            if (StrUtil.isBlank(user.getCode()) && !isCodePass) {
                log.error("用户验证码code不存在");
                return Resp.fail("无效参数");
            }
            if (ConfService.getInt("validateType", 1).intValue() == 2) {
                if (!isCodePass) {
                    ICache cache = Caches.getCache(CacheConfig.EMAIL_AUTHCODE);
                    String email = (String) cache.get(user.getCode(), String.class);
                    if (StrUtil.isBlank(email)) {
                        return Resp.fail("验证码不存在");
                    }
                    if (!email.equals(user.getEmail())) {
                        return Resp.fail("验证码不存在，请重试");
                    }
                }
                User user1 = UserService.ME.getByEmail(user.getEmail(), (byte) 1);
                if (user1 != null) {
                    return Resp.fail("邮箱已注册");
                }
            } else {
                Resp beforeCheck = SmsController.bizPhoneCheck((byte) 2, user.getLoginname(), request);
                if (!beforeCheck.isOk()) {
                    return beforeCheck;
                }
                if (!isCodePass) {
                    Ret ret = SmsService.me.checkCode(user.getLoginname(), (byte) 2, user.getCode(), (Map) null, false);
                    if (ret.isFail()) {
                        return Resp.fail(RetUtils.getRetMsg(ret));
                    }
                }
            }
        } else {
            String loginname = user.getLoginname();
            if (NumberUtil.isNumber(loginname)) {
                return Resp.fail(MulLauUtils.getMsg("账号不可以为纯数字"));
            }
            User tmpUser = UserService.ME.getByLoginname(loginname, (Byte) null);
            if (tmpUser != null && tmpUser.getId() != null) {
                return Resp.fail("账号已存在");
            }
        }
        user.setReghref(request.getReferer());
        if (StrUtil.isBlank(user.getAvatar())) {
            String path = WxGroupAvatarUtil.pressUserAvatar(user.getNick());
            if (StrUtil.isNotBlank(path)) {
                user.setAvatar(path);
                user.setAvatarbig(path);
            }
        }
        Resp resp = registerService.phoneRegister(user, isOpenInviteCode);
        if (resp.isOk()) {
            SmsService.me.delCode(user.getLoginname(), (byte) 2);
            Kv kv = (Kv) resp.getData();
            User reguser = (User) kv.get("user");
            p2pAfterRegister(reguser, request);
        }
        return resp;
    }

    @RequestPath("/xx")
    /* renamed from: xx */
    public Resp m1xx(User user, HttpRequest request) throws Exception {
        User currUser = WebUtils.currUser(request);
        if (currUser == null || !UserService.isSuper(currUser)) {
            return Resp.fail("你没资格操作");
        }
        completeUser(user, request);
        Integer uid = Db.use("tio_site_main").queryInt("select min(id) from user");
        user.setId(Integer.valueOf(uid.intValue() - 1));
        Resp resp = registerService.innerEmailRegister(user, true, "1".equals(ConfService.getString("inviteEnable", "2")));
        if (resp.isOk()) {
            p2pAfterRegister(user, request);
        }
        return resp;
    }

    @RequestPath("/bxx")
    public Resp bxx(String pwd, String nicks, HttpRequest request) throws Exception {
        Long loginnameStartIndex;
        User user1 = new User().dao().findFirst("select * from user order by id limit 0,1");
        Integer uid = user1.getId();
        String loginname = user1.getLoginname();
        String xx = StrUtil.split(loginname, "@")[0];
        try {
            loginnameStartIndex = Long.valueOf(Long.parseLong(xx));
        } catch (Exception e) {
            log.error("", e);
            loginnameStartIndex = 0L;
        }
        Long loginnameStartIndex2 = Long.valueOf(loginnameStartIndex.longValue() + 1);
        Resp resp = null;
        String[] nickarray = nicks.split(Constants.SPE1);
        for (String nick : nickarray) {
            StringBuilder sb = new StringBuilder();
            Long l = loginnameStartIndex2;
            loginnameStartIndex2 = Long.valueOf(loginnameStartIndex2.longValue() + 1);
            String loginname1 = sb.append(l).append("@qq.com").toString();
            User user = new User();
            user.setNick(nick);
            user.setPwd(UserService.getMd5Pwd(loginname1, pwd));
            user.setLoginname(loginname1);
            user.setId(uid);
            String path = WxGroupAvatarUtil.pressUserAvatar(user.getNick());
            if (StrUtil.isNotBlank(path)) {
                user.setAvatar(path);
                user.setAvatarbig(path);
            }
            completeUser(user, request);
            resp = registerService.innerEmailRegister(user, true, "1".equals(ConfService.getString("inviteEnable", "2")));
            if (resp.isOk()) {
                p2pAfterRegister(user, request);
            }
        }
        return resp;
    }

    @RequestPath("/retrievePwd")
    public Resp retrievePwd(String loginname, HttpRequest request) throws Exception {
        RequestExt requestExt = WebUtils.getRequestExt(request);
        return registerService.retrievePwd(loginname, request.getClientIp(), request.getHttpSession().getId(), requestExt);
    }

    @RequestPath("/setNewPwd")
    public Resp setNewPwd(String authCode, String newpwd, HttpRequest request) throws Exception {
        return registerService.setNewPwd(authCode, newpwd);
    }

    private void completeUser(User user, HttpRequest request) {
        IpInfo ipInfo = IpInfoService.ME.save(request.getClientIp());
        user.setIpInfo(ipInfo);
        if (user.getId() == null) {
            int uid = generateUid();
            user.setId(Integer.valueOf(uid));
            if (!Const.MILSERVER) {
                user.setInvitecode(String.valueOf(uid));
            }
        }
        RequestExt requestExt = WebUtils.getRequestExt(request);
        byte deviceType = requestExt.getDeviceType();
        user.setRegistertype(Byte.valueOf(deviceType));
    }

    /* JADX WARN: Incorrect condition in loop: B:4:0x0016 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private int generateUid() {
        /*
            r3 = this;
            int r0 = org.tio.sitexxx.servicecommon.vo.Const.UID_LEN
            int r0 = org.tio.sitexxx.web.server.utils.UidUtils.getRandomUid(r0)
            r4 = r0
            org.tio.sitexxx.service.model.main.User r0 = org.tio.sitexxx.service.model.main.User.dao
            r1 = r4
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)
            org.tio.jfinal.plugin.activerecord.Model r0 = r0.findById(r1)
            org.tio.sitexxx.service.model.main.User r0 = (org.tio.sitexxx.service.model.main.User) r0
            r5 = r0
        L15:
            r0 = r5
            if (r0 == 0) goto L31
            int r0 = org.tio.sitexxx.servicecommon.vo.Const.UID_LEN
            int r0 = org.tio.sitexxx.web.server.utils.UidUtils.getRandomUid(r0)
            r4 = r0
            org.tio.sitexxx.service.model.main.User r0 = org.tio.sitexxx.service.model.main.User.dao
            r1 = r4
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)
            org.tio.jfinal.plugin.activerecord.Model r0 = r0.findById(r1)
            org.tio.sitexxx.service.model.main.User r0 = (org.tio.sitexxx.service.model.main.User) r0
            r5 = r0
            goto L15
        L31:
            r0 = r4
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: org.tio.sitexxx.web.server.controller.base.RegisterController.generateUid():int");
    }

    public static boolean register(HttpRequest request, User user, RegisterAtom registerUserAtom) {
        boolean result = Db.tx(registerUserAtom);
        if (result) {
            p2pAfterRegister(user, request);
        }
        return result;
    }

    public static void p2pAfterRegister(User user, HttpRequest request) {
        WxGroup byGroupid;
        WxChatItems chatItem;
        if (user.getInviteUid() != null) {
            if (Const.MILSERVER) {
                System.out.printf("推送用户为：%s,邀请码为：%s%n", user.getInviteUid(), user.getInviteCode());
                UserInviteAttr userInviteAttr = UserInviteAttr.dao.findFirst("select * from user_invite_attr where uid = ? and status = 1", new Object[]{user.getInviteUid()});
                if (userInviteAttr != null) {
                    try {
                        if (userInviteAttr.getFuid() != null) {
                            System.out.println("要添加的邀请码配置用户ID集合为：" + userInviteAttr.getFuid());
                            String[] split = userInviteAttr.getFuid().trim().split(Constants.SPE1);
                            if (split.length > 0) {
                                String fuidStr = split[RandomUtil.getRandom().nextInt(0, split.length)].trim();
                                User toUser = UserService.ME.getById(fuidStr);
                                if (toUser == null) {
                                    System.out.println("要添加的邀请码配置用户不存在：" + fuidStr);
                                } else {
                                    FriendService.me.addFriend(user, Integer.valueOf(Integer.parseInt(fuidStr)));
                                    if (userInviteAttr.getMessage() != null) {
                                        WxChatItems chatItems = (WxChatItems) RetUtils.getOkTData(ChatService.me.actFdChatItems(Integer.valueOf(Integer.parseInt(fuidStr)), user.getId()), "chat");
                                        if (WxSynApi.isSynVersion()) {
                                            WxSynApi.synChatSession(Integer.valueOf(Integer.parseInt(fuidStr)), chatItems, (byte) 1);
                                        } else {
                                            WxChatApi.userActOper(request, Integer.valueOf(Integer.parseInt(fuidStr)), chatItems);
                                        }
                                        WxChatApi.sendFdMsgEach(request, userInviteAttr.getMessage(), (byte) 1, Integer.valueOf(Integer.parseInt(fuidStr)), user.getId(), (byte) 2);
                                    }
                                }
                            }
                        }
                        if (userInviteAttr.getGid() != null && userInviteAttr.getGid().intValue() != 0) {
                            System.out.println("邀请码配置加入群聊：" + userInviteAttr.getGid());
                            WxGroup byGroupid2 = GroupService.me.getByGroupid(Long.valueOf(userInviteAttr.getGid().longValue()));
                            if (byGroupid2 != null) {
                                Integer uid = byGroupid2.getUid();
                                User byId = UserService.ME.getById(uid);
                                Ret ret = GroupService.me.joinGroup(byId, Long.valueOf(userInviteAttr.getGid().longValue()), String.valueOf(user.getId()), (Integer) null);
                                Short joinnum = (Short) RetUtils.getOkTData(ret, "joinnum");
                                if (joinnum != null && joinnum.shortValue() != 0) {
                                    WxChatApi.joinGroup(request, byId, Long.valueOf(userInviteAttr.getGid().longValue()), String.valueOf(user.getId()), (String) RetUtils.getOkTData(ret), (List) RetUtils.getOkTData(ret, "rebind"), false);
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } else {
                Ret chatRet = ChatService.me.actFdChatItems(user.getInviteUid(), user.getId());
                System.out.println("默认加邀请用户为好友：" + user.getInviteUid());
                if (chatRet.isOk() && null != (chatItem = (WxChatItems) RetUtils.getOkTData(chatRet, "chat"))) {
                    if (WxSynApi.isSynVersion()) {
                        WxSynApi.synChatSession(user.getInviteUid(), chatItem, (byte) 1);
                    } else {
                        WxChatApi.userActOper(request, user.getInviteUid(), chatItem);
                    }
                    try {
                        WxChatApi.sendFdMsgEach(request, ConfService.getString("inviteMsg", "您好！"), (byte) 1, user.getInviteUid(), user.getId(), (byte) 2);
                    } catch (Exception e2) {
                        e2.printStackTrace();
                    }
                }
            }
        }
        String str = ConfService.getString("defaultFriendUid", (String) null);
        if (str != null) {
            String spitStr = str.contains("/") ? "/" : Constants.SPE1;
            String[] split2 = str.trim().split(spitStr);
            try {
                if (lastFriendIndex > split2.length - 1) {
                    lastFriendIndex = 0;
                }
                String rdnUid = split2[lastFriendIndex];
                lastFriendIndex++;
                if (FriendService.me.addFriend(user, Integer.valueOf(Integer.parseInt(rdnUid))).isOk()) {
                    Ret ret2 = ChatService.me.actFdChatItems(Integer.valueOf(Integer.parseInt(rdnUid)), user.getId());
                    if (ret2.isOk()) {
                        WxChatItems chatItems2 = (WxChatItems) RetUtils.getOkTData(ret2, "chat");
                        if (WxSynApi.isSynVersion()) {
                            WxSynApi.synChatSession(Integer.valueOf(Integer.parseInt(rdnUid)), chatItems2, (byte) 1);
                        } else {
                            WxChatApi.userActOper(request, Integer.valueOf(Integer.parseInt(rdnUid)), chatItems2);
                        }
                        WxChatApi.sendFdMsgEach(request, ConfService.getString("hello.to.meet", "Hi，我是yx-io社区站长，终于等到您，期待您有一个愉快的旅程！"), (byte) 1, Integer.valueOf(Integer.parseInt(rdnUid)), user.getId(), (byte) 2);
                    }
                }
            } catch (Exception e3) {
                log.error("defaultFriendUid 失败：", e3);
            }
        }
        String defaultGroupId = ConfService.getString("defaultGroupId", "0");
        if (defaultGroupId != null && defaultGroupId != "") {
            String[] split3 = defaultGroupId.split(Constants.SPE1);
            for (String groupIdStr : split3) {
                if (!groupIdStr.equals("0") && NumberUtil.isNumber(groupIdStr) && (byGroupid = GroupService.me.getByGroupid(Long.valueOf(Long.parseLong(groupIdStr)))) != null) {
                    Integer uid2 = byGroupid.getUid();
                    User byId2 = UserService.ME.getById(uid2);
                    Byte.valueOf(WebUtils.getRequestExt(request).getDeviceType());
                    try {
                        Ret ret3 = GroupService.me.joinGroup(byId2, Long.valueOf(Long.parseLong(groupIdStr)), String.valueOf(user.getId()), (Integer) null);
                        Short joinnum2 = (Short) RetUtils.getOkTData(ret3, "joinnum");
                        if (joinnum2 != null && joinnum2.shortValue() != 0) {
                            WxChatApi.joinGroup(request, byId2, Long.valueOf(Long.parseLong(groupIdStr)), String.valueOf(user.getId()), (String) RetUtils.getOkTData(ret3), (List) RetUtils.getOkTData(ret3, "rebind"), false);
                        }
                    } catch (Exception e4) {
                        e4.printStackTrace();
                    }
                }
            }
        }
    }

    public static void main(String[] args) {
        String[] split = "11111,222".trim().split(Constants.SPE1);
        String rdnUid = split[RandomUtil.randomInt(0, split.length)];
        System.out.println("rdnUid==>" + rdnUid);
    }
}
