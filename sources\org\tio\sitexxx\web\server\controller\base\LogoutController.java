package org.tio.sitexxx.web.server.controller.base;

import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpConfig;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.session.HttpSession;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.plugin.activerecord.Db;
import org.tio.sitexxx.im.server.handler.wx.WxChatQueueApi;
import org.tio.sitexxx.service.model.main.LoginLog;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.UserToken;
import org.tio.sitexxx.service.service.base.UserService;
import org.tio.sitexxx.service.service.base.UserTokenService;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.service.vo.SessionExt;
import org.tio.sitexxx.servicecommon.vo.Devicetype;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/logout")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/LogoutController.class */
public class LogoutController {
    private static Logger log = LoggerFactory.getLogger(LogoutController.class);
    private UserService userService = UserService.ME;

    public static void main(String[] args) {
    }

    @RequestPath("")
    public Resp logout(HttpRequest request) throws Exception {
        SessionExt sessionExtInOtherSession;
        HttpConfig httpConfig = request.getHttpConfig();
        HttpSession httpSession = request.getHttpSession();
        String sessionId = httpSession.getId();
        User user = WebUtils.currUser(request);
        if (user != null) {
            SessionExt sessionExt = (SessionExt) httpSession.getAttribute("SESSION_EXT", SessionExt.class);
            sessionExt.setUid((Integer) null);
            sessionExt.setLoginTime((Long) null);
            sessionExt.setKickedInfo((LoginLog) null);
            httpSession.update(httpConfig);
            RequestExt requestExt = WebUtils.getRequestExt(request);
            WxChatQueueApi.leaveFocusQueue(user, Byte.valueOf(requestExt.getAppDevice()), "");
            if (Objects.equals(ConfService.getByte("Jpush.open.flag", (byte) 1), (byte) 1) && Objects.equals(Byte.valueOf(requestExt.getAppDevice()), Devicetype.APP.getValue())) {
                Db.use("tio_site_main").update("delete from wx_jpush_user WHERE uid = ?", new Object[]{user.getId()});
            }
            int c = UserTokenService.me.delete(requestExt.getAppDevice(), user.getId().intValue(), sessionId);
            if (c <= 0) {
                log.warn("can find usertoken by devicetype【{}】 and uid【{}】 and token:【{}】", new Object[]{Byte.valueOf(requestExt.getAppDevice()), user.getId(), sessionId});
                UserToken userToken = UserTokenService.me.find(requestExt.getAppDevice(), user.getId().intValue());
                if (userToken != null) {
                    String tokenInDb = userToken.getToken();
                    HttpSession otherHttpSession = httpConfig.getHttpSession(tokenInDb);
                    if (otherHttpSession != null) {
                        Integer useridInOtherSession = WebUtils.getUserIdBySession(otherHttpSession);
                        if (useridInOtherSession != null && (sessionExtInOtherSession = (SessionExt) otherHttpSession.getAttribute("SESSION_EXT", SessionExt.class)) != null) {
                            sessionExtInOtherSession.setUid((Integer) null);
                            otherHttpSession.update(httpConfig);
                        }
                    }
                }
            }
            Resp resp = Resp.ok();
            return resp;
        }
        Resp resp2 = Resp.fail("你并未登录");
        return resp2;
    }
}
