package org.tio.sitexxx.web.server.recharge.provider.weixin;

import cn.hutool.core.io.FileUtil;
import com.github.wxpay.sdk.WXPayConfig;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.utils.jfinal.P;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/provider/weixin/WXPayConfigImpl.class */
public class WXPayConfigImpl implements WXPayConfig {
    private static Logger log = LoggerFactory.getLogger(WXPayConfigImpl.class);
    private byte[] certData;
    private static WXPayConfigImpl INSTANCE;

    private WXPayConfigImpl() throws IOException {
        try {
            String certPath = FileUtil.getAbsolutePath("classpath:config/weixin/pay/apiclient_cert.p12");
            File file = new File(certPath);
            InputStream certStream = new FileInputStream(file);
            this.certData = new byte[(int) file.length()];
            certStream.read(this.certData);
            certStream.close();
        } catch (Exception e) {
            log.error("", e);
            throw new RuntimeException(e);
        }
    }

    public static WXPayConfigImpl getInstance() {
        if (INSTANCE == null) {
            synchronized (WXPayConfigImpl.class) {
                if (INSTANCE == null) {
                    INSTANCE = new WXPayConfigImpl();
                }
            }
        }
        return INSTANCE;
    }

    public String getAppID() {
        return P.get("recharge.wx.appid");
    }

    public String getSecret() {
        return P.get("recharge.wx.secret");
    }

    public String getMchID() {
        return P.get("recharge.wx.mch_id");
    }

    public String getKey() {
        return P.get("recharge.wx.key");
    }

    public InputStream getCertStream() {
        ByteArrayInputStream certBis = new ByteArrayInputStream(this.certData);
        return certBis;
    }

    public int getHttpConnectTimeoutMs() {
        return 5000;
    }

    public int getHttpReadTimeoutMs() {
        return 10000;
    }

    public String getPrimaryDomain() {
        return "api.mch.weixin.qq.com";
    }

    public String getAlternateDomain() {
        return "api2.mch.weixin.qq.com";
    }
}
