package org.tio.sitexxx.web.server.controller.base;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/AccessTokenTest.class */
public class AccessTokenTest {
    private static Logger log = LoggerFactory.getLogger(AccessTokenTest.class);

    public static String encrypt(String sSrc, String sKey, String ivStr) throws Exception {
        if (sKey == null) {
            throw new Exception("Key为空");
        }
        if (sKey.length() != 16) {
            throw new Exception("Key长度不是16位");
        }
        byte[] raw = sKey.getBytes();
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        IvParameterSpec iv = new IvParameterSpec(ivStr.getBytes());
        cipher.init(1, skeySpec, iv);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes());
        return new BASE64Encoder().encode(encrypted);
    }

    public static String decrypt(String sSrc, String sKey, String ivStr) throws Exception {
        if (sKey == null) {
            throw new Exception("Key为空");
        }
        if (sKey.length() != 16) {
            throw new Exception("Key长度不是16位");
        }
        byte[] raw = sKey.getBytes("ASCII");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        IvParameterSpec iv = new IvParameterSpec(ivStr.getBytes());
        cipher.init(2, skeySpec, iv);
        byte[] encrypted1 = new BASE64Decoder().decodeBuffer(sSrc);
        byte[] original = cipher.doFinal(encrypted1);
        String originalString = new String(original);
        return originalString;
    }

    public static void main(String[] args) throws Exception {
        log.error("xxyy");
        long lStart = System.currentTimeMillis();
        String enString = encrypt("xxyy", "PlkDnjSmySuiEjhY", "PlkDnjSmySuiEjhY");
        log.error("加密后的字串是：" + enString);
        long lUseTime = System.currentTimeMillis() - lStart;
        log.info("加密耗时：" + lUseTime + "毫秒");
        long lStart2 = System.currentTimeMillis();
        String DeString = decrypt(enString, "PlkDnjSmySuiEjhY", "PlkDnjSmySuiEjhY");
        log.error("解密后的字串是：" + DeString);
        long lUseTime2 = System.currentTimeMillis() - lStart2;
        log.info("解密耗时：" + lUseTime2 + "毫秒");
    }
}
