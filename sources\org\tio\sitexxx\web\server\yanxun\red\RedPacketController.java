package org.tio.sitexxx.web.server.yanxun.red;

import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.jfinal.plugin.activerecord.Record;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.WxChatGroupItem;
import org.tio.sitexxx.service.model.main.WxChatUserItem;
import org.tio.sitexxx.service.model.main.WxGroup;
import org.tio.sitexxx.service.model.main.WxGroupUser;
import org.tio.sitexxx.service.model.main.YxUserWallet;
import org.tio.sitexxx.service.pay.base.BasePayResp;
import org.tio.sitexxx.service.pay.service.RedPacketService;
import org.tio.sitexxx.service.service.chat.ChatIndexService;
import org.tio.sitexxx.service.service.chat.ChatService;
import org.tio.sitexxx.service.service.chat.GroupService;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.service.service.yanxun.wallet.WalletService;
import org.tio.sitexxx.service.utils.RetUtils;
import org.tio.sitexxx.service.vo.GrabRedpacketVo;
import org.tio.sitexxx.service.vo.SendRedpacketVo;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/redPacket")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/yanxun/red/RedPacketController.class */
public class RedPacketController {
    private static Logger log = LoggerFactory.getLogger(RedPacketController.class);

    @RequestPath("/sendRedpacket")
    public Resp sendRedpacket(HttpRequest request, SendRedpacketVo redpacketVo) throws Exception {
        User curr = WebUtils.currUser(request);
        if (redpacketVo == null || redpacketVo.getChatlinkid() == null) {
            return Resp.fail("钱包参数为空");
        }
        if (redpacketVo.getPasswd() == null) {
            return Resp.fail("支付密码为空");
        }
        int _amount = Integer.parseInt(redpacketVo.getAmount());
        if (_amount <= 0) {
            return Resp.fail("红包金额为负");
        }
        if (redpacketVo.getPacketType().intValue() == 2 && _amount < redpacketVo.getPacketCount().shortValue()) {
            return Resp.fail("拼手气红包金额太小");
        }
        Integer sendRedpacketLimit = Integer.valueOf(ConfService.getInt("wx.wallet.sendredpacket.max.amount", 10).intValue() * redpacketVo.getPacketCount().shortValue());
        if (_amount > sendRedpacketLimit.intValue()) {
            return Resp.fail("红包金额最大为" + (new Double(sendRedpacketLimit.intValue()).doubleValue() / 100.0d) + "元");
        }
        if (curr == null) {
            return Resp.fail("用户登录超时");
        }
        redpacketVo.setUid(curr.getId());
        redpacketVo.setNick(curr.getNick());
        YxUserWallet yxUserWallet = WalletService.ME.queryByUid(curr.getId());
        if (yxUserWallet == null) {
            return Resp.fail("用户未开户");
        }
        redpacketVo.setWalletid(String.valueOf(yxUserWallet.getId()));
        Long chatlinkid = redpacketVo.getChatlinkid();
        Byte chatmode = (byte) 1;
        if (chatlinkid.longValue() <= 0) {
            chatmode = (byte) 2;
            Long groupid = Long.valueOf(-chatlinkid.longValue());
            WxChatGroupItem groupItem = ChatIndexService.chatGroupIndex(curr.getId(), groupid);
            if (!ChatService.groupExistChat(groupItem)) {
                return Resp.fail("不是群成员");
            }
            WxGroupUser groupUser = GroupService.me.getGroupUser(curr.getId(), groupid);
            if (!Objects.equals(groupUser.getForbiddenflag(), (byte) 2)) {
                return Resp.fail("您已被禁言，不可发红包");
            }
            WxGroup group = GroupService.me.getByGroupid(groupid);
            if (Objects.equals(groupUser.getGrouprole(), (byte) 2) && !Objects.equals(group.getForbiddenflag(), (byte) 2)) {
                return Resp.fail("Nhóm đã tắt tính năng thảo luận");
            }
            groupItem.getChatlinkid();
            redpacketVo.setBizid(groupid);
        } else {
            WxChatUserItem userItem = ChatIndexService.chatUserIndex(chatlinkid);
            if (!ChatService.existTwoFriend(userItem)) {
                return Resp.fail("你们不是好友");
            }
            redpacketVo.setBizid(userItem.getBizid());
        }
        redpacketVo.setChatmode(chatmode);
        Resp resp = RedPacketService.me.sendRedpacket(redpacketVo, request);
        if (!resp.isOk()) {
            return Resp.fail(resp.getMsg());
        }
        return Resp.ok(resp.getData());
    }

    @RequestPath("/redStatus")
    public Resp redStatus(HttpRequest request, String serialnumber) throws Exception {
        User curr = WebUtils.currUser(request);
        new BasePayResp();
        Ret ret = RedPacketService.me.redStatus(curr, serialnumber);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/redInfo")
    public Resp redInfo(HttpRequest request, String serialnumber) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = RedPacketService.me.redInfo(request, serialnumber, curr);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/grabRedpacket")
    public Resp grabRedpacket(HttpRequest request, GrabRedpacketVo grabRedpacketVo) throws Exception {
        User curr = WebUtils.currUser(request);
        if (curr == null) {
            return Resp.fail("用户登录超时");
        }
        grabRedpacketVo.setUid(curr.getId());
        if (grabRedpacketVo == null) {
            return Resp.fail("钱包参数为空");
        }
        YxUserWallet yxUserWallet = WalletService.ME.queryByUid(grabRedpacketVo.getUid());
        if (yxUserWallet == null) {
            return Resp.fail("用户未开户");
        }
        grabRedpacketVo.setWalletid(String.valueOf(yxUserWallet.getId()));
        Long chatlinkid = grabRedpacketVo.getChatlinkid();
        Byte chatmode = (byte) 1;
        log.error("抢红包的chatlinkid:{}", chatlinkid);
        if (chatlinkid.longValue() <= 0) {
            chatmode = (byte) 2;
            Long groupid = Long.valueOf(-chatlinkid.longValue());
            WxChatGroupItem groupItem = ChatIndexService.chatGroupIndex(curr.getId(), groupid);
            if (!ChatService.groupExistChat(groupItem)) {
                return Resp.fail("不是群成员");
            }
            groupItem.getChatlinkid();
            grabRedpacketVo.setBizid(groupid);
        } else {
            WxChatUserItem userItem = ChatIndexService.chatUserIndex(chatlinkid);
            if (!ChatService.existTwoFriend(userItem)) {
                return Resp.fail("你们不是互相不是好友");
            }
            grabRedpacketVo.setBizid(userItem.getBizid());
        }
        grabRedpacketVo.setChatmode(chatmode);
        Resp resp = RedPacketService.me.grabRedpacket(grabRedpacketVo, request);
        if (!resp.isOk()) {
            return Resp.fail(resp.getMsg());
        }
        return Resp.ok(resp.getData());
    }

    @RequestPath("/grabRedpacketlist")
    public Resp grabRedpacketlist(HttpRequest request, Integer pageNumber, String period) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = RedPacketService.me.grabRedpacketlist(curr.getId(), pageNumber, period);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/sendRedpacketlist")
    public Resp sendRedpacketlist(HttpRequest request, Integer pageNumber, String period) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = RedPacketService.me.sendRedpacketlist(curr.getId(), pageNumber, period);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/sendredpacketstat")
    public Resp sendRedpacketStat(HttpRequest request, String period) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = RedPacketService.me.sendRedpacketStat(curr.getId(), period);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Record record = (Record) RetUtils.getOkTData(ret);
        if (record != null) {
            record.set("nick", curr.getNick());
            record.set("avatar", curr.getAvatar());
        }
        return Resp.ok(record);
    }

    @RequestPath("/grabredpacketstat")
    public Resp grabRedpacketStat(HttpRequest request, String period) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = RedPacketService.me.grabRedpacketStat(curr.getId(), period);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Record record = (Record) RetUtils.getOkTData(ret);
        if (record != null) {
            record.set("nick", curr.getNick());
            record.set("avatar", curr.getAvatar());
        }
        return Resp.ok(record);
    }
}
