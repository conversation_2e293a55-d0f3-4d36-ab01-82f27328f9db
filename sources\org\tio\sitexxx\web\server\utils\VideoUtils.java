package org.tio.sitexxx.web.server.utils;

import cn.hutool.core.io.FileUtil;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.awt.image.ImageObserver;
import java.io.File;
import javax.imageio.ImageIO;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.sitexxx.service.model.main.Video;
import org.tio.utils.hutool.BetweenFormater;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/utils/VideoUtils.class */
public class VideoUtils {
    private static Logger log = LoggerFactory.getLogger(VideoUtils.class);

    public static void main(String[] args) {
    }

    public static Video processVideo(String videoFilePath, String coverFilePath, int coverWidth, String coverExt) throws Exception {
        BufferedImage newBufferedImage;
        long start = System.currentTimeMillis();
        File coverFile = new File(coverFilePath);
        if (!coverFile.exists()) {
            FileUtil.mkParentDirs(coverFile);
        }
        FFmpegFrameGrabber ff = new FFmpegFrameGrabber(videoFilePath);
        ff.start();
        int frameCount = ff.getLengthInFrames();
        Frame f = null;
        for (int i = 0; i < frameCount; i++) {
            f = ff.grabImage();
            if (i > 5 && f.image != null) {
                break;
            }
        }
        Java2DFrameConverter converter = new Java2DFrameConverter();
        BufferedImage bufferedImage = converter.getBufferedImage(f);
        int initWidth = bufferedImage.getWidth();
        int initHeight = bufferedImage.getHeight();
        int newWidth = Math.min(coverWidth, initWidth);
        if (newWidth != initWidth) {
            int newHeight = (int) ((newWidth / initWidth) * initHeight);
            newBufferedImage = new BufferedImage(newWidth, newHeight, 5);
            Image img = bufferedImage.getScaledInstance(newWidth, newHeight, 1);
            newBufferedImage.getGraphics().drawImage(img, 0, 0, (ImageObserver) null);
            ImageIO.write(newBufferedImage, coverExt, coverFile);
        } else {
            ImageIO.write(bufferedImage, coverExt, coverFile);
            newBufferedImage = bufferedImage;
        }
        long millseconds = ff.getLengthInTime() / 1000;
        long seconds = millseconds / 1000;
        double fps = ff.getFrameRate();
        ff.stop();
        File videoFile = new File(videoFilePath);
        Video video = new Video();
        video.setFps(Double.valueOf(fps));
        video.setFramecount(Integer.valueOf(frameCount));
        video.setSeconds(Integer.valueOf((int) seconds));
        BetweenFormater betweenFormater = new BetweenFormater(millseconds, BetweenFormater.Level.SECOND);
        betweenFormater.format();
        video.setFormatedseconds(betweenFormater.format());
        video.setCoverheight(Integer.valueOf(newBufferedImage.getHeight()));
        video.setCoverwidth(Integer.valueOf(newBufferedImage.getWidth()));
        video.setCoversize(Integer.valueOf((int) FileUtil.size(coverFile)));
        video.setHeight(Integer.valueOf(initHeight));
        video.setWidth(Integer.valueOf(initWidth));
        video.setSize(Long.valueOf(FileUtil.size(videoFile)));
        log.error("生成封面图耗时:{}ms, 视频[{}]，缩略图:[{}]", new Object[]{Long.valueOf(System.currentTimeMillis() - start), videoFilePath, coverFilePath});
        return video;
    }
}
