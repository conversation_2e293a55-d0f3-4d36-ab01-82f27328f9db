package org.tio.sitexxx.web.server.recharge;

import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.sitexxx.service.model.main.RechargeItem;
import org.tio.sitexxx.web.server.recharge.provider.alipay.AlipayAppRechargeProvider;
import org.tio.sitexxx.web.server.recharge.provider.alipay.AlipayH5RechargeProvider;
import org.tio.sitexxx.web.server.recharge.provider.alipay.AlipayRechargeProvider;
import org.tio.sitexxx.web.server.recharge.provider.apple.AppleRechargeProvider;
import org.tio.sitexxx.web.server.recharge.provider.weixin.WxPayAppRechargeProvider;
import org.tio.sitexxx.web.server.recharge.provider.weixin.WxPayH5RechargeProvider;
import org.tio.sitexxx.web.server.recharge.provider.weixin.WxPayJsapiRechargeProvider;
import org.tio.sitexxx.web.server.recharge.provider.weixin.WxPayScan2RechargeProvider;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/RechargeServiceFactory.class */
public class RechargeServiceFactory {
    private static Logger log = LoggerFactory.getLogger(RechargeServiceFactory.class);

    public static IRechargeProvider getThirdRechargeService(Integer paytype) {
        if (Objects.equals(paytype, RechargeItem.Paytype.ALIPAY)) {
            return AlipayRechargeProvider.f26me;
        }
        if (Objects.equals(paytype, RechargeItem.Paytype.ALIPAY_APP)) {
            return AlipayAppRechargeProvider.f24me;
        }
        if (Objects.equals(paytype, RechargeItem.Paytype.ALIPAY_H5)) {
            return AlipayH5RechargeProvider.f25me;
        }
        if (Objects.equals(paytype, RechargeItem.Paytype.WEIXIN)) {
            return WxPayScan2RechargeProvider.f31me;
        }
        if (Objects.equals(paytype, RechargeItem.Paytype.WEIXIN_APP)) {
            return WxPayAppRechargeProvider.f28me;
        }
        if (Objects.equals(paytype, RechargeItem.Paytype.WEIXIN_H5)) {
            return WxPayH5RechargeProvider.f29me;
        }
        if (Objects.equals(paytype, RechargeItem.Paytype.WEIXIN_JSAPI)) {
            return WxPayJsapiRechargeProvider.f30me;
        }
        if (Objects.equals(paytype, RechargeItem.Paytype.APPLE_APP)) {
            return AppleRechargeProvider.f27me;
        }
        log.error("paytype:{}, 找不到相应的处理类", paytype);
        return null;
    }

    public static void main(String[] args) {
    }
}
