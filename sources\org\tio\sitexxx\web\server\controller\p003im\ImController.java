package org.tio.sitexxx.web.server.controller.p003im;

import cn.hutool.core.util.StrUtil;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.clu.client.CluClient;
import org.tio.clu.common.bs.BestNodeResp;
import org.tio.core.Node;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.sitexxx.im.server.TioSiteImServerStarter;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.jfinal.P;
import org.tio.utils.resp.Resp;

@RequestPath("/im")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/im/ImController.class */
public class ImController {
    private static Logger log = LoggerFactory.getLogger(ImController.class);
    public static final String SUBDIR_VIDEO = "im/upload/video";
    public static final String SUBDIR_IMG = "im/upload/img";

    @RequestPath("/imserver")
    public Resp imserver(HttpRequest request) throws Exception {
        Node node;
        RequestExt requestExt = WebUtils.getRequestExt(request);
        String protocol = requestExt.isFromBrowser() ? "ws" : "app";
        Integer currUid = WebUtils.currUserId(request);
        BestNodeResp bestNodeResp = null;
        if (Const.USE_TIO_CLU) {
            try {
                bestNodeResp = CluClient.bestNodeReq(TioSiteImServerStarter.cluClientStarter, currUid, protocol, 1500L);
            } catch (Exception e) {
                log.error("获取最佳服务器时异常", e);
            }
        }
        if (bestNodeResp != null && bestNodeResp.isOk()) {
            node = bestNodeResp.getNode();
        } else {
            node = new Node(WebUtils.getImServerIP(request), WebUtils.getImServerPort(request));
            if (!Const.IM_SSL_FLAG) {
                node.setSsl((byte) 2);
            }
        }
        if (StrUtil.isNotBlank(P.get("tio.clu.select.sever.force"))) {
            node.setIp(P.get("tio.clu.select.sever.force"));
        }
        Resp resp = Resp.ok().data(node);
        return resp;
    }

    @RequestPath("/turnserver")
    public Resp turnserver(HttpRequest request) throws Exception {
        TurnServer turnServer = new TurnServer();
        turnServer.setUrls(ConfService.getString("turnserver.url", "turn:**************:3478"));
        turnServer.setCredential(ConfService.getString("turnserver.credential", "8654269566"));
        turnServer.setUsername(ConfService.getString("turnserver.username", "tan"));
        List<TurnServer> list = new ArrayList<>(1);
        list.add(turnServer);
        return Resp.ok().data(list);
    }
}
