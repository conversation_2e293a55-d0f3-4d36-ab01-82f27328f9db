package org.tio.sitexxx.web.server.recharge;

import org.tio.http.common.HttpResponse;
import org.tio.sitexxx.service.model.main.RechargeItem;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/NotifyResultVo.class */
public class NotifyResultVo {
    private HttpResponse response;
    private Integer tradeStatus;
    private RechargeItem rechargeItem;
    private Boolean isValidCallback;

    /* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/NotifyResultVo$TradeStatus.class */
    public interface TradeStatus {
        public static final Integer SUCCESS = 1;
        public static final Integer FINISHED = 2;
        public static final Integer CLOSED = 3;
    }

    public NotifyResultVo() {
        this.isValidCallback = false;
    }

    public NotifyResultVo(Integer tradeStatus, RechargeItem rechargeItem, HttpResponse response, Boolean isValidCallback) {
        this.isValidCallback = false;
        this.tradeStatus = tradeStatus;
        setRechargeItem(rechargeItem);
        this.response = response;
        this.isValidCallback = isValidCallback;
    }

    public static void main(String[] args) {
    }

    public Integer getTradeStatus() {
        return this.tradeStatus;
    }

    public void setTradeStatus(Integer tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public Boolean getIsValidCallback() {
        return this.isValidCallback;
    }

    public void setIsValidCallback(Boolean isValidCallback) {
        this.isValidCallback = isValidCallback;
    }

    public RechargeItem getRechargeItem() {
        return this.rechargeItem;
    }

    public void setRechargeItem(RechargeItem rechargeItem) {
        this.rechargeItem = rechargeItem;
    }

    public HttpResponse getResponse() {
        return this.response;
    }

    public void setResponse(HttpResponse response) {
        this.response = response;
    }
}
