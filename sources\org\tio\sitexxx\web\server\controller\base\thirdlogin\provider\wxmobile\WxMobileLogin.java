package org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.wxmobile;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.sitexxx.service.model.main.UserThird;
import org.tio.sitexxx.service.model.main.UserThirdWx;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.MobileLogin;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/provider/wxmobile/WxMobileLogin.class */
public class WxMobileLogin extends MobileLogin {
    private static Logger log = LoggerFactory.getLogger(WxMobileLogin.class);

    /* renamed from: me */
    public static WxMobileLogin f16me = new WxMobileLogin();

    private WxMobileLogin() {
    }

    public static void main(String[] args) {
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.MobileLogin
    public UserThird.SubTable createSubTable(HttpRequest request, Integer type) {
        String country = request.getParam("country");
        String province = request.getParam("province");
        String city = request.getParam("city");
        UserThirdWx userThirdWx = new UserThirdWx();
        userThirdWx.setCity(city);
        userThirdWx.setCountry(country);
        userThirdWx.setProvince(province);
        return userThirdWx;
    }
}
