package org.tio.sitexxx.web.server.recharge.provider.weixin;

import java.util.Objects;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/provider/weixin/TradeType.class */
public enum TradeType {
    JSAPI("JSAPI"),
    NATIVE("NATIVE"),
    APP("APP"),
    WAP("WAP"),
    MICROPAY("MICROPAY"),
    MWEB("MWEB"),
    PAP("PAP");

    String value;

    public static TradeType from(String value) {
        TradeType[] values = values();
        for (TradeType v : values) {
            if (Objects.equals(v.value, value)) {
                return v;
            }
        }
        return null;
    }

    TradeType(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
