package org.tio.sitexxx.web.server.controller.p004wx;

import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.service.chat.SynService;
import org.tio.sitexxx.service.utils.RetUtils;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/syn")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/wx/SynController.class */
public class SynController {
    private static Logger log = LoggerFactory.getLogger(SynController.class);
    private static final SynService syn = SynService.me;

    @RequestPath("/chat")
    public Resp chat(HttpRequest request, Date syntime) throws Exception {
        User curr = WebUtils.currUser(request);
        RequestExt requestExt = WebUtils.getRequestExt(request);
        byte deviceType = requestExt.getAppDevice();
        Ret ret = syn.chat(curr, Byte.valueOf(deviceType), syntime);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/ack")
    public Resp ack(HttpRequest request, Integer synid) throws Exception {
        User curr = WebUtils.currUser(request);
        Ret ret = syn.ack(curr, synid);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }
}
