package org.tio.sitexxx.web.server.controller.base.thirdlogin;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.annotation.RequestPath;
import org.tio.http.server.mvc.Routes;
import org.tio.http.server.util.Resps;
import org.tio.sitexxx.service.cache.CacheConfig;
import org.tio.sitexxx.service.cache.Caches;
import org.tio.sitexxx.service.model.main.IpInfo;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.UserThird;
import org.tio.sitexxx.service.service.atom.RegisterAtom;
import org.tio.sitexxx.service.service.base.IpInfoService;
import org.tio.sitexxx.service.service.base.UserService;
import org.tio.sitexxx.service.service.base.UserThirdService;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.service.utils.CommonUtils;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.sitexxx.web.server.controller.base.LoginController;
import org.tio.sitexxx.web.server.controller.base.RegisterController;
import org.tio.sitexxx.web.server.utils.SessionCacheUtils;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.cache.ICache;
import org.tio.utils.resp.Resp;

@RequestPath("/tlogin")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/ThirdLoginController.class */
public class ThirdLoginController {
    private static Logger log = LoggerFactory.getLogger(ThirdLoginController.class);

    public static void main(String[] args) {
    }

    @RequestPath("/{type}")
    public HttpResponse login(Integer type, HttpRequest request) throws Exception {
        IThirdLogin thirdLogin = ThirdLoginFactory.f5me.getThirdLogin(type);
        if (thirdLogin == null) {
            request.close();
            return null;
        }
        String referer = request.getReferer();
        if (StrUtil.isNotBlank(referer)) {
            SessionCacheUtils.put(request, "THIRD_LOGIN_REFER", referer);
        }
        return thirdLogin.toLoginPage(request, type);
    }

    @RequestPath("/cb/p/{type}")
    public HttpResponse callback(Integer type, HttpRequest request) throws Exception {
        IThirdLogin thirdLogin = ThirdLoginFactory.f5me.getThirdLogin(type);
        if (thirdLogin == null) {
            request.close();
            return null;
        }
        UserThird userThird = thirdLogin.callback(request, type);
        if (userThird == null) {
            request.close();
            return null;
        }
        UserThird userThirdInDb = null;
        String openid = userThird.getOpenid();
        String unionid = userThird.getUnionid();
        if (StrUtil.isNotBlank(unionid)) {
            userThirdInDb = UserThirdService.me.getByUnionid(type, unionid);
            if (userThirdInDb == null) {
                String typeSplit = ThirdLoginFactory.getSimilarTypesStr(type);
                userThirdInDb = UserThirdService.me.getByUnionid(typeSplit, unionid);
                if (userThirdInDb != null) {
                    userThird.setUid(userThirdInDb.getUid());
                    boolean thirdFlag = UserThirdService.me.save(userThird);
                    if (!thirdFlag) {
                        if (thirdLogin.isAjax(request, type)) {
                            return Resps.json(request, Resp.fail("三方同步绑定失败"));
                        }
                        return Resps.redirect(request, "/");
                    }
                }
            }
        } else if (StrUtil.isNotBlank(openid)) {
            userThirdInDb = UserThirdService.me.getByOpenid(type, openid);
        }
        String clientip = request.getClientIp();
        IpInfo ipInfo = IpInfoService.ME.save(clientip);
        String pwd = type + "";
        if (userThirdInDb == null) {
            User user = new User();
            if (StrUtil.isNotBlank(unionid)) {
                user.setLoginname(unionid);
            } else {
                user.setLoginname(openid);
            }
            RequestExt requestExt = WebUtils.getRequestExt(request);
            byte deviceType = requestExt.getDeviceType();
            user.setRegistertype(Byte.valueOf(deviceType));
            user.setPwd(pwd);
            String nick = userThird.getNick();
            Resp resp = CommonUtils.checkGroupName(nick, "昵称");
            if (!resp.isOk()) {
                nick = RandomUtil.randomString("abcdefghijklmnopqrstuvwxyz", 6);
            }
            int uid = generateUid();
            user.setId(Integer.valueOf(uid));
            if (!Const.MILSERVER) {
                user.setInvitecode(String.valueOf(uid));
            }
            user.setNick(nick);
            user.setAvatar(userThird.getAvatar());
            user.setAvatarbig(userThird.getAvatar());
            user.setStatus((byte) 1);
            user.setIpInfo(ipInfo);
            user.setThirdbindflag((byte) 1);
            user.setUserThird(userThird);
            user.setThirdstatus((byte) 2);
            user.setThirdtype(type);
            user.setCreatetime(new Date());
            user.setReghref((String) SessionCacheUtils.get(request, "THIRD_LOGIN_REFER"));
            RegisterAtom registerUserAtom = new RegisterAtom(user, "1".equals(ConfService.getString("inviteEnable", "2")));
            registerUserAtom.setThird(true);
            boolean result = RegisterController.register(request, user, registerUserAtom);
            if (result) {
                if (StrUtil.isNotBlank(unionid)) {
                    ICache openidCache = Caches.getCache(CacheConfig.OPENID_USERTHIRD);
                    String key = type + "_" + unionid;
                    openidCache.remove(key);
                    userThirdInDb = UserThirdService.me.getByUnionid(type, unionid);
                } else {
                    ICache openidCache2 = Caches.getCache(CacheConfig.OPENID_USERTHIRD);
                    String key2 = type + "_" + openid;
                    openidCache2.remove(key2);
                    userThirdInDb = UserThirdService.me.getByOpenid(type, openid);
                }
            } else {
                if (thirdLogin.isAjax(request, type)) {
                    return Resps.json(request, Resp.fail(registerUserAtom.getMsg()));
                }
                return Resps.redirect(request, "/");
            }
        }
        User user2 = UserService.ME.getById(userThirdInDb.getUid());
        request.setAttribute("IS_THIRD_LOGIN", true);
        request.setAttribute("THIRD_LOGIN_USER", user2);
        HttpResponse response = null;
        if (!thirdLogin.isAjax(request, type)) {
            String referer = (String) SessionCacheUtils.get(request, "THIRD_LOGIN_REFER");
            if (StrUtil.isNotBlank(referer)) {
                response = Resps.redirect(request, referer);
            } else {
                response = Resps.redirect(request, "/tioim");
            }
        }
        request.setAttribute("THIRD_LOGIN_RESPONSE", response);
        LoginController loginController = (LoginController) Routes.getController(LoginController.class);
        return loginController.login(user2.getLoginname(), null, null, request);
    }

    /* JADX WARN: Incorrect condition in loop: B:4:0x0015 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private int generateUid() {
        /*
            r3 = this;
            r0 = 8
            int r0 = org.tio.sitexxx.web.server.utils.UidUtils.getRandomUid(r0)
            r4 = r0
            org.tio.sitexxx.service.model.main.User r0 = org.tio.sitexxx.service.model.main.User.dao
            r1 = r4
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)
            org.tio.jfinal.plugin.activerecord.Model r0 = r0.findById(r1)
            org.tio.sitexxx.service.model.main.User r0 = (org.tio.sitexxx.service.model.main.User) r0
            r5 = r0
        L14:
            r0 = r5
            if (r0 == 0) goto L2f
            r0 = 8
            int r0 = org.tio.sitexxx.web.server.utils.UidUtils.getRandomUid(r0)
            r4 = r0
            org.tio.sitexxx.service.model.main.User r0 = org.tio.sitexxx.service.model.main.User.dao
            r1 = r4
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)
            org.tio.jfinal.plugin.activerecord.Model r0 = r0.findById(r1)
            org.tio.sitexxx.service.model.main.User r0 = (org.tio.sitexxx.service.model.main.User) r0
            r5 = r0
            goto L14
        L2f:
            r0 = r4
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: org.tio.sitexxx.web.server.controller.base.thirdlogin.ThirdLoginController.generateUid():int");
    }
}
