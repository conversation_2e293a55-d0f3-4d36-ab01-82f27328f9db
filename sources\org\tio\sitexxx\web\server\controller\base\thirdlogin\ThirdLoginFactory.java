package org.tio.sitexxx.web.server.controller.base.thirdlogin;

import cn.hutool.core.collection.CollUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.apple.AppleLogin;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.douyin.DouyinLogin;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.osc.OscLogin;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.p000qq.QQLogin;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.p001wb.WbLogin;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.p002wx.WxLogin;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.p002wx.WxOpenLogin;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.qqmobile.QQMobileLogin;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.wbmobile.WbMobileLogin;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.wxmobile.WxMobileLogin;
import org.tio.sitexxx.web.server.recharge.IRechargeProvider;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.Constants;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/ThirdLoginFactory.class */
public class ThirdLoginFactory {
    private static Logger log = LoggerFactory.getLogger(ThirdLoginFactory.class);

    /* renamed from: me */
    public static final ThirdLoginFactory f5me = new ThirdLoginFactory();
    private static final Map<Integer, List<Integer>> SIMILAR_TYPES = new HashMap();
    private static final Map<Integer, String> SIMILAR_TYPES_STR = new HashMap();

    /* JADX WARN: Multi-variable type inference failed */
    static {
        for (int i = 1; i < 10; i++) {
            ArrayList arrayList = new ArrayList(3);
            arrayList.add(Integer.valueOf(i));
            arrayList.add(Integer.valueOf(i + (i * 10)));
            arrayList.add(Integer.valueOf(i + (i * 10) + (i * 100)));
            arrayList.add(Integer.valueOf(i + (i * 10) + (i * 100) + (i * Constants.DEFAULT_TIMEOUT)));
            for (int j = 0; j < arrayList.size(); j++) {
                SIMILAR_TYPES.put(arrayList.get(j), arrayList);
                SIMILAR_TYPES_STR.put(arrayList.get(j), CollUtil.join(arrayList, Constants.SPE1));
            }
        }
    }

    private ThirdLoginFactory() {
    }

    public static List<Integer> getSimilarTypes(Integer type) {
        return SIMILAR_TYPES.get(type);
    }

    public static String getSimilarTypesStr(Integer type) {
        return SIMILAR_TYPES_STR.get(type);
    }

    public IThirdLogin getThirdLogin(Integer type) {
        switch (type.intValue()) {
            case IRechargeProvider.CallType.RETURN /* 1 */:
                return QQLogin.f10me;
            case IRechargeProvider.CallType.NOTIFY /* 2 */:
                return WxLogin.f14me;
            case 3:
                return WbLogin.f12me;
            case 4:
                return DouyinLogin.f7me;
            case 5:
                return OscLogin.f8me;
            case 11:
                return QQMobileLogin.f11me;
            case 22:
                return WxMobileLogin.f16me;
            case 33:
                return WbMobileLogin.f13me;
            case 222:
                return WxOpenLogin.f15me;
            case 9999:
                return AppleLogin.f6me;
            default:
                log.warn("找不到IThirdLogin的实现类, type:{}", type);
                return null;
        }
    }

    public static void main(String[] args) {
        System.out.println(3);
        System.out.println(1);
        System.out.println(6);
        System.out.println(7);
        System.out.println(getSimilarTypesStr(1));
        System.out.println(getSimilarTypesStr(11));
        System.out.println(getSimilarTypesStr(111));
        System.out.println(getSimilarTypesStr(1111));
        System.out.println(getSimilarTypesStr(2));
        System.out.println(getSimilarTypesStr(22));
        System.out.println(getSimilarTypesStr(222));
        System.out.println(getSimilarTypesStr(2222));
        System.out.println(getSimilarTypesStr(3));
        System.out.println(getSimilarTypesStr(33));
        System.out.println(getSimilarTypesStr(9999));
    }
}
