package org.tio.sitexxx.web.server.recharge.provider.weixin;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.github.wxpay.sdk.WXPay;
import com.github.wxpay.sdk.WXPayConfig;
import com.github.wxpay.sdk.WXPayConstants;
import com.github.wxpay.sdk.WXPayUtil;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import javax.imageio.ImageIO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.mvc.Routes;
import org.tio.http.server.util.Resps;
import org.tio.sitexxx.service.model.main.RechargeItem;
import org.tio.sitexxx.service.service.recharge.RechargeItemService;
import org.tio.sitexxx.servicecommon.utils.LogUtils;
import org.tio.sitexxx.web.server.controller.base.QrCodeController;
import org.tio.sitexxx.web.server.recharge.IRechargeProvider;
import org.tio.sitexxx.web.server.recharge.RechargeUtils;
import org.tio.sitexxx.web.server.utils.SessionCacheUtils;
import org.tio.utils.json.Json;
import org.tio.websocket.common.util.BASE64Util;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/provider/weixin/WxPayScan2RechargeProvider.class */
public class WxPayScan2RechargeProvider implements IRechargeProvider {
    private static Logger log = LogUtils.getCoinLog();

    /* renamed from: me */
    public static final WxPayScan2RechargeProvider f31me = new WxPayScan2RechargeProvider();
    protected WXPayConfig config = getWXPayConfig();
    protected WXPay wxpay = new WXPay(this.config);

    protected WxPayScan2RechargeProvider() {
    }

    public WXPayConfig getWXPayConfig() {
        return WXPayConfigImpl.getInstance();
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public HttpResponse toThirdRechargePage(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        Integer paytype = rechargeItem.getPaytype();
        String _subject = rechargeItem.getGoods();
        if (StrUtil.isBlank(_subject)) {
            _subject = "充值";
        }
        String total_fee = String.valueOf((int) NumberUtil.mul(rechargeItem.getAmount().doubleValue(), 100.0f));
        String tradeno = rechargeItem.getTradeno();
        String ip = request.getClientIp();
        Map<String, String> data = new HashMap<>();
        data.put("body", _subject);
        data.put("out_trade_no", tradeno);
        data.put("device_info", "");
        data.put("fee_type", "CNY");
        data.put("total_fee", total_fee);
        data.put("spbill_create_ip", ip);
        data.put("notify_url", RechargeUtils.getNotifyUrl(paytype, request));
        data.put("trade_type", TradeType.NATIVE.getValue());
        data.put("product_id", tradeno);
        try {
            Map<String, String> r = this.wxpay.unifiedOrder(data);
            String returnCode = r.get("return_code");
            if ("SUCCESS".equals(returnCode)) {
                String result_code = r.get("result_code");
                if ("SUCCESS".equals(result_code)) {
                    String prepay_id = r.get("prepay_id");
                    if (StrUtil.isNotBlank(prepay_id)) {
                        rechargeItem.setThirdtradeno(prepay_id);
                        RechargeItemService.me.update(rechargeItem);
                        String code_url = r.get("code_url");
                        Integer recharge_only_qr = (Integer) request.getAttribute("recharge_only_qr");
                        Integer width = (Integer) request.getAttribute("recharge_only_qr_width");
                        Integer height = (Integer) request.getAttribute("recharge_only_qr_height");
                        if (Objects.equals(recharge_only_qr, 1)) {
                            QrCodeController qrCodeController = (QrCodeController) Routes.getController(QrCodeController.class);
                            return qrCodeController.index(width, height, null, null, code_url, request);
                        }
                        Map<String, String> retData = new HashMap<>();
                        retData.put("code_url", code_url);
                        BufferedImage bufferedImage = QrCodeUtil.generate(code_url, 190, 190);
                        ByteArrayOutputStream out = new ByteArrayOutputStream();
                        ImageIO.write(bufferedImage, "png", out);
                        byte[] bs = out.toByteArray();
                        String ss = BASE64Util.byteArrayToBase64(bs);
                        SessionCacheUtils.put(request, "WX_PAY_QR_BASE64", ss);
                        SessionCacheUtils.put(request, "WX_PAY_AMOUNT", rechargeItem.getAmount());
                        SessionCacheUtils.put(request, "WX_PAY_SUBJECT", _subject);
                        SessionCacheUtils.put(request, "WX_PAY_TRADENO", tradeno);
                        return Resps.redirect(request, "/recharge/wechatPay.html");
                    }
                } else {
                    log.error("微信扫码充值，提交失败:{}", Json.toJson(r));
                }
            } else {
                log.error("微信扫码充值，提交失败:{}", Json.toJson(r));
            }
            return null;
        } catch (Exception e) {
            log.error("", e);
            return null;
        }
    }

    protected static Map<String, String> toMap(HttpRequest request) throws Exception {
        HashMap<String, String> ret = (HashMap) request.getAttribute("wxPay_params");
        if (ret != null) {
            return ret;
        }
        String bodyString = request.getBodyString();
        HashMap<String, String> ret2 = (HashMap) WXPayUtil.xmlToMap(bodyString);
        request.setAttribute("wxPay_params", ret2);
        return ret2;
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public boolean isValidCallback(HttpRequest request, int callbackType) throws Exception {
        boolean verify_result;
        Map<String, String> params = toMap(request);
        String sign_type = params.get("sign_type");
        if (StrUtil.isNotBlank(sign_type)) {
            if ("MD5".equalsIgnoreCase(sign_type)) {
                verify_result = WXPayUtil.isSignatureValid(params, this.config.getKey(), WXPayConstants.SignType.MD5);
            } else if ("HMAC-SHA256".equalsIgnoreCase(sign_type)) {
                verify_result = WXPayUtil.isSignatureValid(params, this.config.getKey(), WXPayConstants.SignType.HMACSHA256);
            } else {
                verify_result = WXPayUtil.isSignatureValid(params, this.config.getKey());
            }
        } else {
            verify_result = WXPayUtil.isSignatureValid(params, this.config.getKey());
        }
        return verify_result;
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public String getTradeno(HttpRequest request, int callbackType) {
        try {
            Map<String, String> params = toMap(request);
            String out_trade_no = params.get("out_trade_no");
            return out_trade_no;
        } catch (Exception e) {
            log.error("", e);
            return null;
        }
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public void fillOnNotify(HttpRequest request, RechargeItem rechargeItem, int callbackType) {
        try {
            Map<String, String> params = toMap(request);
            String return_code = params.get("return_code");
            String transaction_id = params.get("transaction_id");
            String result_code = params.get("result_code");
            String bank_type = params.get("bank_type");
            String total_fee = params.get("total_fee");
            Integer totalfee1 = Integer.valueOf(Integer.parseInt(total_fee));
            double totalfee = totalfee1.intValue() / 100;
            String time_end = params.get("time_end");
            String nonce_str = params.get("nonce_str");
            rechargeItem.setThirdtradeno(transaction_id);
            rechargeItem.setThirdstatus(result_code);
            rechargeItem.setThirdaccount(bank_type);
            rechargeItem.setThirdamount(Double.valueOf(totalfee));
            rechargeItem.setThirdnotifyid(nonce_str);
            try {
                rechargeItem.setThirdtradetime(new SimpleDateFormat("yyyyMMddHHmmss").parse(time_end));
            } catch (Exception e) {
                LogUtils.getCoinLog().error(time_end + "进行日期转换出错了", e);
            }
            if ("SUCCESS".equals(return_code) && "SUCCESS".equals(result_code)) {
                rechargeItem.setStatus(2);
            } else if ("FAIL".equals(result_code)) {
                rechargeItem.setStatus(3);
            } else {
                rechargeItem.setStatus(99);
            }
            rechargeItem.setRemark(StringUtils.left(request.getHeaderString() + request.getBodyString(), 2048));
        } catch (Exception e2) {
            log.error("", e2);
        }
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public HttpResponse notifySuccess(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        return Resps.html(request, "<xml><return_code>SUCCESS</return_code><return_msg>OK</return_msg></xml>");
    }

    @Override // org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public HttpResponse notifyFail(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        return Resps.html(request, "<xml><return_code>FAIL</return_code><return_msg>fail</return_msg></xml>");
    }

    public static void main(String[] args) {
        double xx = NumberUtil.mul(2.07d, 100.0f);
        System.out.println(xx);
    }
}
