package org.tio.sitexxx.web.server.controller.p004wx;

import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.sitexxx.im.server.handler.wx.WxChatApi;
import org.tio.sitexxx.im.server.handler.wx.WxSynApi;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.WxChatItems;
import org.tio.sitexxx.service.model.main.WxFriend;
import org.tio.sitexxx.service.service.chat.ChatService;
import org.tio.sitexxx.service.service.chat.FriendService;
import org.tio.sitexxx.service.utils.RetUtils;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.json.Json;
import org.tio.utils.resp.Resp;

@RequestPath("/friend")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/wx/FriendController.class */
public class FriendController {
    private static Logger log = LoggerFactory.getLogger(FriendController.class);

    @RequestPath("/modifyRemarkname")
    public Resp modifyRemarkname(HttpRequest request, Integer frienduid, String remarkname) {
        User curr = WebUtils.currUser(request);
        if (!StrUtil.isNotBlank(remarkname)) {
            remarkname = "";
        }
        Ret ret = FriendService.me.updateRemarkName(curr.getId(), frienduid, remarkname);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        Long fid = (Long) RetUtils.getOkTData(ret);
        if (WxSynApi.isSynVersion()) {
            WxSynApi.synFdRemarkName(curr.getId(), fid, remarkname, (Long) RetUtils.getOkTData(ret, "chatlinkid"));
        } else {
            WxFriend sendFreind = FriendService.me.getFriendInfo(fid);
            WxChatItems chatItems = ChatService.me.getAllChatItems((Long) RetUtils.getOkTData(ret, "chatlinkid"));
            WxChatApi.autoUseSysChatNtf(curr.getId(), (byte) 33, "好友信息发生变更", Json.toJson(sendFreind), chatItems);
        }
        return Resp.ok(RetUtils.OPER_RIGHT);
    }
}
