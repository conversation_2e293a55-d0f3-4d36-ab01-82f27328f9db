package org.tio.sitexxx.web.server.controller.base.thirdlogin.provider;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import java.util.Date;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.common.session.HttpSession;
import org.tio.http.server.util.Resps;
import org.tio.sitexxx.service.model.main.UserThird;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.IThirdLogin;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/provider/Auth2Login.class */
public abstract class Auth2Login implements IThirdLogin {
    private static Logger log = LoggerFactory.getLogger(Auth2Login.class);
    public static final String STATE_KEY = "THIRD_LOGIN_STATE_KEY";

    public abstract String loginUrl(HttpRequest httpRequest, Integer num, String str) throws Exception;

    public abstract UserThird getUserThird(HttpRequest httpRequest, Integer num, String str, String str2) throws Exception;

    public static void main(String[] args) {
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.IThirdLogin
    public HttpResponse toLoginPage(HttpRequest request, Integer type) throws Exception {
        HttpSession session = request.getHttpSession();
        String state = IdUtil.simpleUUID();
        String url = loginUrl(request, type, state);
        if (StrUtil.isNotBlank(url)) {
            session.setAttribute(STATE_KEY, state, request.getHttpConfig());
            return Resps.redirect(request, url);
        }
        request.close();
        return null;
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.IThirdLogin
    public UserThird callback(HttpRequest request, Integer type) throws Exception {
        String state = request.getParam("state");
        String code = request.getParam("code");
        if (StrUtil.isBlank(state)) {
            request.close();
            return null;
        }
        HttpSession session = request.getHttpSession();
        String sessionState = (String) session.getAttribute(STATE_KEY, String.class);
        session.removeAttribute(STATE_KEY, request.getHttpConfig());
        if (!Objects.equals(state, sessionState)) {
            request.close();
            return null;
        }
        UserThird userThird = getUserThird(request, type, state, code);
        userThird.setType(type);
        userThird.setTime(new Date());
        return userThird;
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.IThirdLogin
    public boolean isAjax(HttpRequest request, Integer type) throws Exception {
        return false;
    }
}
