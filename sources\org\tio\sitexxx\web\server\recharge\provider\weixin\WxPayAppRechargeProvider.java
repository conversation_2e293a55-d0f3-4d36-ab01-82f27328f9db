package org.tio.sitexxx.web.server.recharge.provider.weixin;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import org.slf4j.Logger;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.util.Resps;
import org.tio.jfinal.kit.HashKit;
import org.tio.sitexxx.service.model.main.RechargeItem;
import org.tio.sitexxx.service.service.recharge.RechargeItemService;
import org.tio.sitexxx.servicecommon.utils.LogUtils;
import org.tio.sitexxx.web.server.recharge.RechargeUtils;
import org.tio.sitexxx.web.server.recharge.provider.alipay.AlipayConfig;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.Constants;
import org.tio.utils.json.Json;
import org.tio.utils.resp.Resp;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/provider/weixin/WxPayAppRechargeProvider.class */
public class WxPayAppRechargeProvider extends WxPayScan2RechargeProvider {
    private static Logger log = LogUtils.getCoinLog();

    /* renamed from: me */
    public static final WxPayAppRechargeProvider f28me = new WxPayAppRechargeProvider();

    protected WxPayAppRechargeProvider() {
    }

    @Override // org.tio.sitexxx.web.server.recharge.provider.weixin.WxPayScan2RechargeProvider, org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public HttpResponse toThirdRechargePage(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        Integer paytype = rechargeItem.getPaytype();
        String _subject = rechargeItem.getGoods();
        if (StrUtil.isBlank(_subject)) {
            _subject = "充值";
        }
        String total_fee = String.valueOf((int) NumberUtil.mul(rechargeItem.getAmount().doubleValue(), 100.0f));
        String tradeno = rechargeItem.getTradeno();
        String ip = request.getClientIp();
        Map<String, String> data = new HashMap<>();
        data.put("body", _subject);
        data.put("out_trade_no", tradeno);
        data.put("device_info", "");
        data.put("fee_type", "CNY");
        data.put("total_fee", total_fee);
        data.put("spbill_create_ip", ip);
        data.put("notify_url", RechargeUtils.getNotifyUrl(paytype, request));
        data.put("trade_type", TradeType.APP.getValue());
        data.put("product_id", tradeno);
        try {
            Map<String, String> r = this.wxpay.unifiedOrder(data);
            String returnCode = r.get("return_code");
            if ("SUCCESS".equals(returnCode)) {
                String result_code = r.get("result_code");
                if ("SUCCESS".equals(result_code)) {
                    String prepay_id = r.get("prepay_id");
                    if (StrUtil.isNotBlank(prepay_id)) {
                        rechargeItem.setThirdtradeno(prepay_id);
                        RechargeItemService.me.update(rechargeItem);
                        HashMap map = new HashMap();
                        map.put("appid", this.config.getAppID());
                        map.put("partnerid", this.config.getMchID());
                        map.put("prepayid", prepay_id);
                        map.put("package", "Sign=WXPay");
                        map.put("noncestr", System.currentTimeMillis() + "");
                        map.put("timestamp", (System.currentTimeMillis() / 1000) + "");
                        String packageSign = createSign(map, this.config.getKey());
                        map.put("sign", packageSign);
                        Map<String, Object> ret = new HashMap<>();
                        ret.put("wechat", map);
                        return Resps.json(request, Resp.ok(ret));
                    }
                } else {
                    log.error("微信APP充值，提交失败:{}", Json.toJson(r));
                }
            } else {
                log.error("微信APP充值，提交失败:{}", Json.toJson(r));
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return Resps.json(request, Resp.fail("未知异常"));
    }

    public static String createSign(Map<String, String> params, String partnerKey) {
        params.remove("sign");
        String stringA = packageSign(params, false);
        String stringSignTemp = stringA + "&key=" + partnerKey;
        return HashKit.md5(stringSignTemp).toUpperCase();
    }

    public static String packageSign(Map<String, String> params, boolean urlEncoder) {
        TreeMap<String, String> sortedParams = new TreeMap<>(params);
        StringBuilder sb = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> param : sortedParams.entrySet()) {
            String value = param.getValue();
            if (!StrUtil.isBlank(value)) {
                if (first) {
                    first = false;
                } else {
                    sb.append(Constants.SPE3);
                }
                sb.append(param.getKey()).append(Constants.SPE4);
                if (urlEncoder) {
                    try {
                        value = urlEncode(value);
                    } catch (UnsupportedEncodingException e) {
                    }
                }
                sb.append(value);
            }
        }
        return sb.toString();
    }

    public static String urlEncode(String src) throws UnsupportedEncodingException {
        return URLEncoder.encode(src, AlipayConfig.charset).replace("+", "%20");
    }
}
