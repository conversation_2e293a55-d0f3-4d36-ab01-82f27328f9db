package org.tio.sitexxx.web.server.controller.base.thirdlogin;

import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.sitexxx.service.model.main.UserThird;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/IThirdLogin.class */
public interface IThirdLogin {
    HttpResponse toLoginPage(HttpRequest httpRequest, Integer num) throws Exception;

    UserThird callback(HttpRequest httpRequest, Integer num) throws Exception;

    boolean isAjax(HttpRequest httpRequest, Integer num) throws Exception;
}
