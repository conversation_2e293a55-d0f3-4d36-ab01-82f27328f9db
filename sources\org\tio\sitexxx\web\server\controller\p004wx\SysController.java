package org.tio.sitexxx.web.server.controller.p004wx;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.UploadFile;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.sitexxx.service.model.main.File;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.service.chat.SysService;
import org.tio.sitexxx.service.utils.RetUtils;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.hutool.StrUtil;
import org.tio.utils.json.Json;
import org.tio.utils.resp.Resp;

@RequestPath("/sys")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/wx/SysController.class */
public class SysController {
    private static Logger log = LoggerFactory.getLogger(SysController.class);

    @RequestPath("/version")
    public Resp version(HttpRequest request, String version) throws Exception {
        RequestExt requestExt = WebUtils.getRequestExt(request);
        byte deviceType = requestExt.getDeviceType();
        if (requestExt.isFromAppAndroid()) {
            deviceType = 1;
        } else if (requestExt.isFromAppIos()) {
            deviceType = 2;
        }
        if (StrUtil.isBlank(version)) {
            version = requestExt.getAppVersion();
        }
        Ret ret = SysService.me.checkVersion(Byte.valueOf(deviceType), version);
        if (ret.isFail()) {
            return Resp.fail().msg(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/errlog")
    public Resp updateAvatar(HttpRequest request, UploadFile uploadFile) throws Exception {
        Integer uid;
        if (uploadFile == null) {
            return Resp.fail("上传信息为空");
        }
        User curr = WebUtils.currUser(request);
        if (curr != null) {
            uid = curr.getId();
        } else {
            uid = -777;
        }
        String sessionid = request.getHttpSession().getId();
        File dbFile = innerUploadFile(uid, uploadFile, sessionid);
        if (dbFile != null) {
            log.error("app出现了未知异常，已保存记录，上传记录：{}", Json.toJson(dbFile));
        }
        return Resp.ok();
    }

    private static File innerUploadFile(Integer uid, UploadFile uploadFile, String sessionid) throws Exception {
        byte[] bs = uploadFile.getData();
        String filename = uploadFile.getName();
        String ext = FileUtil.extName(filename);
        String resRootDir = Const.RES_ROOT;
        String path = "/app/log/err/" + DateUtil.format(new Date(), "yyyyMMddHH") + "/" + uid;
        java.io.File dir = new java.io.File(resRootDir, path);
        FileUtil.mkdir(dir);
        String url = path + "/" + filename;
        java.io.File file = new java.io.File(resRootDir, url);
        FileUtil.writeBytes(bs, file);
        File dbFile = new File();
        dbFile.setExt(ext);
        dbFile.setFilename(uploadFile.getName());
        dbFile.setSession(sessionid);
        dbFile.setSize(Long.valueOf(bs.length));
        dbFile.setUid(uid);
        dbFile.setUrl(url);
        dbFile.save();
        return dbFile;
    }
}
