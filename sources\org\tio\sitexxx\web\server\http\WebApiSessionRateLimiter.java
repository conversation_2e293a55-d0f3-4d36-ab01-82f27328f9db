package org.tio.sitexxx.web.server.http;

import cn.hutool.core.util.StrUtil;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HeaderName;
import org.tio.http.common.HeaderValue;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.common.session.limiter.SessionRateLimiter;
import org.tio.http.common.session.limiter.SessionRateVo;
import org.tio.sitexxx.service.service.conf.IpWhiteListService;
import org.tio.sitexxx.web.server.recharge.provider.alipay.AlipayConfig;
import org.tio.utils.SystemTimer;
import org.tio.utils.json.Json;
import org.tio.utils.resp.Resp;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/http/WebApiSessionRateLimiter.class */
public class WebApiSessionRateLimiter implements SessionRateLimiter {
    private static Logger log = LoggerFactory.getLogger(WebApiSessionRateLimiter.class);

    /* renamed from: me */
    public static final WebApiSessionRateLimiter f20me = new WebApiSessionRateLimiter();
    private final Map<String, Integer> intervalMap = new HashMap();
    private final Map<String, Integer> prefixMap = new HashMap();
    private final Map<String, Integer> allowCountMap = new HashMap();
    private HttpResponse response;

    private WebApiSessionRateLimiter() {
        this.intervalMap.put("/logout", 2000);
        this.intervalMap.put("/register/submit", 5000);
        this.intervalMap.put("/register/retrievePwd", 5000);
        this.intervalMap.put("/register/setNewPwd", 5000);
        this.intervalMap.put("/chat/createGroup", 3000);
        this.allowCountMap.put("/login", 12);
        this.allowCountMap.put("/user/search", 300);
        this.allowCountMap.put("/stat/ip", 15);
        this.allowCountMap.put("/chat/list", 60);
        this.response = new HttpResponse();
        try {
            this.response.setBody(Json.toJson(Resp.fail("操作过快，请稍后再操作").code(1005)).getBytes(AlipayConfig.charset));
        } catch (UnsupportedEncodingException e) {
            log.error("", e);
        }
        this.response.addHeader(HeaderName.Content_Type, HeaderValue.Content_Type.TEXT_PLAIN_JSON);
    }

    public boolean allow(HttpRequest request, SessionRateVo sessionRateVo) {
        if (IpWhiteListService.isWhiteIp(request.getClientIp())) {
            return true;
        }
        String path = request.getRequestLine().getPath();
        Integer allowCount = this.allowCountMap.get(path);
        if (allowCount == null) {
            allowCount = 120;
        }
        if (sessionRateVo.getAccessCount().get() > allowCount.intValue()) {
            return false;
        }
        Integer iv = this.intervalMap.get(path);
        if (iv == null && this.prefixMap.size() > 0) {
            Set<Map.Entry<String, Integer>> set = this.prefixMap.entrySet();
            Iterator<Map.Entry<String, Integer>> it = set.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                Map.Entry<String, Integer> entry = it.next();
                if (StrUtil.startWith(path, entry.getKey())) {
                    iv = entry.getValue();
                    break;
                }
            }
        }
        if (iv != null && SystemTimer.currTime - sessionRateVo.getLastAccessTime() < iv.intValue()) {
            return false;
        }
        return true;
    }

    public HttpResponse response(HttpRequest request, SessionRateVo sessionRateVo) {
        return this.response;
    }
}
