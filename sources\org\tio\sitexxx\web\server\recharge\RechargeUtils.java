package org.tio.sitexxx.web.server.recharge;

import org.slf4j.Logger;
import org.tio.http.common.HttpRequest;
import org.tio.sitexxx.servicecommon.utils.LogUtils;
import org.tio.sitexxx.servicecommon.vo.Const;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/RechargeUtils.class */
public class RechargeUtils {
    private static Logger log = LogUtils.getCoinLog();

    public static String getReturnUrl(Integer paytype, HttpRequest request) {
        return Const.SITE + request.getHttpConfig().getContextPath() + "/recharge/rt/" + paytype + request.getHttpConfig().getSuffix();
    }

    public static String getNotifyUrl(Integer paytype, HttpRequest request) {
        return Const.SITE + request.getHttpConfig().getContextPath() + "/recharge/nf/" + paytype + request.getHttpConfig().getSuffix();
    }
}
