package org.tio.sitexxx.web.server.controller.base;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.sitexxx.service.service.VideoService;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/video")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/VideoController.class */
public class VideoController {
    private static Logger log = LoggerFactory.getLogger(VideoController.class);

    @RequestPath("/updateStatus")
    public Resp updateStatus(HttpRequest request, int id, byte status) {
        VideoService.me.updateStatus(id, status);
        WebUtils.clearHttpcache("/video/page1");
        WebUtils.clearHttpcache("/video/page");
        return Resp.ok();
    }

    @RequestPath("/reTitle")
    public Resp reTitle(HttpRequest request, int id, String title) {
        VideoService.me.reTitle(id, title);
        WebUtils.clearHttpcache("/video/page1");
        WebUtils.clearHttpcache("/video/page");
        return Resp.ok();
    }
}
