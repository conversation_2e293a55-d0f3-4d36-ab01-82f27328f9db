package org.tio.sitexxx.web.server.p007vo;

import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpResponse;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.servicecommon.vo.MulLauUtils;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/vo/LoginResult.class */
public class LoginResult {
    private static Logger log = LoggerFactory.getLogger(LoginResult.class);
    private Code code;
    private ErrorCode errorCode;
    private User user;
    private HttpResponse httpResponse = null;

    /* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/vo/LoginResult$Code.class */
    public enum Code {
        SUCCESS(1),
        FAIL(2);

        Integer value;

        public static Code from(Integer code) {
            Code[] values = values();
            for (Code v : values) {
                if (Objects.equals(v.value, code)) {
                    return v;
                }
            }
            return SUCCESS;
        }

        Code(Integer value) {
            this.value = value;
        }
    }

    /* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/vo/LoginResult$ErrorCode.class */
    public enum ErrorCode {
        USER_OR_PWD_ERROR_PWD(1, MulLauUtils.getMsg("用户名或密码不正确")),
        USER_OR_PWD_SMSCODE_PWD(1, MulLauUtils.getMsg("验证码不正确")),
        USER_OR_PWD_ERROR_SMS(1, "用户名或验证码不正确"),
        USER_INBLACK_ERROR(4, "该账号已禁用"),
        USER_STATUS_ERROR(3, "无效状态用户");

        public Integer code;
        public String value;

        public static ErrorCode from(String code) {
            ErrorCode[] values = values();
            for (ErrorCode v : values) {
                if (Objects.equals(v.value, code)) {
                    return v;
                }
            }
            return USER_OR_PWD_ERROR_PWD;
        }

        ErrorCode(Integer code, String value) {
            this.code = code;
            this.value = value;
        }
    }

    public static LoginResult fail(ErrorCode errorCode, HttpResponse httpResponse) {
        LoginResult ret = new LoginResult(Code.FAIL, null, httpResponse);
        ret.setErrorCode(errorCode);
        return ret;
    }

    public static void main(String[] args) {
        log.info("");
    }

    public static LoginResult success(User user, HttpResponse httpResponse) {
        LoginResult ret = new LoginResult(Code.SUCCESS, user, httpResponse);
        return ret;
    }

    private LoginResult(Code code, User user, HttpResponse httpResponse) {
        this.user = null;
        this.code = code;
        this.user = user;
        setHttpResponse(httpResponse);
    }

    public Code getCode() {
        return this.code;
    }

    public ErrorCode getErrorCode() {
        return this.errorCode;
    }

    public HttpResponse getHttpResponse() {
        return this.httpResponse;
    }

    public User getUser() {
        return this.user;
    }

    public void setCode(Code code) {
        this.code = code;
    }

    public void setErrorCode(ErrorCode errorCode) {
        this.errorCode = errorCode;
    }

    public void setHttpResponse(HttpResponse httpResponse) {
        this.httpResponse = httpResponse;
    }

    public void setUser(User user) {
        this.user = user;
    }
}
