package org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.apple;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.common.session.HttpSession;
import org.tio.http.server.util.Resps;
import org.tio.jfinal.kit.Ret;
import org.tio.sitexxx.service.model.main.UserThird;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login;
import org.tio.sitexxx.web.server.init.WebApiInit;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.crypto.Md5;
import org.tio.utils.resp.Resp;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/provider/apple/AppleLogin.class */
public class AppleLogin extends Auth2Login {
    private static Logger log = LoggerFactory.getLogger(AppleLogin.class);

    /* renamed from: me */
    public static AppleLogin f6me = new AppleLogin();

    private AppleLogin() {
    }

    public static void main(String[] args) {
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login
    public String loginUrl(HttpRequest request, Integer type, String state) throws Exception {
        RequestExt requestExt = WebUtils.getRequestExt(request);
        if (!requestExt.isFromAppIos()) {
            return null;
        }
        return "/tlogin/cb/p/" + type;
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login
    public UserThird getUserThird(HttpRequest request, Integer type, String state, String code) throws Exception {
        RequestExt requestExt = WebUtils.getRequestExt(request);
        if (!requestExt.isFromAppIos()) {
            return null;
        }
        String sign = request.getParam("sign");
        if (StrUtil.isBlank(sign)) {
            return null;
        }
        String imei = requestExt.getImei();
        String pkey = ConfService.getString("third.login.apple.app.key", "jkijhbmn");
        String str = state + pkey + imei;
        String mysign = Md5.getMD5(str);
        if (!Objects.equals(mysign, sign)) {
            return null;
        }
        UserThird userThird = new UserThird();
        userThird.setOpenid(imei);
        userThird.setNick("IOS游客" + WebApiInit.sessionIdGenerator.sessionId(request.getHttpConfig(), request));
        return userThird;
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login, org.tio.sitexxx.web.server.controller.base.thirdlogin.IThirdLogin
    public HttpResponse toLoginPage(HttpRequest request, Integer type) throws Exception {
        HttpSession session = request.getHttpSession();
        String state = RandomUtil.simpleUUID();
        String url = loginUrl(request, type, state);
        if (StrUtil.isNotBlank(url)) {
            session.setAttribute(Auth2Login.STATE_KEY, state, request.getHttpConfig());
            Ret ret = Ret.ok("url", url);
            ret.set("state", state);
            Resp resp = Resp.ok(ret);
            return Resps.json(request, resp);
        }
        request.close();
        return null;
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.provider.Auth2Login, org.tio.sitexxx.web.server.controller.base.thirdlogin.IThirdLogin
    public boolean isAjax(HttpRequest request, Integer type) throws Exception {
        return true;
    }
}
