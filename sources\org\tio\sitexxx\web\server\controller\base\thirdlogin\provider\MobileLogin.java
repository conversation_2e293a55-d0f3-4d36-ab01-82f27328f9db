package org.tio.sitexxx.web.server.controller.base.thirdlogin.provider;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.util.Resps;
import org.tio.sitexxx.service.cache.CacheConfig;
import org.tio.sitexxx.service.cache.Caches;
import org.tio.sitexxx.service.model.main.UserThird;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.web.server.controller.base.thirdlogin.IThirdLogin;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.cache.ICache;
import org.tio.utils.crypto.Md5;
import org.tio.utils.resp.Resp;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/thirdlogin/provider/MobileLogin.class */
public abstract class MobileLogin implements IThirdLogin {
    private static Logger log = LoggerFactory.getLogger(MobileLogin.class);
    final ICache cache = Caches.getCache(CacheConfig.TIME_TO_LIVE_SECONDS_5);

    public abstract UserThird.SubTable createSubTable(HttpRequest httpRequest, Integer num);

    public static void main(String[] args) {
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.IThirdLogin
    public HttpResponse toLoginPage(HttpRequest request, Integer type) throws Exception {
        String openid = request.getParam("openid");
        String sign = request.getParam("sign");
        String mysign = Md5.getMD5(openid + request.getHttpSession().getId() + type);
        if (!Objects.equals(mysign, sign)) {
            RequestExt requestExt = WebUtils.getRequestExt(request);
            log.error("appversion:{}, {}, 验签没通过,mysign:{},sign:{}", new Object[]{requestExt.getAppVersion(), requestExt.getDeviceinfo(), mysign, sign});
            request.close();
            return null;
        }
        String uuid = IdUtil.simpleUUID();
        this.cache.put(uuid, openid);
        return Resps.json(request, Resp.ok(uuid));
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.IThirdLogin
    public UserThird callback(HttpRequest request, Integer type) throws Exception {
        String uuid = request.getParam("uuid");
        if (StrUtil.isBlank(uuid)) {
            return null;
        }
        String openid = request.getParam("openid");
        String unionid = request.getParam("unionid");
        String myopenid = (String) this.cache.get(uuid, String.class);
        if (StrUtil.isBlank(openid) || StrUtil.isBlank(myopenid) || !Objects.equals(myopenid, openid)) {
            log.error("myopenid:{}, openid:{}", myopenid, openid);
            return null;
        }
        String nick = request.getParam("nick");
        String avatar = request.getParam("avatar");
        String sex = request.getParam("sex");
        UserThird userThird = new UserThird();
        userThird.setAvatar(avatar);
        userThird.setNick(nick);
        userThird.setOpenid(openid);
        userThird.setUnionid(unionid);
        if (StrUtil.isNotBlank(sex)) {
            userThird.setSex(Integer.valueOf(Integer.parseInt(sex)));
        }
        userThird.setType(type);
        UserThird.SubTable sub = createSubTable(request, type);
        if (sub != null) {
            userThird.setSubTable(sub);
        }
        return userThird;
    }

    @Override // org.tio.sitexxx.web.server.controller.base.thirdlogin.IThirdLogin
    public boolean isAjax(HttpRequest request, Integer type) throws Exception {
        return true;
    }
}
