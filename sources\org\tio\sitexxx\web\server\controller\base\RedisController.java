package org.tio.sitexxx.web.server.controller.base;

import cn.hutool.core.date.DateUtil;
import org.redisson.api.RKeys;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.sitexxx.service.init.RedisInit;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.service.base.UserService;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.utils.cache.redis.RedisCache;
import org.tio.utils.resp.Resp;

@RequestPath("/redis")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/controller/base/RedisController.class */
public class RedisController {
    private static Logger log = LoggerFactory.getLogger(RedisController.class);

    @RequestPath("/getTtl")
    public Resp getTtl(HttpRequest request, String cacheName, String key) throws Exception {
        RedisCache redisCache = RedisCache.getCache(cacheName);
        if (redisCache == null) {
            return Resp.fail("cacheName【" + cacheName + "】不存在");
        }
        long remainTimeToLive = redisCache.ttl(key);
        if (remainTimeToLive == -1) {
            return Resp.fail("不会超时(-1)");
        }
        if (remainTimeToLive == -2) {
            return Resp.fail("key不存在(-2)");
        }
        return Resp.ok(DateUtil.formatBetween(remainTimeToLive));
    }

    @RequestPath("/clean")
    public Resp clean(HttpRequest request, String cacheName) throws Exception {
        User currUser = WebUtils.currUser(request);
        if (currUser == null || !UserService.isSuper(currUser)) {
            return Resp.fail("你没资格清除redis缓存");
        }
        RedisCache redisCache = RedisCache.getCache(cacheName);
        if (redisCache == null) {
            RedissonClient redisson = RedisInit.get();
            RKeys keys = redisson.getKeys();
            keys.deleteByPatternAsync(RedisCache.keyPrefix(cacheName) + "*");
        } else {
            redisCache.clear();
        }
        return Resp.ok();
    }
}
