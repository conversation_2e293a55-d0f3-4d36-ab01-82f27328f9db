package org.tio.sitexxx.web.server.yanxun.friendgroup;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.plugin.activerecord.Record;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.WxChatUserItem;
import org.tio.sitexxx.service.model.main.WxFriend;
import org.tio.sitexxx.service.model.main.WxFriendGroup;
import org.tio.sitexxx.service.service.base.UserService;
import org.tio.sitexxx.service.service.chat.ChatIndexService;
import org.tio.sitexxx.service.service.chat.ChatService;
import org.tio.sitexxx.service.service.chat.FriendService;
import org.tio.sitexxx.service.service.yanxun.friendGroup.FriendGroupService;
import org.tio.sitexxx.service.utils.RetUtils;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.Constants;
import org.tio.utils.hutool.StrUtil;
import org.tio.utils.resp.Resp;

@RequestPath("/friendGroup")
/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/yanxun/friendgroup/FriendGroupController.class */
public class FriendGroupController {
    private static Logger log = LoggerFactory.getLogger(FriendGroupController.class);
    public static final FriendGroupService groupService = FriendGroupService.me;

    @RequestPath("/test")
    public Resp test() {
        return groupService.delete(5);
    }

    @RequestPath("/list")
    public Resp friendGroupList(HttpRequest request) throws Exception {
        WxFriend friend;
        List<WxFriendGroup> list = groupService.queryGroupList(WebUtils.currUserId(request));
        ArrayList arrayList = new ArrayList();
        if (list.size() > 0) {
            for (int j = 0; j < list.size(); j++) {
                HashMap map = new HashMap();
                WxFriendGroup item = list.get(j);
                List friendListId = new ArrayList();
                if (!StrUtil.isEmpty(item.getFriendidlist())) {
                    String[] friendIdLists = item.getFriendidlist().split(Constants.SPE1);
                    friendListId = Arrays.asList(friendIdLists);
                }
                ArrayList arrayList2 = new ArrayList();
                for (int i = 0; i < friendListId.size(); i++) {
                    Integer userId = Integer.valueOf(Integer.parseInt((String) friendListId.get(i)));
                    User curr = WebUtils.currUser(request);
                    Record record = UserService.ME.info(userId);
                    if (curr != null) {
                        WxChatUserItem fd = ChatIndexService.fdUserIndex(curr.getId(), userId);
                        if (ChatService.existFriend(fd) && (friend = FriendService.me.getFriendInfo(fd.getLinkid())) != null) {
                            HashMap map2 = new HashMap();
                            map2.put("id", friend.getId());
                            map2.put("nick", record.get("nick"));
                            map2.put("uid", record.get("id"));
                            map2.put("chatindex", friend.getChatindex());
                            map2.put("avatar", record.get("avatar"));
                            arrayList2.add(map2);
                        }
                    }
                }
                map.put("id", item.getId());
                map.put("createtime", item.getCreatetime());
                map.put("friendidlist", arrayList2);
                map.put("groupname", item.getGroupname());
                map.put("uid", item.getUid());
                arrayList.add(map);
            }
        }
        Map<String, Object> map3 = new HashMap<>();
        map3.put("groupList", arrayList);
        return Resp.ok(map3);
    }

    @RequestPath("/add")
    public Resp friendGroupAdd(WxFriendGroup friendGroup, HttpRequest request) {
        try {
            if (null != groupService.queryGroupName(WebUtils.currUserId(request), friendGroup.getGroupname())) {
                return Resp.fail("分组名称已存在");
            }
            if (friendGroup.getGroupname().isEmpty()) {
                return Resp.fail("请输入分组名称");
            }
            if (0 == friendGroup.getUid().intValue()) {
                friendGroup.setUid(WebUtils.currUserId(request));
            }
            if (groupService.createGroup(friendGroup).isOk()) {
                return Resp.ok(RetUtils.OPER_RIGHT);
            }
            return Resp.fail("操作失败");
        } catch (Exception e) {
            e.printStackTrace();
            return Resp.fail("系统错误");
        }
    }

    @RequestPath("/updateGroupUserList")
    public Resp updateGroupUserList(String groupId, String userIdListStr, HttpRequest request) {
        try {
            if (StringUtils.isEmpty(groupId) || groupId.equals("0")) {
                return Resp.fail("无效参数");
            }
            Resp resp = groupService.updateGroupUserList(WebUtils.currUserId(request), Integer.parseInt(groupId), userIdListStr);
            return resp;
        } catch (Exception e) {
            return Resp.fail("系统错误");
        }
    }

    @RequestPath("/updateFriend")
    public Resp updateFriend(Integer toUserId, String groupIdStr) {
        return null;
    }

    @RequestPath("/update")
    public Resp friendGroupUpdate(String groupId, String groupName, HttpRequest request) {
        try {
            if (StringUtils.isEmpty(groupId) || groupId.equals("0")) {
                return Resp.fail("无效参数");
            }
            if (StrUtil.isEmpty(groupName)) {
                return Resp.fail("请输入分组名称");
            }
            if (null != groupService.queryGroupName(WebUtils.currUserId(request), groupName)) {
                return Resp.fail("记录已存在");
            }
            Resp resp = groupService.updateGroupName(WebUtils.currUserId(request), Integer.parseInt(groupId), groupName);
            return resp;
        } catch (Exception e) {
            e.printStackTrace();
            return Resp.fail("系统错误");
        }
    }

    @RequestPath("/delete")
    public Resp friendGroupDelete(Integer groupId) {
        if (groupId != null) {
            try {
                if (groupId.intValue() != 0) {
                    Resp resp = groupService.delete(groupId.intValue());
                    return resp;
                }
            } catch (Exception e) {
                e.printStackTrace();
                return Resp.fail("系统错误");
            }
        }
        return Resp.fail("无效参数");
    }
}
