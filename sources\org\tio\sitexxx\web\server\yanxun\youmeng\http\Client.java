package org.tio.sitexxx.web.server.yanxun.youmeng.http;

import org.tio.sitexxx.web.server.recharge.IRechargeProvider;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.enums.Method;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.util.HttpUtil;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/yanxun/youmeng/http/Client.class */
public class Client {

    /* renamed from: org.tio.sitexxx.web.server.yanxun.youmeng.http.Client$1 */
    /* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/yanxun/youmeng/http/Client$1.class */
    static /* synthetic */ class C00441 {

        /* renamed from: $SwitchMap$org$tio$sitexxx$web$server$yanxun$youmeng$http$enums$Method */
        static final /* synthetic */ int[] f35x1dba61b6 = new int[Method.values().length];

        static {
            try {
                f35x1dba61b6[Method.GET.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                f35x1dba61b6[Method.POST_FORM.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                f35x1dba61b6[Method.POST_STRING.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                f35x1dba61b6[Method.POST_BYTES.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                f35x1dba61b6[Method.PUT_STRING.ordinal()] = 5;
            } catch (NoSuchFieldError e5) {
            }
            try {
                f35x1dba61b6[Method.PUT_BYTES.ordinal()] = 6;
            } catch (NoSuchFieldError e6) {
            }
            try {
                f35x1dba61b6[Method.DELETE.ordinal()] = 7;
            } catch (NoSuchFieldError e7) {
            }
        }
    }

    public static Response execute(Request request) throws Exception {
        switch (C00441.f35x1dba61b6[request.getMethod().ordinal()]) {
            case IRechargeProvider.CallType.RETURN /* 1 */:
                return HttpUtil.httpGet(request.getHost(), request.getPath(), request.getTimeout(), request.getHeaders(), request.getQuerys(), request.getSignHeaderPrefixList(), request.getAppKey(), request.getAppSecret());
            case IRechargeProvider.CallType.NOTIFY /* 2 */:
                return HttpUtil.httpPost(request.getHost(), request.getPath(), request.getTimeout(), request.getHeaders(), request.getQuerys(), request.getBodys(), request.getSignHeaderPrefixList(), request.getAppKey(), request.getAppSecret());
            case 3:
                return HttpUtil.httpPost(request.getHost(), request.getPath(), request.getTimeout(), request.getHeaders(), request.getQuerys(), request.getStringBody(), request.getSignHeaderPrefixList(), request.getAppKey(), request.getAppSecret());
            case 4:
                return HttpUtil.httpPost(request.getHost(), request.getPath(), request.getTimeout(), request.getHeaders(), request.getQuerys(), request.getBytesBody(), request.getSignHeaderPrefixList(), request.getAppKey(), request.getAppSecret());
            case 5:
                return HttpUtil.httpPut(request.getHost(), request.getPath(), request.getTimeout(), request.getHeaders(), request.getQuerys(), request.getStringBody(), request.getSignHeaderPrefixList(), request.getAppKey(), request.getAppSecret());
            case 6:
                return HttpUtil.httpPut(request.getHost(), request.getPath(), request.getTimeout(), request.getHeaders(), request.getQuerys(), request.getBytesBody(), request.getSignHeaderPrefixList(), request.getAppKey(), request.getAppSecret());
            case 7:
                return HttpUtil.httpDelete(request.getHost(), request.getPath(), request.getTimeout(), request.getHeaders(), request.getQuerys(), request.getSignHeaderPrefixList(), request.getAppKey(), request.getAppSecret());
            default:
                throw new IllegalArgumentException(String.format("unsupported method:%s", request.getMethod()));
        }
    }
}
