package org.tio.sitexxx.web.server.recharge.provider.apple.p006vo;

import java.util.List;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/provider/apple/vo/Receipt.class */
public class Receipt {
    private String receipt_type;
    private long adam_id;
    private long app_item_id;
    private String bundle_id;
    private String application_version;
    private long download_id;
    private long version_external_identifier;
    private String receipt_creation_date;
    private String receipt_creation_date_ms;
    private String receipt_creation_date_pst;
    private String request_date;
    private String request_date_ms;
    private String request_date_pst;
    private String original_purchase_date;
    private String original_purchase_date_ms;
    private String original_purchase_date_pst;
    private String original_application_version;
    private List<In_app> in_app;

    public void setReceipt_type(String receipt_type) {
        this.receipt_type = receipt_type;
    }

    public String getReceipt_type() {
        return this.receipt_type;
    }

    public void setAdam_id(long adam_id) {
        this.adam_id = adam_id;
    }

    public long getAdam_id() {
        return this.adam_id;
    }

    public void setApp_item_id(long app_item_id) {
        this.app_item_id = app_item_id;
    }

    public long getApp_item_id() {
        return this.app_item_id;
    }

    public void setBundle_id(String bundle_id) {
        this.bundle_id = bundle_id;
    }

    public String getBundle_id() {
        return this.bundle_id;
    }

    public void setApplication_version(String application_version) {
        this.application_version = application_version;
    }

    public String getApplication_version() {
        return this.application_version;
    }

    public void setDownload_id(long download_id) {
        this.download_id = download_id;
    }

    public long getDownload_id() {
        return this.download_id;
    }

    public void setVersion_external_identifier(long version_external_identifier) {
        this.version_external_identifier = version_external_identifier;
    }

    public long getVersion_external_identifier() {
        return this.version_external_identifier;
    }

    public void setReceipt_creation_date(String receipt_creation_date) {
        this.receipt_creation_date = receipt_creation_date;
    }

    public String getReceipt_creation_date() {
        return this.receipt_creation_date;
    }

    public void setReceipt_creation_date_ms(String receipt_creation_date_ms) {
        this.receipt_creation_date_ms = receipt_creation_date_ms;
    }

    public String getReceipt_creation_date_ms() {
        return this.receipt_creation_date_ms;
    }

    public void setReceipt_creation_date_pst(String receipt_creation_date_pst) {
        this.receipt_creation_date_pst = receipt_creation_date_pst;
    }

    public String getReceipt_creation_date_pst() {
        return this.receipt_creation_date_pst;
    }

    public void setRequest_date(String request_date) {
        this.request_date = request_date;
    }

    public String getRequest_date() {
        return this.request_date;
    }

    public void setRequest_date_ms(String request_date_ms) {
        this.request_date_ms = request_date_ms;
    }

    public String getRequest_date_ms() {
        return this.request_date_ms;
    }

    public void setRequest_date_pst(String request_date_pst) {
        this.request_date_pst = request_date_pst;
    }

    public String getRequest_date_pst() {
        return this.request_date_pst;
    }

    public void setOriginal_purchase_date(String original_purchase_date) {
        this.original_purchase_date = original_purchase_date;
    }

    public String getOriginal_purchase_date() {
        return this.original_purchase_date;
    }

    public void setOriginal_purchase_date_ms(String original_purchase_date_ms) {
        this.original_purchase_date_ms = original_purchase_date_ms;
    }

    public String getOriginal_purchase_date_ms() {
        return this.original_purchase_date_ms;
    }

    public void setOriginal_purchase_date_pst(String original_purchase_date_pst) {
        this.original_purchase_date_pst = original_purchase_date_pst;
    }

    public String getOriginal_purchase_date_pst() {
        return this.original_purchase_date_pst;
    }

    public void setOriginal_application_version(String original_application_version) {
        this.original_application_version = original_application_version;
    }

    public String getOriginal_application_version() {
        return this.original_application_version;
    }

    public void setIn_app(List<In_app> in_app) {
        this.in_app = in_app;
    }

    public List<In_app> getIn_app() {
        return this.in_app;
    }
}
