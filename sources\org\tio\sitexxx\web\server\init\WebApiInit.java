package org.tio.sitexxx.web.server.init;

import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpConfig;
import org.tio.http.common.HttpResponse;
import org.tio.http.common.session.id.impl.SnowflakeSessionIdGenerator;
import org.tio.http.server.HttpServerStarter;
import org.tio.http.server.handler.DefaultHttpRequestHandler;
import org.tio.http.server.mvc.Routes;
import org.tio.http.server.session.DomainSessionCookieDecorator;
import org.tio.http.server.stat.ip.path.IpPathAccessStats;
import org.tio.http.server.stat.token.TokenPathAccessStats;
import org.tio.server.ServerTioConfig;
import org.tio.sitexxx.service.init.PropInit;
import org.tio.sitexxx.service.init.RedisInit;
import org.tio.sitexxx.service.pay.service.PayService;
import org.tio.sitexxx.service.tio.TioSiteIpStatListener;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.sitexxx.web.server.WebApiRoot;
import org.tio.sitexxx.web.server.auth.AccessCtrlConfig;
import org.tio.sitexxx.web.server.http.TioSiteThrowableHandler;
import org.tio.sitexxx.web.server.http.WebApiHttpServerInterceptor;
import org.tio.sitexxx.web.server.http.WebApiHttpSessionListener;
import org.tio.sitexxx.web.server.http.WebApiSessionRateLimiter;
import org.tio.sitexxx.web.server.http.stat.TioSiteStatPathFilter;
import org.tio.sitexxx.web.server.http.stat.p005ip.TioSiteIpPathAccessStatListener;
import org.tio.sitexxx.web.server.http.stat.token.TioSiteCurrUseridGetter;
import org.tio.sitexxx.web.server.http.stat.token.TioSiteTokenGetter;
import org.tio.sitexxx.web.server.http.stat.token.TioSiteTokenPathAccessStatListener;
import org.tio.utils.Threads;
import org.tio.utils.cache.caffeineredis.CaffeineRedisCache;
import org.tio.utils.jfinal.P;
import org.tio.utils.json.Json;
import org.tio.utils.resp.Resp;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/init/WebApiInit.class */
public class WebApiInit {
    public static HttpServerStarter httpServerStarter;
    public static ServerTioConfig serverTioConfig;
    public static HttpConfig httpConfig;
    public static Routes routes;
    public static DefaultHttpRequestHandler requestHandler;
    private static Logger log = LoggerFactory.getLogger(WebApiInit.class);
    public static SnowflakeSessionIdGenerator sessionIdGenerator = null;
    public static final Long startTime = Long.valueOf(System.currentTimeMillis());

    public static void init() throws Exception {
        PropInit.init();
        int port = P.getInt("http.api.port").intValue();
        long sessionTimeout = P.getLong("http.session.timeout", 1800L).longValue();
        String contextpath = Const.API_CONTEXTPATH;
        String suffix = Const.API_SUFFIX;
        httpConfig = new HttpConfig(Integer.valueOf(port), Long.valueOf(sessionTimeout), contextpath, suffix);
        httpConfig.setSessionCacheName(P.get("http.session.cache.name", "tio-h-s"));
        Integer maxLiveTimeOfStaticRes = P.getInt("http.maxLiveTimeOfStaticRes");
        if (maxLiveTimeOfStaticRes != null) {
            httpConfig.setMaxLiveTimeOfStaticRes(maxLiveTimeOfStaticRes.intValue());
        }
        Integer max_length_of_multi_body = P.getInt("http.max_length_of_multi_body");
        if (max_length_of_multi_body != null) {
            httpConfig.setMaxLengthOfMultiBody(max_length_of_multi_body.intValue() * 1024);
        }
        Integer max_length_of_post_body = P.getInt("http.max_length_of_post_body");
        if (max_length_of_post_body != null) {
            httpConfig.setMaxLengthOfPostBody(max_length_of_post_body.intValue() * 1024);
        }
        int workerid = P.getInt("uuid.workerid").intValue();
        int datacenter = P.getInt("uuid.datacenter").intValue();
        sessionIdGenerator = new SnowflakeSessionIdGenerator(workerid, datacenter);
        httpConfig.setSessionIdGenerator(sessionIdGenerator);
        httpConfig.setAppendRequestHeaderString(true);
        httpConfig.setPage500(P.get("http.api.page500", "/500.html"));
        httpConfig.setPage404(P.get("http.api.page404", "/404.html"));
        httpConfig.setSessionCookieName(Const.Http.SESSION_COOKIE_NAME);
        String pageRoot = P.get("http.api.page", (String) null);
        httpConfig.setPageRoot(pageRoot);
        CaffeineRedisCache caffeineRedisCache = CaffeineRedisCache.register(RedisInit.get(), httpConfig.getSessionCacheName(), (Long) null, Long.valueOf(sessionTimeout));
        httpConfig.setSessionStore(caffeineRedisCache);
        httpConfig.setProxied(P.getBoolean("http.isproxied", false).booleanValue());
        byte[] bs = Json.toJson(Resp.fail("Your IP is on the blacklist")).getBytes();
        httpConfig.setRespForBlackIp(new HttpResponse((Map) null, bs));
        httpConfig.setSessionRateLimiter(WebApiSessionRateLimiter.f20me);
        String[] scanPackages = {WebApiRoot.class.getPackage().getName()};
        routes = new Routes(scanPackages);
        requestHandler = new DefaultHttpRequestHandler(httpConfig, routes);
        requestHandler.setCompatibilityAssignment(false);
        AccessCtrlConfig accessCtrlConfig = new AccessCtrlConfig("access-url-role.properties", routes.PATH_METHOD_MAP.keySet(), false);
        WebApiHttpServerInterceptor.f18ME.setAccessCtrlConfig(accessCtrlConfig);
        requestHandler.setHttpServerInterceptor(WebApiHttpServerInterceptor.f18ME);
        requestHandler.setHttpSessionListener(WebApiHttpSessionListener.f19ME);
        String cookieDomain = P.get("http.cookie.domain");
        DomainSessionCookieDecorator domainSessionCookieDecorator = new DomainSessionCookieDecorator(cookieDomain);
        requestHandler.setSessionCookieDecorator(domainSessionCookieDecorator);
        requestHandler.setThrowableHandler(TioSiteThrowableHandler.f17ME);
        httpConfig.setName("Web-Api");
        httpServerStarter = new HttpServerStarter(httpConfig, requestHandler, Threads.getTioExecutor(), Threads.getGroupExecutor());
        httpServerStarter.getTioServer().setCheckLastVersion(P.getBoolean("tio.setCheckLastVersion", false).booleanValue());
        serverTioConfig = httpServerStarter.getServerTioConfig();
        serverTioConfig.logWhenDecodeError = P.getBoolean("http.api.logWhenDecodeError", false).booleanValue();
        serverTioConfig.setName("Tio-Site Api HttpServer");
        serverTioConfig.setReadBufferSize(P.getInt("http.api.readbuffersize", 20480).intValue());
        serverTioConfig.setIpStatListener(TioSiteIpStatListener.web_api);
        serverTioConfig.ipStats.addDurations(Const.IpStatDuration.IPSTAT_DURATIONS);
        boolean isWebApiUseSsl = P.getInt("web.api.use.ssl", 2).intValue() == 1;
        if (isWebApiUseSsl) {
            String keyStoreFile = P.get("ssl.keystore", (String) null);
            String trustStoreFile = P.get("ssl.truststore", (String) null);
            String keyStorePwd = P.get("ssl.pwd", (String) null);
            serverTioConfig.useSsl(keyStoreFile, trustStoreFile, keyStorePwd);
        }
        IpPathAccessStats ipPathAccessStats = new IpPathAccessStats(TioSiteStatPathFilter.f21me, serverTioConfig, TioSiteIpPathAccessStatListener.ME_SITE_API, Const.IpPathAccessStatDuration.IP_PATH_ACCESS_STAT_DURATIONS);
        requestHandler.setIpPathAccessStats(ipPathAccessStats);
        TokenPathAccessStats tokenPathAccessStats = new TokenPathAccessStats(TioSiteStatPathFilter.f21me, TioSiteTokenGetter.f23me, TioSiteCurrUseridGetter.f22me, serverTioConfig, TioSiteTokenPathAccessStatListener.ME_SITE_API, Const.TokenPathAccessStatDuration.TOKEN_PATH_ACCESS_STAT_DURATIONS);
        requestHandler.setTokenPathAccessStats(tokenPathAccessStats);
        HttpcacheInit.init(routes);
        PayService.initPay();
        httpServerStarter.start();
    }
}
