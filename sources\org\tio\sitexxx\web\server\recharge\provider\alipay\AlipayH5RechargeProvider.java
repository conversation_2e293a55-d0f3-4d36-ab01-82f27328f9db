package org.tio.sitexxx.web.server.recharge.provider.alipay;

import cn.hutool.core.util.StrUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradeWapPayModel;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import org.slf4j.Logger;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.util.Resps;
import org.tio.sitexxx.service.model.main.RechargeItem;
import org.tio.sitexxx.servicecommon.utils.LogUtils;
import org.tio.sitexxx.web.server.recharge.RechargeUtils;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/recharge/provider/alipay/AlipayH5RechargeProvider.class */
public class AlipayH5RechargeProvider extends AlipayRechargeProvider {
    private static Logger log = LogUtils.getCoinLog();

    /* renamed from: me */
    public static final AlipayH5RechargeProvider f25me = new AlipayH5RechargeProvider();

    /* JADX INFO: Thrown type has an unknown type hierarchy: com.alipay.api.AlipayApiException */
    @Override // org.tio.sitexxx.web.server.recharge.provider.alipay.AlipayRechargeProvider, org.tio.sitexxx.web.server.recharge.IRechargeProvider
    public HttpResponse toThirdRechargePage(HttpRequest request, RechargeItem rechargeItem, int callbackType) throws Exception {
        Integer paytype = rechargeItem.getPaytype();
        String _subject = rechargeItem.getGoods();
        if (StrUtil.isBlank(_subject)) {
            _subject = "充值";
        }
        String total_fee = String.valueOf(rechargeItem.getAmount());
        AlipayClient alipayClient = AlipayConfig.alipayClient;
        AlipayTradeWapPayRequest alipay_request = new AlipayTradeWapPayRequest();
        AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
        model.setOutTradeNo(rechargeItem.getTradeno());
        model.setSubject(_subject);
        model.setTotalAmount(total_fee);
        model.setBody("");
        model.setTimeoutExpress("30m");
        model.setProductCode("QUICK_WAP_WAY");
        alipay_request.setBizModel(model);
        alipay_request.setNotifyUrl(RechargeUtils.getNotifyUrl(paytype, request));
        alipay_request.setReturnUrl(RechargeUtils.getReturnUrl(paytype, request));
        try {
            String form = alipayClient.pageExecute(alipay_request).getBody();
            log.info("支付宝生成的h5页面\r\n{}", form);
            return Resps.html(request, form);
        } catch (AlipayApiException e) {
            log.error("支付宝支付生成H5支付页面时产生异常", e);
            throw e;
        }
    }
}
