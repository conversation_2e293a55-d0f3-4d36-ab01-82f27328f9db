package org.tio.sitexxx.web.server.utils;

import java.util.Date;
import org.tio.http.common.HttpRequest;
import org.tio.sitexxx.service.model.stat.TioIpPullblackLog;
import org.tio.sitexxx.service.service.base.IpInfoService;
import org.tio.sitexxx.service.service.base.TioIpPullblackLogService;
import org.tio.sitexxx.service.service.conf.IpBlackListService;
import org.tio.sitexxx.servicecommon.vo.Const;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/utils/TioIpPullblackUtils.class */
public class TioIpPullblackUtils {
    public static void addToBlack(HttpRequest request, String ip, String remark, byte type) {
        Integer currId = WebUtils.currUserId(request);
        TioIpPullblackLog tioIpPullblackLog = new TioIpPullblackLog();
        tioIpPullblackLog.setIp(ip);
        tioIpPullblackLog.setIpid(IpInfoService.ME.save(request.getClientIp()).getId());
        tioIpPullblackLog.setRemark(remark);
        tioIpPullblackLog.setServer(Const.MY_IP_API);
        tioIpPullblackLog.setServerport(Integer.valueOf(request.getChannelContext().getServerNode().getPort()));
        tioIpPullblackLog.setTime(new Date());
        tioIpPullblackLog.setType(Byte.valueOf(type));
        tioIpPullblackLog.setSessionid(request.getHttpSession().getId());
        tioIpPullblackLog.setCookie(request.getHeader("cookie"));
        tioIpPullblackLog.setInitpath(request.requestLine.getInitPath());
        tioIpPullblackLog.setPath(request.requestLine.getPath());
        tioIpPullblackLog.setRequestline(request.requestLine.toString());
        tioIpPullblackLog.setUid(currId);
        TioIpPullblackLogService.ME.addToBlack(tioIpPullblackLog);
        IpBlackListService.me.save(ip, remark);
    }

    public static void main(String[] args) {
    }
}
