package org.tio.sitexxx.web.server.http;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.Cookie;
import org.tio.http.common.HeaderName;
import org.tio.http.common.HeaderValue;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.common.HttpResponseStatus;
import org.tio.http.common.MimeType;
import org.tio.http.common.RequestLine;
import org.tio.http.common.session.HttpSession;
import org.tio.http.common.utils.HttpGzipUtils;
import org.tio.http.server.intf.HttpServerInterceptor;
import org.tio.http.server.util.Resps;
import org.tio.sitexxx.service.cache.CacheConfig;
import org.tio.sitexxx.service.cache.Caches;
import org.tio.sitexxx.service.model.conf.Httpcache;
import org.tio.sitexxx.service.model.main.IpInfo;
import org.tio.sitexxx.service.model.main.LoginLog;
import org.tio.sitexxx.service.model.main.RechargeItem;
import org.tio.sitexxx.service.model.main.User;
import org.tio.sitexxx.service.model.main.UserAgent;
import org.tio.sitexxx.service.model.stat.ImeiStat;
import org.tio.sitexxx.service.model.stat.TioSlowRequest;
import org.tio.sitexxx.service.service.base.ImeiStatService;
import org.tio.sitexxx.service.service.base.IpInfoService;
import org.tio.sitexxx.service.service.base.UserAgentService;
import org.tio.sitexxx.service.service.conf.ConfService;
import org.tio.sitexxx.service.service.conf.HttpcacheService;
import org.tio.sitexxx.service.service.conf.IpWhiteListService;
import org.tio.sitexxx.service.vo.RequestExt;
import org.tio.sitexxx.service.vo.SessionExt;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.sitexxx.servicecommon.vo.Devicetype;
import org.tio.sitexxx.servicecommon.vo.MulLauUtils;
import org.tio.sitexxx.web.server.auth.AccessCtrlConfig;
import org.tio.sitexxx.web.server.auth.AccessCtrlService;
import org.tio.sitexxx.web.server.recharge.provider.alipay.AlipayConfig;
import org.tio.sitexxx.web.server.utils.TioIpPullblackUtils;
import org.tio.sitexxx.web.server.utils.WebUtils;
import org.tio.sitexxx.web.server.yanxun.youmeng.http.constant.Constants;
import org.tio.utils.SystemTimer;
import org.tio.utils.cache.ICache;
import org.tio.utils.jfinal.P;
import org.tio.utils.json.Json;
import org.tio.utils.lock.LockUtils;
import org.tio.utils.resp.Resp;

/* loaded from: tio-site-http-server-api-1.0.0-tio-sitexxx.jar:org/tio/sitexxx/web/server/http/WebApiHttpServerInterceptor.class */
public class WebApiHttpServerInterceptor implements HttpServerInterceptor {
    public static final String HEADER_NAME_MOBILE_DEVICEINFO = "tio-deviceinfo";
    public static final String HEADER_NAME_TIO_APPVERSION = "tio-appversion";
    public static final String HEADER_NAME_TIO_CID = "tio-cid";
    public static final String HEADER_NAME_TIO_RESOLUTION = "tio-resolution";
    public static final String HEADER_NAME_TIO_IMEI = "tio-imei";
    public static final String HEADER_NAME_TIO_OPERATOR = "tio-operator";
    public static final String HEADER_NAME_TIO_SIZE = "tio-size";
    public static final String HEADER_NAME_TIO_IDFA = "tio-idfa";
    private static final String PARAM_NAME_IS_FROM_ANDROID = "p_is_android";
    private static final String PARAM_NAME_IS_FROM_IOS = "p_is_ios";
    private AccessCtrlConfig accessCtrlConfig;
    private static byte[] BODY_BYTES_NEED_ACCESS_TOKEN;
    private static boolean useHttpcache;
    private static Logger log = LoggerFactory.getLogger(WebApiHttpServerInterceptor.class);

    /* renamed from: ME */
    public static final WebApiHttpServerInterceptor f18ME = new WebApiHttpServerInterceptor();
    private static final HeaderName HTTPCACHE_FLAG_HEADER_NAME = HeaderName.from("tio-httpcache-old");
    private static final HeaderName HTTPCACHE_FIRST_HEADER_NAME = HeaderName.from("tio-httpcache-new");
    private static final HeaderName HEADER_NAME_WEBAPI_SERVER = HeaderName.from("tio-webapi-server");
    private static final HeaderValue HEADER_VALUE_WHICH_API = HeaderValue.from(Const.MY_IP_API);
    private static final Object lockForGetLock = new Object();
    private static Set<String> skipCheckAccessTokenPathSet = new HashSet();
    private static Set<String> neededCheckAccessTokenPathSet = new HashSet();
    private static String[] skipCheckAccessTokenPathprefix = {"/open/lastVersion1", "/open/lastVersion2", "/recharge/nf/", "/recharge/rt/", "/recharge/qrSubmit/", "/tlogin/cb/p/", "/upload/video", "/upload/img", "/upload/all", "/config/query"};
    private final String httpCacheLockKey = getClass().getName() + ".httpCacheLockKey";
    final String PAYTYPE_APPLE_APP = RechargeItem.Paytype.APPLE_APP + "";

    static {
        BODY_BYTES_NEED_ACCESS_TOKEN = null;
        neededCheckAccessTokenPathSet.add("/recharge/nf/" + RechargeItem.Paytype.APPLE_APP);
        skipCheckAccessTokenPathSet.add("/a/x");
        skipCheckAccessTokenPathSet.add("/a/y");
        skipCheckAccessTokenPathSet.add("/wechat/service");
        skipCheckAccessTokenPathSet.add("/tlogin/");
        skipCheckAccessTokenPathSet.add("/blog/uploadimg");
        skipCheckAccessTokenPathSet.add("/ad/redirect");
        skipCheckAccessTokenPathSet.add("/blog/save");
        skipCheckAccessTokenPathSet.add("/test/checkStr");
        Resp resp = Resp.fail().code(1006);
        String xx = Json.toJson(resp);
        try {
            BODY_BYTES_NEED_ACCESS_TOKEN = xx.getBytes(AlipayConfig.charset);
        } catch (UnsupportedEncodingException e) {
            log.error("", e);
        }
        useHttpcache = P.getInt("web.api.use.http.cache", 1).intValue() == 1;
    }

    public static void main(String[] args) {
    }

    private WebApiHttpServerInterceptor() {
    }

    private static boolean needCheckAccessToken(HttpRequest request, String path) {
        if (neededCheckAccessTokenPathSet.contains(path)) {
            return true;
        }
        if (skipCheckAccessTokenPathSet.contains(path)) {
            return false;
        }
        for (String pathPrefix : skipCheckAccessTokenPathprefix) {
            if (StrUtil.startWith(path, pathPrefix)) {
                return false;
            }
        }
        return true;
    }

    private static ImeiStat createImeiStat(HttpRequest request, RequestExt requestExt) {
        String ip = request.getClientIp();
        IpInfo ipInfo = IpInfoService.ME.save(ip);
        ImeiStat imeiStat = new ImeiStat();
        imeiStat.setAppversion(requestExt.getAppVersion());
        imeiStat.setCid(requestExt.getCid());
        imeiStat.setDeviceinfo(requestExt.getDeviceinfo());
        imeiStat.setImei(requestExt.getImei());
        imeiStat.setIp(ip);
        imeiStat.setIpid(ipInfo.getId());
        imeiStat.setResolution(requestExt.getResolution());
        imeiStat.setSize(requestExt.getSize());
        imeiStat.setTime(new Date());
        imeiStat.setType(Byte.valueOf(requestExt.getDeviceType()));
        imeiStat.setUrl(request.getRequestLine().getPath());
        imeiStat.setIdfa(requestExt.getIdfa());
        return imeiStat;
    }

    public HttpResponse doBeforeHandler(HttpRequest request, RequestLine requestLine, HttpResponse httpResponseFromCache) throws Exception {
        RequestExt requestExt = new RequestExt();
        request.setAttribute("TIO_SITE_REQUESTEXT", requestExt);
        requestExt.setCanCache(false);
        String path = requestLine.getPath();
        boolean isFromAndroid = "1".equals(request.getParam(PARAM_NAME_IS_FROM_ANDROID));
        boolean isFromIos = "1".equals(request.getParam(PARAM_NAME_IS_FROM_IOS));
        boolean isFromApp = isFromIos || isFromAndroid;
        boolean isFromPc = true;
        String clientTypeName = "Browser";
        if (isFromApp) {
            isFromPc = false;
            if (isFromIos) {
                clientTypeName = "IOS";
            } else {
                clientTypeName = "Android";
            }
        }
        int accessTokenOn = 2;
        if (isFromPc) {
            accessTokenOn = ConfService.getInt("use.access.token.pc", 2).intValue();
        } else if (isFromAndroid) {
            accessTokenOn = ConfService.getInt("use.access.token.android", 2).intValue();
        } else if (isFromIos) {
            accessTokenOn = ConfService.getInt("use.access.token.ios", 2).intValue();
        }
        if (isFromApp) {
            requestExt.setFromApp(true);
            requestExt.setFromBrowser(false);
            requestExt.setFromBrowserPc(false);
            requestExt.setFromBrowserMobile(false);
            if (isFromIos) {
                requestExt.setFromAppIos(true);
                requestExt.setDeviceType(Devicetype.IOS.getValue().byteValue());
            } else {
                requestExt.setFromAppAndroid(true);
                requestExt.setDeviceType(Devicetype.ANDROID.getValue().byteValue());
            }
            requestExt.setAppDevice(Devicetype.APP.getValue().byteValue());
        }
        IpInfo ipInfo = IpInfoService.ME.save(request.getClientIp());
        requestExt.setIpInfo(ipInfo);
        if (requestExt.isFromApp()) {
            String appVersion = request.getHeader(HEADER_NAME_TIO_APPVERSION);
            requestExt.setAppVersion(appVersion);
            if (StrUtil.isBlank(appVersion)) {
                log.info("{} path:{}, 没有提供App版本号【{}】", new Object[]{clientTypeName, path, HEADER_NAME_TIO_APPVERSION});
            }
            String cid = request.getHeader(HEADER_NAME_TIO_CID);
            requestExt.setCid(cid);
            if (StrUtil.isBlank(cid)) {
                log.info("{} {}, path:{}, 没有提供渠道号【{}】", new Object[]{clientTypeName, appVersion, path, HEADER_NAME_TIO_CID});
            }
            String resolution = request.getHeader(HEADER_NAME_TIO_RESOLUTION);
            requestExt.setResolution(resolution);
            if (StrUtil.isBlank(resolution)) {
                log.info("{} {}, path:{}, 没有提供分辨率【{}】", new Object[]{clientTypeName, appVersion, path, HEADER_NAME_TIO_RESOLUTION});
            }
            String imei = request.getHeader(HEADER_NAME_TIO_IMEI);
            requestExt.setImei(imei);
            if (StrUtil.isBlank(imei)) {
                log.info("{} {}, path:{}, 没有提供IMEI【{}】", new Object[]{clientTypeName, appVersion, path, HEADER_NAME_TIO_IMEI});
            }
            String operator = request.getHeader(HEADER_NAME_TIO_OPERATOR);
            requestExt.setOperator(operator);
            if (StrUtil.isBlank(operator)) {
                log.info("{} {}, path:{}, 没有提供运营商【{}】", new Object[]{clientTypeName, appVersion, path, HEADER_NAME_TIO_OPERATOR});
            }
            String deviceinfo = request.getHeader(HEADER_NAME_MOBILE_DEVICEINFO);
            requestExt.setDeviceinfo(deviceinfo);
            if (StrUtil.isBlank(deviceinfo)) {
                log.info("{} {}, path:{}, 没有提供手机信息【{}】", new Object[]{clientTypeName, appVersion, path, HEADER_NAME_MOBILE_DEVICEINFO});
            }
            String size = request.getHeader(HEADER_NAME_TIO_SIZE);
            requestExt.setSize(size);
            if (StrUtil.isBlank(size)) {
                log.info("{} {}, path:{}, 没有提供手机尺寸【{}】", new Object[]{clientTypeName, appVersion, path, HEADER_NAME_TIO_SIZE});
            }
            String idfa = null;
            if (isFromIos) {
                idfa = request.getHeader(HEADER_NAME_TIO_IDFA);
                requestExt.setIdfa(idfa);
                if (StrUtil.isBlank(idfa)) {
                    log.info("{} {}, path:{}, 没有提供idfa【{}】", new Object[]{clientTypeName, appVersion, path, HEADER_NAME_TIO_IDFA});
                }
            }
            if (StrUtil.isNotBlank(imei)) {
                ImeiStat imeiStat = ImeiStatService.me.getByImei(imei);
                if (imeiStat == null) {
                    ImeiStatService.me.save(createImeiStat(request, requestExt));
                } else {
                    String idfaInDb = imeiStat.getIdfa();
                    if (StrUtil.isBlank(idfaInDb) && StrUtil.isNotBlank(idfa)) {
                        ImeiStat newImeiStat = createImeiStat(request, requestExt);
                        newImeiStat.setId(imeiStat.getId());
                        ImeiStatService.me.update(newImeiStat);
                    }
                }
            }
        } else {
            String userAgentStr = request.getUserAgent();
            UserAgent userAgent = UserAgentService.ME.save(userAgentStr);
            requestExt.setUserAgent(userAgent);
            boolean isMobile = UserAgent.isMobile(userAgent);
            requestExt.setFromBrowser(true);
            requestExt.setFromBrowserPc(!isMobile);
            requestExt.setFromBrowserMobile(isMobile);
            if (isMobile) {
                requestExt.setDeviceType(Devicetype.H5.getValue().byteValue());
                requestExt.setAppDevice(Devicetype.H5.getValue().byteValue());
            }
        }
        boolean isWhiteIp = IpWhiteListService.isWhiteIp(request.getClientIp());
        if (!request.isForward() && !isWhiteIp && accessTokenOn == 1) {
            if (needCheckAccessToken(request, path)) {
                Cookie cookie = request.getCookie("tio_access_token");
                boolean needNewAccessToken = true;
                if (cookie != null) {
                    String value = cookie.getValue();
                    if (!StrUtil.isBlank(value)) {
                        ICache cache2 = Caches.getCache(CacheConfig.TIO_ACCESS_TOKEN);
                        String valueInCache = (String) cache2.get(request.getHttpSession().getId(), String.class);
                        if (Objects.equals(value, valueInCache)) {
                            needNewAccessToken = false;
                        }
                    }
                }
                if (needNewAccessToken) {
                    HttpResponse ret = Resps.bytesWithContentType(request, BODY_BYTES_NEED_ACCESS_TOKEN, MimeType.TEXT_PLAIN_JSON.getType());
                    return ret;
                }
            }
            if (needClearAccessToken(path, request)) {
                ICache cache22 = Caches.getCache(CacheConfig.TIO_ACCESS_TOKEN);
                cache22.remove(request.getHttpSession().getId());
            }
        }
        String pageSize = request.getParam("pageSize");
        if (StrUtil.isNotBlank(pageSize)) {
            String remark = null;
            try {
                int _pageSize = Integer.parseInt(pageSize);
                if (_pageSize > 1000) {
                    remark = "pageSize参数值[" + _pageSize + "]过大，被认为是攻击";
                }
            } catch (NumberFormatException e) {
                remark = "pageSize参数值[" + pageSize + "]不是数字，被认为是攻击";
            }
            if (remark != null) {
                TioIpPullblackUtils.addToBlack(request, request.getClientIp(), remark, (byte) 2);
                request.close(remark);
                return null;
            }
        }
        HttpSession session = request.getHttpSession();
        User user = WebUtils.currUser(request);
        Integer userid = null;
        if (user != null) {
            userid = user.getId();
        }
        boolean b = AccessCtrlService.canAccess(this.accessCtrlConfig, userid, path);
        if (!b) {
            if (user != null) {
                Resp resp = Resp.fail("没权限访问").code(1004);
                return Resps.json(request, resp);
            }
            SessionExt sessionExt = WebUtils.getSessionExt(session);
            LoginLog loginLog = sessionExt.getKickedInfo();
            if (loginLog != null) {
                String ip = loginLog.getIp();
                Date time = loginLog.getTime();
                String msg = "异地登录，您的帐号于" + DateUtil.formatDateTime(time) + "在" + ip + "登录过";
                String deviceinfo2 = loginLog.getDeviceinfo();
                if (StrUtil.isNotBlank(deviceinfo2)) {
                    msg = msg + "，登录设备【" + deviceinfo2 + "】";
                }
                Resp resp2 = Resp.fail(msg).code(1003);
                return Resps.json(request, resp2);
            }
            Resp resp3 = Resp.fail("您尚未登录或登录超时").code(1001);
            return Resps.json(request, resp3);
        }
        requestExt.setCanCache(true);
        HttpResponse httpResponse = doHttpCacheOnBeforeHandler(request, requestExt, path, this.httpCacheLockKey, useHttpcache);
        return httpResponse;
    }

    public static HttpResponse doHttpCacheOnBeforeHandler(HttpRequest request, RequestExt requestExt, String path, String httpCacheLockKey, boolean useHttpcache2) throws Exception {
        Httpcache httpcache;
        ICache cache = null;
        if (useHttpcache2) {
            cache = HttpcacheService.getCache(path);
        }
        if (useHttpcache2 && cache != null && (httpcache = HttpcacheService.get(path)) != null) {
            String cacheKey = getHttpcacheKey(request, cache, httpcache);
            HttpResponse httpResponse = (HttpResponse) cache.get(cacheKey, HttpResponse.class);
            if (httpResponse != null) {
                return cloneAnd304(request, requestExt, httpResponse);
            }
            ReentrantReadWriteLock lock = LockUtils.getReentrantReadWriteLock(cacheKey, lockForGetLock);
            ReentrantReadWriteLock.WriteLock writeLock = lock.writeLock();
            boolean tryWrite = writeLock.tryLock();
            if (tryWrite) {
                request.setAttribute(httpCacheLockKey, writeLock);
                HttpResponse httpResponse2 = (HttpResponse) cache.get(cacheKey, HttpResponse.class);
                if (httpResponse2 != null) {
                    return cloneAnd304(request, requestExt, httpResponse2);
                }
                return null;
            }
            ReentrantReadWriteLock.ReadLock readLock = lock.readLock();
            boolean tryRead = readLock.tryLock(10L, TimeUnit.SECONDS);
            if (tryRead) {
                request.setAttribute(httpCacheLockKey, readLock);
                HttpResponse httpResponse3 = (HttpResponse) cache.get(cacheKey, HttpResponse.class);
                if (httpResponse3 != null) {
                    return cloneAnd304(request, requestExt, httpResponse3);
                }
                return null;
            }
            return null;
        }
        return null;
    }

    private static HttpResponse cloneAnd304(HttpRequest request, RequestExt requestExt, HttpResponse httpResponse) throws NumberFormatException {
        HttpResponse clone = HttpResponse.cloneResponse(request, httpResponse);
        requestExt.setFromCache(true);
        HeaderValue lastModified = clone.getLastModified();
        if (lastModified != null) {
            try {
                long _lastModified = Long.parseLong(lastModified.value);
                HttpResponse r304 = Resps.try304(request, _lastModified);
                if (r304 != null) {
                    r304.addHeader(HTTPCACHE_FLAG_HEADER_NAME, clone.getHeader(HTTPCACHE_FLAG_HEADER_NAME));
                    return r304;
                }
            } catch (NumberFormatException e) {
                return clone;
            }
        }
        return clone;
    }

    private boolean needClearAccessToken(String path, HttpRequest request) {
        if ("/recharge".equals(path)) {
            String paytype = request.getParam("paytype");
            if (StrUtil.isNotBlank(paytype) && this.PAYTYPE_APPLE_APP.equals(paytype)) {
                return true;
            }
            return false;
        }
        return false;
    }

    public void doAfterHandler(HttpRequest request, RequestLine requestLine, HttpResponse response, long cost) throws Exception {
        RequestExt requestExt = WebUtils.getRequestExt(request);
        try {
            try {
                String body = new String(response.getBody());
                JSONObject jsonObject = JSONObject.parseObject(body);
                String msg = jsonObject.getString("msg");
                if (msg != null) {
                    String msgTmp = MulLauUtils.getMsg(msg);
                    if (StrUtil.isNotBlank(msgTmp)) {
                        jsonObject.put("msg", msgTmp);
                        response.setBody(jsonObject.toJSONString().getBytes(AlipayConfig.charset));
                    }
                } else {
                    Object o = jsonObject.getOrDefault("data", (Object) null);
                    if (o != null && (o instanceof String)) {
                        String m = (String) o;
                        String msgTmp2 = MulLauUtils.getMsg(m);
                        if (StrUtil.isNotBlank(msgTmp2)) {
                            jsonObject.put("data", msgTmp2);
                            response.setBody(jsonObject.toJSONString().getBytes(AlipayConfig.charset));
                        }
                    }
                }
                response.addHeader(HeaderName.Access_Control_Allow_Credentials, HeaderValue.TRUE);
                String origin = request.getHeader("origin") == null ? "*" : request.getHeader("origin");
                response.addHeader(HeaderName.Access_Control_Allow_Origin, HeaderValue.from(origin));
                doHttpCacheOnAfterHandler(response, request, requestExt, requestLine.path, useHttpcache, this.httpCacheLockKey);
                saveSlowRequest(request, requestLine, response, cost, (byte) 1);
            } catch (Exception e) {
                log.error("", e);
                response.addHeader(HeaderName.Access_Control_Allow_Credentials, HeaderValue.TRUE);
                String origin2 = request.getHeader("origin") == null ? "*" : request.getHeader("origin");
                response.addHeader(HeaderName.Access_Control_Allow_Origin, HeaderValue.from(origin2));
                doHttpCacheOnAfterHandler(response, request, requestExt, requestLine.path, useHttpcache, this.httpCacheLockKey);
                saveSlowRequest(request, requestLine, response, cost, (byte) 1);
            }
        } catch (Throwable th) {
            response.addHeader(HeaderName.Access_Control_Allow_Credentials, HeaderValue.TRUE);
            String origin3 = request.getHeader("origin") == null ? "*" : request.getHeader("origin");
            response.addHeader(HeaderName.Access_Control_Allow_Origin, HeaderValue.from(origin3));
            doHttpCacheOnAfterHandler(response, request, requestExt, requestLine.path, useHttpcache, this.httpCacheLockKey);
            saveSlowRequest(request, requestLine, response, cost, (byte) 1);
            throw th;
        }
    }

    public static HttpResponse doHttpCacheOnAfterHandler(HttpResponse response, HttpRequest request, RequestExt requestExt, String path, boolean useHttpcache2, String httpCacheLockKey) {
        Httpcache httpcache;
        String cacheKey = null;
        try {
            try {
            } catch (Exception e) {
                log.error("", e);
                try {
                    Lock lock = (Lock) request.getAttribute(httpCacheLockKey);
                    if (lock != null) {
                        if (path == null) {
                            path = request.requestLine.getPath();
                        }
                        log.info("httpcache释放锁【{}】, 这是正常日志. path:【{}】, cacheKey:{}", new Object[]{lock.getClass().getName(), path, cacheKey});
                        lock.unlock();
                    }
                } catch (Exception e2) {
                    log.error(request.requestLine.toString(), e2);
                }
            }
            if (requestExt.isFromCache()) {
                try {
                    Lock lock2 = (Lock) request.getAttribute(httpCacheLockKey);
                    if (lock2 != null) {
                        if (path == null) {
                            path = request.requestLine.getPath();
                        }
                        log.info("httpcache释放锁【{}】, 这是正常日志. path:【{}】, cacheKey:{}", new Object[]{lock2.getClass().getName(), path, null});
                        lock2.unlock();
                    }
                } catch (Exception e3) {
                    log.error(request.requestLine.toString(), e3);
                }
                return response;
            }
            ICache cache = null;
            if (useHttpcache2) {
                cache = HttpcacheService.getCache(path);
            }
            if (!useHttpcache2 || cache == null || (httpcache = HttpcacheService.get(path)) == null || response == null || response.getStatus() != HttpResponseStatus.C200 || !requestExt.isCanCache()) {
                try {
                    Lock lock3 = (Lock) request.getAttribute(httpCacheLockKey);
                    if (lock3 != null) {
                        if (path == null) {
                            path = request.requestLine.getPath();
                        }
                        log.info("httpcache释放锁【{}】, 这是正常日志. path:【{}】, cacheKey:{}", new Object[]{lock3.getClass().getName(), path, null});
                        lock3.unlock();
                    }
                } catch (Exception e4) {
                    log.error(request.requestLine.toString(), e4);
                }
                return response;
            }
            cacheKey = getHttpcacheKey(request, cache, httpcache);
            HeaderValue headerValueCacheKey = HeaderValue.from(cacheKey);
            HeaderValue lastModified = HeaderValue.from(SystemTimer.currTime + "");
            response.setLastModified(lastModified);
            HttpGzipUtils.gzip(request, response);
            HttpResponse responseForCache = HttpResponse.cloneResponse(request, response);
            responseForCache.addHeader(HTTPCACHE_FLAG_HEADER_NAME, headerValueCacheKey);
            cache.put(cacheKey, responseForCache);
            response.addHeader(HTTPCACHE_FIRST_HEADER_NAME, headerValueCacheKey);
            response.addHeader(HEADER_NAME_WEBAPI_SERVER, HEADER_VALUE_WHICH_API);
            try {
                Lock lock4 = (Lock) request.getAttribute(httpCacheLockKey);
                if (lock4 != null) {
                    if (path == null) {
                        path = request.requestLine.getPath();
                    }
                    log.info("httpcache释放锁【{}】, 这是正常日志. path:【{}】, cacheKey:{}", new Object[]{lock4.getClass().getName(), path, cacheKey});
                    lock4.unlock();
                }
            } catch (Exception e5) {
                log.error(request.requestLine.toString(), e5);
            }
            return response;
        } catch (Throwable th) {
            try {
                Lock lock5 = (Lock) request.getAttribute(httpCacheLockKey);
                if (lock5 != null) {
                    if (path == null) {
                        path = request.requestLine.getPath();
                    }
                    log.info("httpcache释放锁【{}】, 这是正常日志. path:【{}】, cacheKey:{}", new Object[]{lock5.getClass().getName(), path, cacheKey});
                    lock5.unlock();
                }
            } catch (Exception e6) {
                log.error(request.requestLine.toString(), e6);
            }
            throw th;
        }
    }

    public static void saveSlowRequest(HttpRequest request, RequestLine requestLine, HttpResponse response, long cost, byte type) {
        int slow_request_cost = ConfService.getInt("slow_request_cost", 2000).intValue();
        if (cost >= slow_request_cost) {
            try {
                Date endtime = new Date();
                Date starttime = new Date(endtime.getTime() - cost);
                Integer uid = WebUtils.currUserId(request);
                String path = requestLine.getPathAndQuery();
                TioSlowRequest tioSlowRequest = new TioSlowRequest();
                tioSlowRequest.setType(Byte.valueOf(type));
                tioSlowRequest.setCost(Long.valueOf(cost));
                tioSlowRequest.setPath(path);
                if (!Objects.equals("/register/submit", path)) {
                    tioSlowRequest.setBody(StrUtil.subPre(request.getBodyString(), 1024));
                }
                tioSlowRequest.setEndtime(endtime);
                tioSlowRequest.setStarttime(starttime);
                tioSlowRequest.setUid(uid);
                tioSlowRequest.setSession(request.getHttpSession().getId());
                tioSlowRequest.save();
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    private static String getHttpcacheKey(HttpRequest request, ICache cache, Httpcache httpcache) {
        Integer currUid = WebUtils.currUserId(request);
        Map<String, Object> params = null;
        String[] paramArray = httpcache.getParamArray();
        if (paramArray != null && paramArray.length > 0) {
            params = new HashMap<>();
            for (String name : paramArray) {
                String value = request.getParam(name);
                params.put(name, value);
            }
        }
        return getHttpcacheKey(currUid, params, cache, httpcache);
    }

    public static String getHttpcacheKey(Integer currUid, Map<String, Object> params, ICache cache, Httpcache httpcache) {
        String[] paramArray = httpcache.getParamArray();
        boolean isUseUidAsKey = httpcache.isUseUidAsKey();
        boolean isUseLoginedAsKey = httpcache.isUseLoginedAsKey();
        StringBuilder key = new StringBuilder(30);
        if (isUseUidAsKey && currUid != null) {
            key.append("u{").append(currUid).append("}");
        }
        if (isUseLoginedAsKey) {
            if (currUid != null) {
                key.append("l{1}");
            } else {
                key.append("l{0}");
            }
        }
        if (paramArray != null && params != null) {
            key.append("p{");
            for (String name : paramArray) {
                Object value = params.get(name);
                if (value != null) {
                    key.append(name).append(Constants.SPE4).append(value).append(Constants.SPE3);
                }
            }
            key.append("}");
        }
        if (key.length() == 0) {
            return "t-io";
        }
        return key.toString();
    }

    public static void removeHttpcache(String path, Map<String, Object> params, Integer currUid) {
        Httpcache httpcache;
        ICache cache = HttpcacheService.getCache(path);
        if (cache != null && (httpcache = HttpcacheService.get(path)) != null) {
            if (httpcache.isHasPageNumber()) {
                if (params == null) {
                    params = new HashMap();
                }
                for (int i = 0; i < 15; i++) {
                    params.put("pageNumber", Integer.valueOf(i));
                    String cacheKey = getHttpcacheKey(currUid, params, cache, httpcache);
                    cache.remove(cacheKey);
                }
                return;
            }
            String cacheKey2 = getHttpcacheKey(currUid, params, cache, httpcache);
            cache.remove(cacheKey2);
        }
    }

    public static void clearHttpcache(String path) {
        ICache cache = HttpcacheService.getCache(path);
        if (cache != null) {
            cache.clear();
        }
    }

    public AccessCtrlConfig getAccessCtrlConfig() {
        return this.accessCtrlConfig;
    }

    public void setAccessCtrlConfig(AccessCtrlConfig accessCtrlConfig) {
        this.accessCtrlConfig = accessCtrlConfig;
    }
}
